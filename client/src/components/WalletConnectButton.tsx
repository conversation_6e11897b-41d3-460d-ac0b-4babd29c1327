import { ConnectButton } from "thirdweb/react";
import { createThirdwebClient } from "thirdweb";
import { createWallet } from "thirdweb/wallets";
import { ethereum, polygon, sepolia, polygonAmoy } from "thirdweb/chains";
import { toast } from "@/hooks/use-toast";

// Create the Thirdweb client
const client = createThirdwebClient({
  clientId: import.meta.env.VITE_THIRDWEB_CLIENT_ID as string,
});

// Define supported wallets
const wallets = [
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  createWallet("walletConnect"),
];

// Define supported chains (including testnet chains as per user preference)
const chains = [ethereum, polygon, sepolia, polygonAmoy];

/**
 * Enhanced Wallet Connect Button Component
 * Uses Thirdweb v5 ConnectButton with built-in wallet management features
 * Includes Send, Receive, Buy, View Assets, and Manage Wallet functionality
 */
const WalletConnectButton = () => {
  // Handle connection events
  const handleConnect = (wallet: any) => {
    // toast({
    //   title: "Wallet connected",
    //   description: `Successfully connected with ${wallet.id}`,
    // });
  };

  const handleDisconnect = () => {
    toast({
      title: "Wallet disconnected",
      description: "Your wallet has been disconnected",
    });
  };

  return (
    <ConnectButton
      client={client}
      wallets={wallets}
      chains={chains}
      theme="dark"
      onConnect={handleConnect}
      onDisconnect={handleDisconnect}
      connectButton={{
        label: "Connect Wallet",
        className: "w-full nebula-action-button text-foreground border-none",
      }}
      connectModal={{
        title: "Connect to Nebula",
        size: "compact",
      }}
      detailsButton={{
        className: "w-full",
      }}
      detailsModal={{
        assetTabs: ["token", "nft"],
      }}
      supportedTokens={{
        [ethereum.id]: [
          {
            address: "******************************************", // Example USDC
            name: "USD Coin",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [polygon.id]: [
          {
            address: "******************************************", // USDC on Polygon
            name: "USD Coin",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [sepolia.id]: [
          {
            address: "******************************************", // Example test token
            name: "Test USDC",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [polygonAmoy.id]: [
          {
            address: "******************************************", // Example test token
            name: "Test USDC",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
      }}
    />
  );
};

export default WalletConnectButton;
