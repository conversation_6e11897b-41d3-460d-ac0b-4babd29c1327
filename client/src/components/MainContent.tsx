import { useState, useEffect, useRef } from "react";

import ChatMessage from "@/components/ChatMessage";
import MessageInput from "@/components/MessageInput";
import SuggestionChips from "@/components/SuggestionChips";

import { CogIcon, HelpCircleIcon, Bot, PlusIcon } from "lucide-react";
import { useActiveAccount, useActiveWalletChain } from "thirdweb/react";
import { getChainById } from "@/lib/chainConfig";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { useNebulaChat } from "@/hooks/use-nebula-chat";

const MainContent = () => {
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const isSendingRef = useRef(false);

  // Helper function to scroll to bottom smoothly
  const scrollToBottom = (smooth: boolean = false) => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
    }
  };

  // Use our custom Nebula chat hook
  const {
    activeChat,
    messages,
    isLoading,
    sendStreamingMessage,
    createChat,
    setActiveChat,
  } = useNebulaChat();

  // ThirdWeb v5 hooks
  const account = useActiveAccount();
  const activeChain = useActiveWalletChain();

  // Extract values from v5 hooks
  const address = account?.address;
  const chainId = activeChain?.id;

  // Get current chainId (default to Ethereum if not connected)
  const currentChainId = chainId ? chainId.toString() : "1";
  const currentChain = getChainById(currentChainId);

  // Check if this is first load (no active chat)
  const [isFirstLoad, setIsFirstLoad] = useState(!activeChat);

  // Update isFirstLoad when activeChat changes
  useEffect(() => {
    setIsFirstLoad(!activeChat);
  }, [activeChat]);

  // Handle chain changes
  useEffect(() => {
    if (activeChain && activeChat) {
      // Show toast notification on network change
      toast({
        title: "Network Changed",
        description: `Switched to ${currentChain?.name || "Unknown Network"}`,
        variant: "default",
      });
    }
  }, [activeChain?.id, activeChat, currentChain?.name]);

  // Handle creating a new chat - show fresh chat UI without creating backend chat
  const handleCreateChat = async () => {
    if (!address) {
      toast({
        title: "Wallet Required",
        description: "Please connect your wallet to create a new chat",
        variant: "destructive",
      });
      return;
    }

    // Clear active chat to show fresh UI - chat will be created when user sends first message
    setActiveChat(null);
    setIsFirstLoad(false);
  };

  // Handle send message with streaming
  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Prevent duplicate calls using ref
    if (isSendingRef.current || isLoading.sendMessage) {
      console.log("Already sending message, ignoring duplicate call");
      return;
    }

    if (!address) {
      toast({
        title: "Wallet Required",
        description: "Please connect your wallet to start chatting",
        variant: "destructive",
      });
      return;
    }

    // Set flag to prevent duplicate calls
    isSendingRef.current = true;

    try {
      setIsFirstLoad(false); // Switch to chat view immediately

      // Immediately scroll to bottom when user sends message
      scrollToBottom();

      // If we don't have an active chat, create one first
      if (!activeChat) {
        const newChat = await createChat("New Blockchain Chat");
        if (newChat) {
          // Now send the streaming message to the new chat using the chat ID
          await sendStreamingMessage(content, newChat.id, (_chunk) => {
            // Auto-scroll as content streams in
            setTimeout(() => scrollToBottom(true), 50);
          });
        }
      } else {
        await sendStreamingMessage(content, undefined, (_chunk) => {
          // Auto-scroll as content streams in
          setTimeout(() => scrollToBottom(true), 50);
        });
      }
    } finally {
      // Reset flag when done
      isSendingRef.current = false;
    }
  };

  // Scroll to bottom of chat when messages change
  useEffect(() => {
    if (messages) {
      scrollToBottom();
    }
  }, [messages]);

  // Scroll to bottom immediately when user sends a message (loading starts)
  useEffect(() => {
    if (isLoading.sendMessage) {
      // Small delay to ensure the UI has updated
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [isLoading.sendMessage]);

  return (
    <main className="flex-1 flex flex-col h-screen overflow-hidden bg-background">
      {/* Centered container with 70% width */}
      <div className="w-[70%] mx-auto flex flex-col h-full">
        {/* Chat Area */}
        <div
          ref={chatContainerRef}
          className="flex-1 overflow-y-auto chat-container"
        >
          {/* Initial welcome view */}
          {isFirstLoad && (
            <div className="flex flex-col justify-center items-center h-full">
              <div className="text-center max-w-2xl px-4">
                <div
                  className="w-20 h-20 mx-auto mb-8 flex items-center justify-center rounded-xl"
                  style={{
                    background:
                      "linear-gradient(135deg, hsl(0deg 0% 3.92%), hsl(var(--primary) / 0.3))",
                  }}
                >
                  <Bot className="h-10 w-10 text-foreground" />
                </div>
                <h1 className="text-4xl font-bold mb-12 text-foreground">
                  How can I help you
                  <br />
                  onchain today?
                </h1>

                {/* Wallet Connection Banner (show if not connected) */}

                {/* Query Input for Initial View */}
                <div className="w-full max-w-2xl mx-auto mb-6">
                  <MessageInput
                    onSendMessage={handleSendMessage}
                    compact={false}
                  />
                </div>

                {/* Suggestion Chips */}
                <SuggestionChips onChipClick={handleSendMessage} />
              </div>
            </div>
          )}

          {/* Chat Messages */}
          {!isFirstLoad && messages.length > 0 && (
            <div className="py-6">
              {messages.map((message: any) => (
                <ChatMessage key={message.id} message={message} />
              ))}
            </div>
          )}

          {/* Empty state - no messages yet but chat created */}
          {!isFirstLoad && messages.length === 0 && !isLoading.messages && (
            <div className="flex justify-center items-center h-full">
              <div className="text-center max-w-md px-4">
                <div
                  className="w-12 h-12 mx-auto mb-4 flex items-center justify-center rounded-lg"
                  style={{
                    background:
                      "linear-gradient(135deg, hsl(0deg 0% 3.92%), hsl(var(--primary) / 0.3))",
                  }}
                >
                  <Bot className="h-6 w-6 text-foreground" />
                </div>
                <h3 className="text-xl font-medium mb-2 text-foreground">
                  Start a conversation
                </h3>
                <p className="text-muted-foreground mb-6">
                  Ask a question about blockchain development or Web3
                  functionality.
                </p>
                <div className="py-2">
                  <SuggestionChips onChipClick={handleSendMessage} />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Input Area (only shown when not on first load) */}
        {!isFirstLoad && (
          <div className="p-4">
            {/* Network indicator (only if wallet connected) */}

            <MessageInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading.sendMessage}
            />
          </div>
        )}
      </div>
    </main>
  );
};

export default MainContent;
