@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Theme: Nebula Dark - Matching the reference image */

  /* Main chat area - very dark with subtle warmth */
  --background: 0 0% 4%;
  --foreground: 0 0% 98%;
  --muted: 0 0% 8%;
  --muted-foreground: 0 0% 60%;
  --popover: 0 0% 6%;
  --popover-foreground: 0 0% 98%;
  --card: 0 0% 6%;
  --card-foreground: 0 0% 98%;
  --border: 320 100% 70%;
  --input: 0 0% 8%;
  --primary: 320 100% 70%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 10%;
  --secondary-foreground: 0 0% 98%;
  --accent: 320 100% 70%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 320 100% 70%;
  --radius: 0.5rem;

  /* Sidebar - darker with subtle shade for distinction */
  --sidebar-background: 0 0% 2%;
  --sidebar-foreground: 0 0% 98%;
  --sidebar-primary: 320 100% 70%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 0 0% 5%;
  --sidebar-accent-foreground: 0 0% 98%;
  --sidebar-border: 320 50% 30%;
  --sidebar-ring: 320 100% 70%;

  /* Chart colors - Nebula Dark theme */
  --chart-1: 320 100% 70%;
  --chart-2: 300 80% 65%;
  --chart-3: 340 90% 75%;
  --chart-4: 280 70% 60%;
  --chart-5: 260 85% 65%;

  /* Chain colors - Nebula Dark theme */
  --magenta-500: #ff00ff;
  --purple-500: #a855f7;
  --pink-500: #ec4899;
  --magenta-400: #ff66ff;
  --purple-400: #c084fc;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Hide scrollbar utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Custom scrollbar for chat container */
.chat-container::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.chat-container::-webkit-scrollbar-thumb {
  background: #271122;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: #3f253d;
}

/* Custom scrollbar for sidebar chat list */
.sidebar-chat-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
  position: relative;
}

.sidebar-chat-list::-webkit-scrollbar {
  width: 8px;
}

.sidebar-chat-list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.sidebar-chat-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar-chat-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

.sidebar-chat-list:hover::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.12);
}

/* Fade effect for scrollable content */
.sidebar-chat-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(to bottom, rgba(20, 21, 30, 0.8), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.sidebar-chat-list::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(to top, rgba(20, 21, 30, 0.8), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.sidebar-chat-list.scrollable::before,
.sidebar-chat-list.scrollable::after {
  opacity: 1;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Theme: Nebula Dark - Enhanced UI Components */
.nebula-card {
  @apply bg-card border border-border p-4 backdrop-blur-sm;
  border-radius: var(--radius);
  background: linear-gradient(135deg, rgba(10, 10, 15, 0.95), rgba(15, 15, 20, 0.9));
  border: 1px solid hsl(var(--border) / 0.3);
  box-shadow: 0 8px 32px rgba(255, 0, 255, 0.1), 0 4px 16px rgba(0, 0, 0, 0.5);
}

.nebula-transaction-card {
  @apply nebula-card mb-4;
  border: 1px solid hsl(var(--primary) / 0.3);
}

.nebula-transaction-row {
  @apply flex items-center justify-between py-2 border-b border-border/50 last:border-0;
}

.nebula-transaction-label {
  @apply text-sm text-muted-foreground;
}

.nebula-transaction-value {
  @apply text-sm font-medium text-foreground;
}

.nebula-icon-bg {
  @apply flex items-center justify-center;
  border-radius: var(--radius);
  background: linear-gradient(135deg, hsl(var(--primary) / 0.6), hsl(var(--primary) / 0.3));
  border: 1px solid hsl(var(--primary) / 0.4);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.2);
}

.nebula-action-button {
  @apply flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium transition-all duration-200;
  border-radius: var(--radius);
  background: linear-gradient(135deg, hsl(var(--primary) / 0.4), hsl(var(--primary) / 0.2));
  border: 1px solid hsl(var(--primary) / 0.5);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.2);
}

.nebula-action-button:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.6), hsl(var(--primary) / 0.4));
  border: 1px solid hsl(var(--primary) / 0.7);
  box-shadow: 0 6px 20px hsl(var(--primary) / 0.3);
  transform: translateY(-1px);
}

.nebula-action-button:disabled {
  background: linear-gradient(135deg, hsl(var(--muted) / 0.3), hsl(var(--muted) / 0.2));
  border: 1px solid hsl(var(--border));
  box-shadow: none;
  transform: none;
  opacity: 0.6;
}

.nebula-suggestion-chip {
  @apply px-4 py-2 text-sm font-medium inline-flex items-center gap-2 transition-all duration-200;
  border-radius: var(--radius);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: hsl(var(--foreground) / 0.7);
  backdrop-filter: blur(10px);
}

.nebula-suggestion-chip:hover {
  background: rgba(255, 0, 255, 0.1);
  border: 1px solid hsl(var(--primary) / 0.5);
  color: hsl(var(--foreground));
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.2);
  transform: translateY(-1px);
}

/* Enhanced input styling with theme borders */
.theme-input {
  /* border: 1px solid hsl(var(--border)); */
  border: 1px solid hsl(320deg 12.67% 23.89%);
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.theme-input:focus {
  border-color: hsl(var(--primary) / 0.6);
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1), 0 4px 12px hsl(var(--primary) / 0.15);
  outline: none;
}

/* Enhanced button styling */
.theme-button {
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.theme-button:focus-visible {
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2);
}
