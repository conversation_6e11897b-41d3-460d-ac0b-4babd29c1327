{"version": 3, "sources": ["../../thirdweb/src/insight/common.ts"], "sourcesContent": ["import type { Chain } from \"../chains/types.js\";\nimport { getChainServices } from \"../chains/utils.js\";\n\nexport async function assertInsightEnabled(chains: Chain[]) {\n  const chainData = await Promise.all(\n    chains.map((chain) =>\n      isInsightEnabled(chain).then((enabled) => ({\n        chain,\n        enabled,\n      })),\n    ),\n  );\n\n  const insightEnabled = chainData.every((c) => c.enabled);\n\n  if (!insightEnabled) {\n    throw new Error(\n      `Insight is not available for chains ${chainData\n        .filter((c) => !c.enabled)\n        .map((c) => c.chain.id)\n        .join(\", \")}`,\n    );\n  }\n}\n\nexport async function isInsightEnabled(chain: Chain) {\n  const chainData = await getChainServices(chain);\n  return chainData.some((c) => c.service === \"insight\" && c.enabled);\n}\n"], "mappings": ";;;;;AAGA,eAAsB,qBAAqB,QAAe;AACxD,QAAM,YAAY,MAAM,QAAQ,IAC9B,OAAO,IAAI,CAAC,UACV,iBAAiB,KAAK,EAAE,KAAK,CAAC,aAAa;IACzC;IACA;IACA,CAAC,CACJ;AAGH,QAAM,iBAAiB,UAAU,MAAM,CAAC,MAAM,EAAE,OAAO;AAEvD,MAAI,CAAC,gBAAgB;AACnB,UAAM,IAAI,MACR,uCAAuC,UACpC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EACxB,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,EACrB,KAAK,IAAI,CAAC,EAAE;EAEnB;AACF;AAEA,eAAsB,iBAAiB,OAAY;AACjD,QAAM,YAAY,MAAM,iBAAiB,KAAK;AAC9C,SAAO,UAAU,KAAK,CAAC,MAAM,EAAE,YAAY,aAAa,EAAE,OAAO;AACnE;", "names": []}