{"version": 3, "sources": ["../../thirdweb/src/react/web/ui/ConnectWallet/Modal/ConnectEmbed.tsx", "../../thirdweb/src/react/core/hooks/wallets/useIsAutoConnecting.ts", "../../thirdweb/src/react/core/hooks/transaction/transaction-button-utils.ts", "../../thirdweb/src/react/web/hooks/transaction/useSendTransaction.tsx", "../../thirdweb/src/react/core/hooks/transaction/useSendTransaction.ts", "../../thirdweb/src/react/web/ui/TransactionButton/TransactionModal.tsx", "../../thirdweb/src/react/web/ui/TransactionButton/DepositScreen.tsx", "../../thirdweb/src/react/web/ui/TransactionButton/ExecutingScreen.tsx", "../../thirdweb/src/react/web/ui/TransactionButton/index.tsx", "../../thirdweb/src/react/web/providers/thirdweb-provider.tsx", "../../thirdweb/src/react/core/providers/thirdweb-provider.tsx", "../../thirdweb/src/react/core/utils/structuralSharing.ts", "../../thirdweb/src/react/web/hooks/wallets/useLinkProfile.ts", "../../thirdweb/src/wallets/eip5792/get-capabilities.ts", "../../thirdweb/src/react/core/hooks/wallets/useCapabilities.ts", "../../thirdweb/src/wallets/eip5792/send-calls.ts", "../../thirdweb/src/wallets/eip5792/get-calls-status.ts", "../../thirdweb/src/wallets/eip5792/wait-for-calls-receipt.ts", "../../thirdweb/src/react/core/hooks/wallets/useSendCalls.ts", "../../thirdweb/src/wallets/eip5792/send-and-confirm-calls.ts", "../../thirdweb/src/react/core/hooks/wallets/useSendAndConfirmCalls.ts", "../../thirdweb/src/react/core/hooks/wallets/useWaitForCallsReceipt.ts", "../../thirdweb/src/react/core/hooks/contract/useReadContract.ts", "../../thirdweb/src/react/core/hooks/contract/useContractEvents.ts", "../../thirdweb/src/react/core/hooks/transaction/useSimulateTransaction.ts", "../../thirdweb/src/react/core/hooks/transaction/useSendBatchTransaction.ts", "../../thirdweb/src/react/core/hooks/transaction/useSendAndConfirmTransaction.ts", "../../thirdweb/src/react/core/hooks/transaction/useEstimateGas.ts", "../../thirdweb/src/react/core/hooks/transaction/useEstimateGasCost.ts", "../../thirdweb/src/react/core/hooks/rpc/useBlockNumber.ts", "../../thirdweb/src/react/core/utils/createQuery.ts", "../../thirdweb/src/react/core/hooks/others/useInvalidateQueries.ts", "../../thirdweb/src/react/core/hooks/pay/useBuyWithCryptoHistory.ts", "../../thirdweb/src/pay/buyWithFiat/getHistory.ts", "../../thirdweb/src/react/core/hooks/pay/useBuyWithFiatHistory.ts", "../../thirdweb/src/pay/getBuyHistory.ts", "../../thirdweb/src/react/core/hooks/pay/useBuyHistory.ts", "../../thirdweb/src/pay/buyWithFiat/getPostOnRampQuote.ts", "../../thirdweb/src/react/core/hooks/pay/usePostOnrampQuote.ts", "../../thirdweb/src/react/web/ui/PayEmbed.tsx", "../../thirdweb/src/react/web/ui/ConnectWallet/useConnectModal.tsx", "../../thirdweb/src/react/web/ui/prebuilt/thirdweb/ClaimButton/index.tsx", "../../thirdweb/src/react/web/ui/prebuilt/thirdweb/BuyDirectListingButton/index.tsx", "../../thirdweb/src/react/web/ui/prebuilt/thirdweb/CreateDirectListingButton/index.tsx", "../../thirdweb/src/extensions/marketplace/__generated__/IDirectListings/write/createListing.ts", "../../thirdweb/src/extensions/marketplace/direct-listings/write/createListing.ts", "../../thirdweb/src/react/web/ui/prebuilt/NFT/provider.tsx", "../../thirdweb/src/react/web/ui/prebuilt/NFT/utils.ts", "../../thirdweb/src/react/web/ui/prebuilt/NFT/name.tsx", "../../thirdweb/src/react/web/ui/prebuilt/NFT/description.tsx", "../../thirdweb/src/react/web/ui/prebuilt/NFT/media.tsx", "../../thirdweb/src/react/web/ui/SiteEmbed.tsx", "../../thirdweb/src/react/web/ui/SiteLink.tsx", "../../thirdweb/src/react/web/ui/prebuilt/Token/provider.tsx", "../../thirdweb/src/react/web/ui/prebuilt/Token/name.tsx", "../../thirdweb/src/react/web/ui/prebuilt/Token/symbol.tsx", "../../thirdweb/src/react/web/ui/prebuilt/Token/icon.tsx", "../../thirdweb/src/react/web/utils/storage.ts", "../../thirdweb/src/react/web/ui/prebuilt/Wallet/icon.tsx", "../../thirdweb/src/react/core/utils/walletname.ts", "../../thirdweb/src/react/web/ui/prebuilt/Wallet/name.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useMemo } from \"react\";\nimport type { Chain } from \"../../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../../client/client.js\";\nimport { getDefaultWallets } from \"../../../../../wallets/defaultWallets.js\";\nimport type { Wallet } from \"../../../../../wallets/interfaces/wallet.js\";\nimport type { SmartWalletOptions } from \"../../../../../wallets/smart/types.js\";\nimport {\n  CustomThemeProvider,\n  useCustomTheme,\n} from \"../../../../core/design-system/CustomThemeProvider.js\";\nimport { radius } from \"../../../../core/design-system/index.js\";\nimport {\n  type SiweAuthOptions,\n  useSiweAuth,\n} from \"../../../../core/hooks/auth/useSiweAuth.js\";\nimport type { ConnectEmbedProps } from \"../../../../core/hooks/connection/ConnectEmbedProps.js\";\nimport { useActiveAccount } from \"../../../../core/hooks/wallets/useActiveAccount.js\";\nimport { useActiveWallet } from \"../../../../core/hooks/wallets/useActiveWallet.js\";\nimport { useIsAutoConnecting } from \"../../../../core/hooks/wallets/useIsAutoConnecting.js\";\nimport { useConnectionManager } from \"../../../../core/providers/connection-manager.js\";\nimport { WalletUIStatesProvider } from \"../../../providers/wallet-ui-states-provider.js\";\nimport { canFitWideModal } from \"../../../utils/canFitWideModal.js\";\nimport { usePreloadWalletProviders } from \"../../../utils/usePreloadWalletProviders.js\";\nimport { LoadingScreen } from \"../../../wallets/shared/LoadingScreen.js\";\nimport { AutoConnect } from \"../../AutoConnect/AutoConnect.js\";\nimport { DynamicHeight } from \"../../components/DynamicHeight.js\";\nimport { StyledDiv } from \"../../design-system/elements.js\";\nimport type { LocaleId } from \"../../types.js\";\nimport {\n  modalMaxWidthCompact,\n  modalMaxWidthWide,\n  reservedScreens,\n  wideModalMaxHeight,\n} from \"../constants.js\";\nimport { useConnectLocale } from \"../locale/getConnectLocale.js\";\nimport type { ConnectLocale } from \"../locale/types.js\";\nimport type { WelcomeScreen } from \"../screens/types.js\";\nimport { ConnectModalContent } from \"./ConnectModalContent.js\";\nimport { useSetupScreen } from \"./screen.js\";\n\n/**\n * An inline wallet connection component that allows to:\n *\n * - Connect to 500+ external wallets\n * - Connect with email, phone, passkey or socials\n * - Convert any wallet to a ERC4337 smart wallet for gasless transactions\n * - Sign in with ethereum (Auth)\n *\n * It renders the same UI as the [`ConnectButton`](https://portal.thirdweb.com/react/v5/ConnectButton) component's modal - but directly inline in the page instead of being in a modal.\n *\n * Once connected, the component does not render any UI. It only renders UI if wallet is not connected.\n *\n * @example\n *\n * ## Default setup\n *\n * ```tsx\n * import { createThirdwebClient } from \"thirdweb\";\n * import { ConnectEmbed } from \"thirdweb/react\";\n *\n * const client = createThirdwebClient({ clientId: \"YOUR_CLIENT_ID\" });\n *\n * <ConnectEmbed\n *    client={client}\n * />\n * ```\n *\n * [View all available config options](https://portal.thirdweb.com/references/typescript/v5/ConnectEmbedProps)\n *\n *  ## Customization options\n *\n * ### Customizing wallet options\n *\n * ```tsx\n * <ConnectEmbed\n *    client={client}\n *    wallets={[\n *      createWallet(\"io.metamask\"),\n *      createWallet(\"com.coinbase.wallet\"),\n *      createWallet(\"me.rainbow\"),\n *    ]}\n * />\n * ```\n *\n * [View all available wallets](https://portal.thirdweb.com/typescript/v5/supported-wallets)\n *\n *  ### Customizing the default chain to connect to\n *\n * ```tsx\n * import { base } from \"thirdweb/chains\";\n *\n * <ConnectEmbed\n *   client={client}\n *   chain={base}\n * />\n * ```\n *\n * ### Enabling Account Abstraction\n *\n * By passing the `accountAbstraction` prop, ALL connected wallets will be converted to smart accounts.\n * And by setting `sponsorGas` to `true`, all transactions done with those smart accounts will be sponsored.\n *\n * ```tsx\n * <ConnectEmbed\n * client={client}\n * accountAbstraction={{\n *   chain: sepolia,\n *   sponsorGas: true,\n * }}\n * />;\n * ```\n * Note that this prop doesn't affect ecosystem wallets. Ecosystem wallets will only be converted to smart accounts if the ecosystem owner has enabled account abstraction.\n *\n * ### Enabling sign in with ethereum (Auth)\n *\n * ```tsx\n * <ConnectEmbed\n * client={client}\n * auth={{\n *   isLoggedIn: async (address) => {\n *     console.log(\"checking if logged in!\", { address });\n *     return await isLoggedIn();\n *   },\n *   doLogin: async (params) => {\n *     console.log(\"logging in!\");\n *     await login(params);\n *   },\n *   getLoginPayload: async ({ address }) =>\n *     generatePayload({ address }),\n *   doLogout: async () => {\n *     console.log(\"logging out!\");\n *     await logout();\n *   },\n * }}\n * />;\n * ```\n *\n * ### Customizing the theme\n *\n * ```tsx\n * <ConnectEmbed\n *    client={client}\n *    theme=\"light\"\n * />\n * ```\n *\n * For more granular control, you can also pass a custom theme object:\n *\n * ```tsx\n * <ConnectEmbed\n *    client={client}\n *    theme={lightTheme({\n *      colors: {\n *        modalBg: \"red\",\n *      },\n *    })}\n * />\n * ```\n *\n * [View all available themes properties](https://portal.thirdweb.com/references/typescript/v5/Theme)\n *\n * ### Changing the display language\n *\n * ```tsx\n * <ConnectEmbed\n *    client={client}\n *    locale=\"ja_JP\"\n * />\n * ```\n *\n * [View all available locales](https://portal.thirdweb.com/references/typescript/v5/LocaleId)\n *\n * @param props -\n * The props for the `ConnectEmbed` component.\n *\n * Refer to the [`ConnectEmbedProps`](https://portal.thirdweb.com/references/typescript/v5/ConnectEmbedProps) type for more details\n *\n * @returns A JSX element that renders the <ConnectEmbed> component.\n * @component\n * @walletConnection\n */\nexport function ConnectEmbed(props: ConnectEmbedProps) {\n  const activeWallet = useActiveWallet();\n  const activeAccount = useActiveAccount();\n  const siweAuth = useSiweAuth(activeWallet, activeAccount, props.auth);\n  const show =\n    !activeAccount || (siweAuth.requiresAuth && !siweAuth.isLoggedIn);\n  const connectionManager = useConnectionManager();\n\n  // Add props.chain and props.chains to defined chains store\n  useEffect(() => {\n    if (props.chain) {\n      connectionManager.defineChains([props.chain]);\n    }\n  }, [props.chain, connectionManager]);\n\n  useEffect(() => {\n    if (props.chains) {\n      connectionManager.defineChains(props.chains);\n    }\n  }, [props.chains, connectionManager]);\n\n  const wallets = useMemo(\n    () =>\n      props.wallets ||\n      getDefaultWallets({\n        appMetadata: props.appMetadata,\n        chains: props.chains,\n      }),\n    [props.wallets, props.appMetadata, props.chains],\n  );\n  const localeId = props.locale || \"en_US\";\n  const localeQuery = useConnectLocale(localeId);\n\n  usePreloadWalletProviders({\n    wallets,\n  });\n\n  const modalSize = useMemo(() => {\n    return !canFitWideModal() || wallets.length === 1\n      ? \"compact\"\n      : props.modalSize || (\"compact\" as const);\n  }, [wallets.length, props.modalSize]);\n\n  const meta = useMemo(() => {\n    return {\n      privacyPolicyUrl: props.privacyPolicyUrl,\n      showThirdwebBranding: props.showThirdwebBranding !== false,\n      termsOfServiceUrl: props.termsOfServiceUrl,\n      title: undefined,\n      titleIconUrl: undefined,\n      requireApproval: props.requireApproval,\n    };\n  }, [\n    props.privacyPolicyUrl,\n    props.showThirdwebBranding,\n    props.termsOfServiceUrl,\n    props.requireApproval,\n  ]);\n\n  const preferredChain =\n    props.accountAbstraction?.chain || props.chain || props.chains?.[0];\n\n  const autoConnectComp = props.autoConnect !== false && (\n    <AutoConnect\n      chain={preferredChain}\n      appMetadata={props.appMetadata}\n      client={props.client}\n      siweAuth={siweAuth}\n      wallets={wallets}\n      accountAbstraction={props.accountAbstraction}\n      timeout={\n        typeof props.autoConnect === \"boolean\"\n          ? undefined\n          : props.autoConnect?.timeout\n      }\n      onConnect={props.onConnect}\n    />\n  );\n\n  if (show) {\n    if (!localeQuery.data) {\n      return (\n        <>\n          {autoConnectComp}\n          <CustomThemeProvider theme={props.theme}>\n            <EmbedContainer modalSize={modalSize}>\n              <LoadingScreen />\n            </EmbedContainer>\n          </CustomThemeProvider>\n        </>\n      );\n    }\n\n    return (\n      <WalletUIStatesProvider theme={props.theme} isOpen={true}>\n        <ConnectEmbedContent\n          auth={props.auth}\n          accountAbstraction={props.accountAbstraction}\n          chain={preferredChain}\n          chains={props.chains}\n          client={props.client}\n          connectLocale={localeQuery.data}\n          size={modalSize}\n          meta={meta}\n          header={props.header}\n          localeId={props.locale || \"en_US\"}\n          onConnect={props.onConnect}\n          recommendedWallets={props.recommendedWallets}\n          showAllWallets={props.showAllWallets}\n          walletConnect={props.walletConnect}\n          wallets={wallets}\n          className={props.className}\n          modalSize={modalSize}\n          style={props.style}\n          welcomeScreen={props.welcomeScreen}\n        />\n        {autoConnectComp}\n      </WalletUIStatesProvider>\n    );\n  }\n\n  return <div>{autoConnectComp}</div>;\n}\n\n/**\n * @internal\n */\nconst ConnectEmbedContent = (props: {\n  modalSize?: \"compact\" | \"wide\";\n  className?: string;\n  style?: React.CSSProperties;\n  // ---\n  accountAbstraction: SmartWalletOptions | undefined;\n  auth: SiweAuthOptions | undefined;\n  chain: Chain | undefined;\n  chains: Chain[] | undefined;\n  client: ThirdwebClient;\n  connectLocale: ConnectLocale;\n  meta: {\n    title?: string;\n    titleIconUrl?: string;\n    showThirdwebBranding?: boolean;\n    termsOfServiceUrl?: string;\n    privacyPolicyUrl?: string;\n  };\n  size: \"compact\" | \"wide\";\n  header:\n    | {\n        title?: string;\n        titleIcon?: string;\n      }\n    | true\n    | undefined;\n  localeId: LocaleId;\n  onConnect: ((wallet: Wallet) => void) | undefined;\n  recommendedWallets: Wallet[] | undefined;\n  showAllWallets: boolean | undefined;\n  walletConnect:\n    | {\n        projectId?: string;\n      }\n    | undefined;\n  wallets: Wallet[];\n  welcomeScreen: WelcomeScreen | undefined;\n}) => {\n  // const requiresSignIn = false;\n  const screenSetup = useSetupScreen({\n    size: props.size,\n    welcomeScreen: undefined,\n    wallets: props.wallets,\n  });\n  const { setScreen, initialScreen, screen } = screenSetup;\n  const activeWallet = useActiveWallet();\n  const activeAccount = useActiveAccount();\n  const siweAuth = useSiweAuth(activeWallet, activeAccount, props.auth);\n\n  const isAutoConnecting = useIsAutoConnecting();\n\n  let content = null;\n\n  // if sign in is required but connect embed is showing the initial screen - change to sign in screen\n  useEffect(() => {\n    if (\n      siweAuth.requiresAuth &&\n      !siweAuth.isLoggedIn &&\n      activeAccount &&\n      screen === initialScreen\n    ) {\n      setScreen(reservedScreens.signIn);\n    }\n  }, [siweAuth, setScreen, activeAccount, screen, initialScreen]);\n\n  const modalSize = !canFitWideModal()\n    ? \"compact\"\n    : props.modalSize || (\"compact\" as const);\n\n  // show spinner on page load and during auto connecting a wallet\n  if (isAutoConnecting) {\n    content = <LoadingScreen />;\n  } else {\n    content = (\n      <ConnectModalContent\n        shouldSetActive={true}\n        screenSetup={screenSetup}\n        isOpen={true}\n        onClose={() => {\n          setScreen(initialScreen);\n        }}\n        setModalVisibility={() => {\n          // no op\n        }}\n        accountAbstraction={props.accountAbstraction}\n        auth={props.auth}\n        chain={props.chain}\n        chains={props.chains}\n        client={props.client}\n        connectLocale={props.connectLocale}\n        meta={{\n          ...props.meta,\n          title:\n            typeof props.header === \"object\" ? props.header.title : undefined,\n          titleIconUrl:\n            typeof props.header === \"object\"\n              ? props.header.titleIcon\n              : undefined,\n        }}\n        size={props.size}\n        welcomeScreen={props.welcomeScreen}\n        hideHeader={!props.header}\n        onConnect={props.onConnect}\n        recommendedWallets={props.recommendedWallets}\n        showAllWallets={props.showAllWallets}\n        walletConnect={props.walletConnect}\n        wallets={props.wallets}\n        modalHeader={undefined}\n        walletIdsToHide={undefined}\n      />\n    );\n  }\n\n  return (\n    <EmbedContainer\n      modalSize={modalSize}\n      className={props.className}\n      style={props.style}\n    >\n      {modalSize === \"wide\" ? (\n        content\n      ) : (\n        <DynamicHeight> {content} </DynamicHeight>\n      )}\n    </EmbedContainer>\n  );\n};\n\nexport const EmbedContainer = /* @__PURE__ */ StyledDiv<{\n  modalSize: \"compact\" | \"wide\";\n}>((props) => {\n  const { modalSize } = props;\n  const theme = useCustomTheme();\n  return {\n    color: theme.colors.primaryText,\n    background: theme.colors.modalBg,\n    height: modalSize === \"compact\" ? \"auto\" : wideModalMaxHeight,\n    width: modalSize === \"compact\" ? modalMaxWidthCompact : modalMaxWidthWide,\n    boxSizing: \"border-box\",\n    position: \"relative\",\n    lineHeight: \"normal\",\n    borderRadius: radius.xl,\n    border: `1px solid ${theme.colors.borderColor}`,\n    overflow: \"hidden\",\n    fontFamily: theme.fontFamily,\n    \"& *::selection\": {\n      backgroundColor: theme.colors.selectedTextBg,\n      color: theme.colors.selectedTextColor,\n    },\n    \"& *\": {\n      boxSizing: \"border-box\",\n    },\n  };\n});\n", "\"use client\";\n\nimport { useSyncExternalStore } from \"react\";\nimport { useConnectionManagerCtx } from \"../../providers/connection-manager.js\";\n\n/**\n * A hook to check if the auto connect is in progress.\n * @example\n * ```jsx\n * function Example() {\n *   const isAutoConnecting = useIsAutoConnecting();\n *\n *   return <div> ... </div>;\n * }\n * ```\n * @returns A boolean indicating if the auto connect is in progress.\n * @walletConnection\n */\nexport function useIsAutoConnecting() {\n  const manager = useConnectionManagerCtx(\"useIsAutoConnecting\");\n  const store = manager.isAutoConnecting;\n  return useSyncExternalStore(store.subscribe, store.getValue, store.getValue);\n}\n", "import { useMutation } from \"@tanstack/react-query\";\nimport type { TransactionReceipt } from \"viem\";\nimport type { GaslessOptions } from \"../../../../transaction/actions/gasless/types.js\";\nimport {\n  type WaitForReceiptOptions,\n  waitForReceipt,\n} from \"../../../../transaction/actions/wait-for-tx-receipt.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type { Theme } from \"../../design-system/index.js\";\nimport type {\n  SendTransactionPayModalConfig,\n  useSendTransactionCore,\n} from \"./useSendTransaction.js\";\n\n/**\n * Props for the [`TransactionButton`](https://portal.thirdweb.com/references/typescript/v5/TransactionButton) component.\n * @transaction\n */\nexport type TransactionButtonProps = {\n  /**\n   * The a function returning a prepared transaction of type [`PreparedTransaction`](https://portal.thirdweb.com/references/typescript/v5/PreparedTransaction) to be sent when the button is clicked\n   */\n  transaction: () => // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n    | PreparedTransaction<any>\n    // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n    | Promise<PreparedTransaction<any>>;\n\n  /**\n   * Callback that will be called when the transaction is submitted onchain\n   * @param transactionResult - The object of type [`WaitForReceiptOptions`](https://portal.thirdweb.com/references/typescript/v5/WaitForReceiptOptions)\n   */\n  onTransactionSent?: (transactionResult: WaitForReceiptOptions) => void;\n  /**\n   *\n   * Callback that will be called when the transaction is confirmed onchain.\n   * If this callback is set, the component will wait for the transaction to be confirmed.\n   * @param receipt - The transaction receipt object of type [`TransactionReceipt`](https://portal.thirdweb.com/references/typescript/v5/TransactionReceipt)\n   */\n  onTransactionConfirmed?: (receipt: TransactionReceipt) => void;\n  /**\n   * The Error thrown when trying to send the transaction\n   * @param error - The `Error` object thrown\n   */\n  onError?: (error: Error) => void;\n  /**\n   * Callback to be called when the button is clicked\n   * This function is provoked before the transaction is sent\n   */\n  onClick?: () => void;\n  /**\n   * The className to apply to the button element for custom styling\n   */\n  className?: string;\n  /**\n   * The style to apply to the button element for custom styling\n   */\n  style?: React.CSSProperties;\n  /**\n   * Remove all default styling from the button\n   */\n  unstyled?: boolean;\n  /**\n   * The `React.ReactNode` to be rendered inside the button\n   */\n  children: React.ReactNode;\n\n  /**\n   * Configuration for gasless transactions.\n   * Refer to [`GaslessOptions`](https://portal.thirdweb.com/references/typescript/v5/GaslessOptions) for more details.\n   */\n  gasless?: GaslessOptions;\n\n  /**\n   * The button's disabled state\n   */\n  disabled?: boolean;\n\n  /**\n   * Configuration for the \"Pay Modal\" that opens when the user doesn't have enough funds to send a transaction.\n   * Set `payModal: false` to disable the \"Pay Modal\" popup\n   *\n   * This configuration object includes the following properties to configure the \"Pay Modal\" UI:\n   *\n   * ### `locale`\n   * The language to use for the \"Pay Modal\" UI. Defaults to `\"en_US\"`.\n   *\n   * ### `supportedTokens`\n   * An object of type [`SupportedTokens`](https://portal.thirdweb.com/references/typescript/v5/SupportedTokens) to configure the tokens to show for a chain.\n   *\n   * ### `theme`\n   * The theme to use for the \"Pay Modal\" UI. Defaults to `\"dark\"`.\n   *\n   * It can be set to `\"light\"` or `\"dark\"` or an object of type [`Theme`](https://portal.thirdweb.com/references/typescript/v5/Theme) for a custom theme.\n   *\n   * Refer to [`lightTheme`](https://portal.thirdweb.com/references/typescript/v5/lightTheme)\n   * or [`darkTheme`](https://portal.thirdweb.com/references/typescript/v5/darkTheme) helper functions to use the default light or dark theme and customize it.\n   */\n  payModal?: SendTransactionPayModalConfig;\n\n  /**\n   * The theme to use for the button\n   */\n  theme?: \"dark\" | \"light\" | Theme;\n\n  /**\n   * Set the type attribute of the button element.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/HTMLButtonElement/type)\n   */\n  type?: HTMLButtonElement[\"type\"];\n};\n\nexport const useTransactionButtonMutation = (\n  props: TransactionButtonProps,\n  sendTransactionFn: ReturnType<typeof useSendTransactionCore>[\"mutateAsync\"],\n) => {\n  const {\n    transaction,\n    onTransactionSent,\n    onTransactionConfirmed,\n    onError,\n    onClick,\n  } = props;\n  return useMutation({\n    mutationFn: async () => {\n      if (onClick) {\n        onClick();\n      }\n      try {\n        const resolvedTx = await transaction();\n        const result = await sendTransactionFn(resolvedTx);\n\n        if (onTransactionSent) {\n          onTransactionSent(result);\n        }\n\n        if (onTransactionConfirmed) {\n          const receipt = await waitForReceipt(result);\n          if (receipt.status === \"reverted\") {\n            throw new Error(\n              `Execution reverted: ${stringify(receipt, null, 2)}`,\n            );\n          }\n          onTransactionConfirmed(receipt);\n        }\n      } catch (error) {\n        if (onError) {\n          onError(error as Error);\n        }\n      } finally {\n      }\n    },\n  });\n};\n", "import { useContext } from \"react\";\nimport { randomBytesHex } from \"../../../../utils/random.js\";\nimport {\n  type SendTransactionConfig,\n  type ShowModalData,\n  useSendTransactionCore,\n} from \"../../../core/hooks/transaction/useSendTransaction.js\";\nimport { useActiveWallet } from \"../../../core/hooks/wallets/useActiveWallet.js\";\nimport { useSwitchActiveWalletChain } from \"../../../core/hooks/wallets/useSwitchActiveWalletChain.js\";\nimport { SetRootElementContext } from \"../../../core/providers/RootElementContext.js\";\nimport { TransactionModal } from \"../../ui/TransactionButton/TransactionModal.js\";\n\n/**\n * A hook to send a transaction with from the user's connected wallet.\n *\n * You can send a transaction with a [prepared contract call](https://portal.thirdweb.com/references/typescript/v5/prepareContractCall), a [prepared transaction](https://portal.thirdweb.com/references/typescript/v5/prepareTransaction), or using a write [Extension](https://portal.thirdweb.com/react/v5/extensions).\n *\n * @returns A UseMutationResult object to send a transaction.\n * @param config Configuration for the `useSendTransaction` hook.\n * Refer to [`SendTransactionConfig`](https://portal.thirdweb.com/references/typescript/v5/SendTransactionConfig) for more details.\n * @example\n *\n * ### Sending a prepared contract call\n *\n * ```tsx\n * import { useSendTransaction } from \"thirdweb/react\";\n * import { getContract, prepareContractCall } from \"thirdweb\";\n * import { sepolia } from \"thirdweb/chains\";\n *\n * const contract = getContract({\n *   address: \"0x...\",\n *   chain: sepolia,\n *   client,\n * });\n *\n * const { mutate: sendTx, data: transactionResult } = useSendTransaction();\n *\n * const onClick = () => {\n *   const transaction = prepareContractCall({\n *     contract,\n *     method: \"function transfer(address to, uint256 value)\",\n *     params: [to, value],\n *   });\n *   sendTx(transaction);\n * };\n * ```\n *\n * ### Using a write extension\n *\n * ```tsx\n * import { useSendTransaction } from \"thirdweb/react\";\n * import { mintTo } from \"thirdweb/extensions/erc721\";\n *\n * const { mutate: sendTx, data: transactionResult } = useSendTransaction();\n *\n * const onClick = () => {\n *   const transaction = mintTo({\n *     contract,\n *     to: \"0x...\",\n *     nft: {\n *       name: \"NFT Name\",\n *       description: \"NFT Description\",\n *       image: \"https://example.com/image.png\",\n *     },\n *   });\n *   sendTx(transaction);\n * };\n * ```\n *\n * ### Sending a prepared transaction\n *\n * ```tsx\n * import { useSendTransaction } from \"thirdweb/react\";\n * import { prepareTransaction } from \"thirdweb\";\n * import { sepolia } from \"thirdweb/chains\";\n *\n * const { mutate: sendTx, data: transactionResult } = useSendTransaction();\n *\n * const onClick = () => {\n *   // Send 0.1 SepoliaETH to an address\n *   const transaction = prepareTransaction({\n *     to: \"0x...\",\n *     value: toWei(\"0.1\"),\n *     chain: sepolia,\n *     client: thirdwebClient,\n *   });\n *   sendTx(transaction);\n * };\n * ```\n *\n * @transaction\n */\nexport function useSendTransaction(config: SendTransactionConfig = {}) {\n  const switchChain = useSwitchActiveWalletChain();\n  const wallet = useActiveWallet();\n  const setRootEl = useContext(SetRootElementContext);\n  const payModal = config.payModal;\n\n  let payModalEnabled = true;\n\n  if (payModal === false || config.gasless) {\n    payModalEnabled = false;\n  }\n\n  const showPayModal = (data: ShowModalData) => {\n    if (payModal === false) return;\n    setRootEl(\n      <TransactionModal\n        title={payModal?.metadata?.name || \"Transaction\"}\n        txId={randomBytesHex()}\n        tx={data.tx}\n        onComplete={data.sendTx}\n        onClose={() => {\n          setRootEl(null);\n          data.rejectTx(\n            new Error(\"User rejected transaction by closing modal\"),\n          );\n        }}\n        onTxSent={data.resolveTx}\n        client={data.tx.client}\n        localeId={payModal?.locale || \"en_US\"}\n        supportedTokens={payModal?.supportedTokens}\n        theme={payModal?.theme || \"dark\"}\n        modalMode={data.mode}\n        payOptions={{\n          buyWithCrypto: payModal?.buyWithCrypto,\n          buyWithFiat: payModal?.buyWithFiat,\n          purchaseData: payModal?.purchaseData,\n          mode: \"transaction\",\n          transaction: data.tx,\n          metadata: payModal?.metadata,\n          onPurchaseSuccess: payModal?.onPurchaseSuccess,\n          showThirdwebBranding: payModal?.showThirdwebBranding,\n        }}\n      />,\n    );\n  };\n\n  return useSendTransactionCore({\n    showPayModal:\n      !payModalEnabled || payModal === false ? undefined : showPayModal,\n    gasless: config.gasless,\n    switchChain,\n    wallet,\n  });\n}\n", "import { type UseMutationResult, useMutation } from \"@tanstack/react-query\";\nimport { trackPayEvent } from \"../../../../analytics/track/pay.js\";\nimport * as Bridge from \"../../../../bridge/index.js\";\nimport type { Chain } from \"../../../../chains/types.js\";\nimport type { BuyWithCryptoStatus } from \"../../../../pay/buyWithCrypto/getStatus.js\";\nimport type { BuyWithFiatStatus } from \"../../../../pay/buyWithFiat/getStatus.js\";\nimport type { FiatProvider } from \"../../../../pay/utils/commonTypes.js\";\nimport type { GaslessOptions } from \"../../../../transaction/actions/gasless/types.js\";\nimport { sendTransaction } from \"../../../../transaction/actions/send-transaction.js\";\nimport type { WaitForReceiptOptions } from \"../../../../transaction/actions/wait-for-tx-receipt.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { getTransactionGasCost } from \"../../../../transaction/utils.js\";\nimport type { Hex } from \"../../../../utils/encoding/hex.js\";\nimport { resolvePromisedValue } from \"../../../../utils/promise/resolve-promised-value.js\";\nimport type { Wallet } from \"../../../../wallets/interfaces/wallet.js\";\nimport { getTokenBalance } from \"../../../../wallets/utils/getTokenBalance.js\";\nimport { getWalletBalance } from \"../../../../wallets/utils/getWalletBalance.js\";\nimport type { LocaleId } from \"../../../web/ui/types.js\";\nimport type { Theme } from \"../../design-system/index.js\";\nimport type { SupportedTokens } from \"../../utils/defaultTokens.js\";\nimport { hasSponsoredTransactionsEnabled } from \"../../utils/wallet.js\";\n\n/**\n * Configuration for the \"Pay Modal\" that opens when the user doesn't have enough funds to send a transaction.\n * Set `payModal: false` to disable the \"Pay Modal\" popup\n *\n * This configuration object includes the following properties to configure the \"Pay Modal\" UI:\n *\n * ### `locale`\n * The language to use for the \"Pay Modal\" UI. Defaults to `\"en_US\"`.\n *\n * ### `supportedTokens`\n * An object of type [`SupportedTokens`](https://portal.thirdweb.com/references/typescript/v5/SupportedTokens) to configure the tokens to show for a chain.\n *\n * ### `theme`\n * The theme to use for the \"Pay Modal\" UI. Defaults to `\"dark\"`.\n *\n * It can be set to `\"light\"` or `\"dark\"` or an object of type [`Theme`](https://portal.thirdweb.com/references/typescript/v5/Theme) for a custom theme.\n *\n * Refer to [`lightTheme`](https://portal.thirdweb.com/references/typescript/v5/lightTheme)\n * or [`darkTheme`](https://portal.thirdweb.com/references/typescript/v5/darkTheme) helper functions to use the default light or dark theme and customize it.\n */\nexport type SendTransactionPayModalConfig =\n  | {\n      metadata?: {\n        name?: string;\n        image?: string;\n      };\n      locale?: LocaleId;\n      supportedTokens?: SupportedTokens;\n      theme?: Theme | \"light\" | \"dark\";\n      buyWithCrypto?:\n        | false\n        | {\n            testMode?: boolean;\n          };\n      buyWithFiat?:\n        | false\n        | {\n            prefillSource?: {\n              currency?: \"USD\" | \"CAD\" | \"GBP\" | \"EUR\" | \"JPY\";\n            };\n            testMode?: boolean;\n            preferredProvider?: FiatProvider;\n          };\n      purchaseData?: object;\n      /**\n       * Callback to be called when the user successfully completes the purchase.\n       */\n      onPurchaseSuccess?: (\n        info:\n          | {\n              type: \"crypto\";\n              status: BuyWithCryptoStatus;\n            }\n          | {\n              type: \"fiat\";\n              status: BuyWithFiatStatus;\n            }\n          | {\n              type: \"transaction\";\n              chainId: number;\n              transactionHash: Hex;\n            },\n      ) => void;\n      showThirdwebBranding?: boolean;\n    }\n  | false;\n\n/**\n * Configuration for the `useSendTransaction` hook.\n */\nexport type SendTransactionConfig = {\n  /**\n   * Refer to [`SendTransactionPayModalConfig`](https://portal.thirdweb.com/references/typescript/v5/SendTransactionPayModalConfig) for more details.\n   */\n  payModal?: SendTransactionPayModalConfig;\n\n  /**\n   * Configuration for gasless transactions.\n   * Refer to [`GaslessOptions`](https://portal.thirdweb.com/references/typescript/v5/GaslessOptions) for more details.\n   */\n  gasless?: GaslessOptions;\n};\n\nexport type ShowModalData = {\n  mode: \"buy\" | \"deposit\";\n  tx: PreparedTransaction;\n  sendTx: () => void;\n  rejectTx: (reason: Error) => void;\n  resolveTx: (data: WaitForReceiptOptions) => void;\n};\n\n/**\n * A hook to send a transaction.\n * @returns A mutation object to send a transaction.\n * @example\n * ```jsx\n * import { useSendTransaction } from \"thirdweb/react\";\n * const { mutate: sendTx, data: transactionResult } = useSendTransaction();\n *\n * // later\n * sendTx(tx);\n * ```\n * @internal\n */\nexport function useSendTransactionCore(args: {\n  showPayModal?: (data: ShowModalData) => void;\n  gasless?: GaslessOptions;\n  wallet: Wallet | undefined;\n  switchChain: (chain: Chain) => Promise<void>;\n}): UseMutationResult<WaitForReceiptOptions, Error, PreparedTransaction> {\n  const { showPayModal, gasless, wallet, switchChain } = args;\n  let _account = wallet?.getAccount();\n\n  return useMutation({\n    mutationFn: async (tx) => {\n      // switch chain if needed\n      if (wallet && tx.chain.id !== wallet.getChain()?.id) {\n        await switchChain(tx.chain);\n        // in smart wallet case, account may change after chain switch\n        _account = wallet.getAccount();\n      }\n\n      const account = _account;\n\n      if (!account) {\n        throw new Error(\"No active account\");\n      }\n\n      if (!showPayModal) {\n        trackPayEvent({\n          client: tx.client,\n          walletAddress: account.address,\n          walletType: wallet?.id,\n          chainId: tx.chain.id,\n          event: \"pay_transaction_modal_disabled\",\n        });\n        return sendTransaction({\n          transaction: tx,\n          account,\n          gasless,\n        });\n      }\n\n      return new Promise<WaitForReceiptOptions>((resolve, reject) => {\n        const sendTx = async () => {\n          try {\n            const res = await sendTransaction({\n              transaction: tx,\n              account,\n              gasless,\n            });\n\n            resolve(res);\n          } catch (e) {\n            reject(e);\n          }\n        };\n\n        (async () => {\n          try {\n            const [_nativeValue, _erc20Value] = await Promise.all([\n              resolvePromisedValue(tx.value),\n              resolvePromisedValue(tx.erc20Value),\n            ]);\n\n            const nativeValue = _nativeValue || 0n;\n            const erc20Value = _erc20Value?.amountWei || 0n;\n\n            const [nativeBalance, erc20Balance, gasCost] = await Promise.all([\n              getWalletBalance({\n                client: tx.client,\n                address: account.address,\n                chain: tx.chain,\n              }),\n              _erc20Value?.tokenAddress\n                ? getTokenBalance({\n                    client: tx.client,\n                    account,\n                    chain: tx.chain,\n                    tokenAddress: _erc20Value.tokenAddress,\n                  })\n                : undefined,\n              getTransactionGasCost(tx, account.address),\n            ]);\n\n            const gasSponsored = hasSponsoredTransactionsEnabled(wallet);\n            const txGasCost = gasSponsored ? 0n : gasCost;\n            const nativeCost = nativeValue + txGasCost;\n\n            const shouldShowModal =\n              (erc20Value > 0n &&\n                erc20Balance &&\n                erc20Balance.value < erc20Value) ||\n              (nativeCost > 0n && nativeBalance.value < nativeCost);\n\n            if (shouldShowModal) {\n              const supportedDestinations = await Bridge.routes({\n                client: tx.client,\n                destinationChainId: tx.chain.id,\n                destinationTokenAddress: _erc20Value?.tokenAddress,\n              }).catch((err) => {\n                trackPayEvent({\n                  client: tx.client,\n                  walletAddress: account.address,\n                  walletType: wallet?.id,\n                  toChainId: tx.chain.id,\n                  event: \"pay_transaction_modal_pay_api_error\",\n                  error: err?.message,\n                });\n                return null;\n              });\n\n              if (\n                !supportedDestinations ||\n                supportedDestinations.length === 0\n              ) {\n                // not a supported destination -> show deposit screen\n                trackPayEvent({\n                  client: tx.client,\n                  walletAddress: account.address,\n                  walletType: wallet?.id,\n                  toChainId: tx.chain.id,\n                  toToken: _erc20Value?.tokenAddress || undefined,\n                  event: \"pay_transaction_modal_chain_token_not_supported\",\n                  error: JSON.stringify({\n                    chain: tx.chain.id,\n                    token: _erc20Value?.tokenAddress,\n                    message: \"chain/token not supported\",\n                  }),\n                });\n\n                showPayModal({\n                  mode: \"deposit\",\n                  tx,\n                  sendTx,\n                  rejectTx: reject,\n                  resolveTx: resolve,\n                });\n                return;\n              }\n\n              // chain is supported, show buy mode\n              showPayModal({\n                mode: \"buy\",\n                tx,\n                sendTx,\n                rejectTx: reject,\n                resolveTx: resolve,\n              });\n            } else {\n              trackPayEvent({\n                client: tx.client,\n                walletAddress: account.address,\n                walletType: wallet?.id,\n                toChainId: tx.chain.id,\n                toToken: _erc20Value?.tokenAddress || undefined,\n                event: \"pay_transaction_modal_has_enough_funds\",\n              });\n              sendTx();\n            }\n          } catch (e) {\n            console.error(\"Failed to estimate cost\", e);\n            // send it anyway?\n            sendTx();\n          }\n        })();\n      });\n    },\n  });\n}\n", "import { useQuery } from \"@tanstack/react-query\";\nimport { useState } from \"react\";\nimport { trackPayEvent } from \"../../../../analytics/track/pay.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport type { WaitForReceiptOptions } from \"../../../../transaction/actions/wait-for-tx-receipt.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { resolvePromisedValue } from \"../../../../utils/promise/resolve-promised-value.js\";\nimport { CustomThemeProvider } from \"../../../core/design-system/CustomThemeProvider.js\";\nimport type { Theme } from \"../../../core/design-system/index.js\";\nimport type { PayUIOptions } from \"../../../core/hooks/connection/ConnectButtonProps.js\";\nimport { useActiveAccount } from \"../../../core/hooks/wallets/useActiveAccount.js\";\nimport { useActiveWallet } from \"../../../core/hooks/wallets/useActiveWallet.js\";\nimport type { SupportedTokens } from \"../../../core/utils/defaultTokens.js\";\nimport { LoadingScreen } from \"../../wallets/shared/LoadingScreen.js\";\nimport { useConnectLocale } from \"../ConnectWallet/locale/getConnectLocale.js\";\nimport { LazyBuyScreen } from \"../ConnectWallet/screens/Buy/LazyBuyScreen.js\";\nimport { Modal } from \"../components/Modal.js\";\nimport type { LocaleId } from \"../types.js\";\nimport { DepositScreen } from \"./DepositScreen.js\";\nimport { ExecutingTxScreen } from \"./ExecutingScreen.js\";\n\ntype ModalProps = {\n  title: string;\n  txId: string;\n  onComplete: () => void;\n  onClose: () => void;\n  client: ThirdwebClient;\n  localeId: LocaleId;\n  supportedTokens?: SupportedTokens;\n  theme: Theme | \"light\" | \"dark\";\n  tx: PreparedTransaction;\n  payOptions: PayUIOptions;\n  onTxSent: (data: WaitForReceiptOptions) => void;\n  modalMode: \"buy\" | \"deposit\";\n};\n\nexport function TransactionModal(props: ModalProps) {\n  const account = useActiveAccount();\n  const wallet = useActiveWallet();\n\n  useQuery({\n    queryKey: [\"transaction-modal-event\", props.txId],\n    queryFn: async () => {\n      if (!account || !wallet) {\n        throw new Error(); // never happens, because enabled is false\n      }\n      trackPayEvent({\n        client: props.client,\n        walletAddress: account.address,\n        walletType: wallet.id,\n        toChainId: props.tx.chain.id,\n        toToken: props.tx.erc20Value\n          ? (await resolvePromisedValue(props.tx.erc20Value))?.tokenAddress\n          : undefined,\n        event:\n          props.modalMode === \"buy\"\n            ? \"open_pay_transaction_modal\"\n            : \"open_pay_deposit_modal\",\n      });\n\n      return null;\n    },\n    enabled: !!wallet && !!account,\n  });\n\n  return (\n    <CustomThemeProvider theme={props.theme}>\n      <Modal\n        open={true}\n        size=\"compact\"\n        setOpen={(_open) => {\n          if (!_open) {\n            props.onClose();\n          }\n        }}\n      >\n        <TransactionModalContent {...props} />\n      </Modal>\n    </CustomThemeProvider>\n  );\n}\n\nfunction TransactionModalContent(props: ModalProps & { onBack?: () => void }) {\n  const localeQuery = useConnectLocale(props.localeId);\n  const [screen, setScreen] = useState<\"buy\" | \"execute-tx\">(\"buy\");\n\n  if (!localeQuery.data) {\n    return <LoadingScreen />;\n  }\n\n  if (screen === \"execute-tx\") {\n    return (\n      <ExecutingTxScreen\n        tx={props.tx}\n        closeModal={props.onClose}\n        onTxSent={props.onTxSent}\n      />\n    );\n  }\n\n  if (props.modalMode === \"deposit\") {\n    return (\n      <DepositScreen\n        client={props.client}\n        onBack={props.onBack}\n        tx={props.tx}\n        connectLocale={localeQuery.data}\n        onDone={() => {\n          setScreen(\"execute-tx\");\n        }}\n      />\n    );\n  }\n\n  return (\n    <LazyBuyScreen\n      title={props.title}\n      isEmbed={false}\n      client={props.client}\n      onBack={props.onBack}\n      supportedTokens={props.supportedTokens}\n      connectLocale={localeQuery.data}\n      theme={typeof props.theme === \"string\" ? props.theme : props.theme.type}\n      payOptions={props.payOptions}\n      onDone={() => {\n        setScreen(\"execute-tx\");\n      }}\n      connectOptions={undefined}\n    />\n  );\n}\n", "import { keyframes } from \"@emotion/react\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { shortenAddress } from \"../../../../utils/address.js\";\nimport { formatNumber } from \"../../../../utils/formatNumber.js\";\nimport { toTokens } from \"../../../../utils/units.js\";\nimport { useCustomTheme } from \"../../../core/design-system/CustomThemeProvider.js\";\nimport {\n  fontSize,\n  iconSize,\n  radius,\n  spacing,\n} from \"../../../core/design-system/index.js\";\nimport { useActiveAccount } from \"../../../core/hooks/wallets/useActiveAccount.js\";\nimport { useActiveWallet } from \"../../../core/hooks/wallets/useActiveWallet.js\";\nimport { hasSponsoredTransactionsEnabled } from \"../../../core/utils/wallet.js\";\nimport { ErrorState } from \"../../wallets/shared/ErrorState.js\";\nimport { LoadingScreen } from \"../../wallets/shared/LoadingScreen.js\";\nimport { CoinsIcon } from \"../ConnectWallet/icons/CoinsIcon.js\";\nimport type { ConnectLocale } from \"../ConnectWallet/locale/types.js\";\nimport { useTransactionCostAndData } from \"../ConnectWallet/screens/Buy/main/useBuyTxStates.js\";\nimport { WalletRow } from \"../ConnectWallet/screens/Buy/swap/WalletRow.js\";\nimport { formatTokenBalance } from \"../ConnectWallet/screens/formatTokenBalance.js\";\nimport { isNativeToken } from \"../ConnectWallet/screens/nativeToken.js\";\nimport { CopyIcon } from \"../components/CopyIcon.js\";\nimport { QRCode } from \"../components/QRCode.js\";\nimport { Skeleton } from \"../components/Skeleton.js\";\nimport { Spacer } from \"../components/Spacer.js\";\nimport { WalletImage } from \"../components/WalletImage.js\";\nimport { Container, ModalHeader } from \"../components/basic.js\";\nimport { Button } from \"../components/buttons.js\";\nimport { Text } from \"../components/text.js\";\nimport { TokenSymbol } from \"../components/token/TokenSymbol.js\";\nimport { StyledButton, StyledDiv } from \"../design-system/elements.js\";\nimport { useClipboard } from \"../hooks/useCopyClipboard.js\";\n\nconst pulseAnimation = keyframes`\n0% {\n  opacity: 1;\n  transform: scale(0.5);\n}\n100% {\n  opacity: 0;\n  transform: scale(1.5);\n}\n`;\n\nconst WaitingBadge = /* @__PURE__ */ StyledDiv(() => {\n  const theme = useCustomTheme();\n  return {\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: spacing.sm,\n    backgroundColor: theme.colors.tertiaryBg,\n    border: `1px solid ${theme.colors.borderColor}`,\n    padding: `${spacing.md} ${spacing.sm}`,\n    borderRadius: radius.lg,\n    color: theme.colors.secondaryText,\n    fontSize: fontSize.sm,\n    fontWeight: 500,\n    position: \"relative\" as const,\n    \"&::before\": {\n      content: '\"\"',\n      width: \"8px\",\n      height: \"8px\",\n      borderRadius: \"50%\",\n      backgroundColor: theme.colors.accentText,\n      animation: `${pulseAnimation} 1s infinite`,\n    },\n  };\n});\n\n/**\n *\n * @internal\n */\nexport function DepositScreen(props: {\n  onBack: (() => void) | undefined;\n  connectLocale: ConnectLocale;\n  client: ThirdwebClient;\n  tx: PreparedTransaction;\n  onDone: () => void;\n}) {\n  const activeWallet = useActiveWallet();\n  const activeAccount = useActiveAccount();\n  const address = activeAccount?.address;\n  const { hasCopied, onCopy } = useClipboard(address || \"\");\n  const { connectLocale, client } = props;\n  const locale = connectLocale.receiveFundsScreen;\n  const isTestnet = props.tx.chain.testnet === true;\n  const {\n    data: transactionCostAndData,\n    error: transactionCostAndDataError,\n    isFetching: transactionCostAndDataFetching,\n    refetch: transactionCostAndDataRefetch,\n  } = useTransactionCostAndData({\n    transaction: props.tx,\n    account: activeAccount,\n    supportedDestinations: [],\n    refetchIntervalMs: 10_000,\n  });\n  const theme = useCustomTheme();\n  const sponsoredTransactionsEnabled =\n    hasSponsoredTransactionsEnabled(activeWallet);\n\n  if (transactionCostAndDataError) {\n    return (\n      <Container\n        style={{\n          minHeight: \"350px\",\n        }}\n        fullHeight\n        flex=\"row\"\n        center=\"both\"\n      >\n        <ErrorState\n          title={transactionCostAndDataError?.message || \"Something went wrong\"}\n          onTryAgain={transactionCostAndDataRefetch}\n        />\n      </Container>\n    );\n  }\n\n  if (!transactionCostAndData) {\n    return <LoadingScreen />;\n  }\n\n  const totalCost =\n    isNativeToken(transactionCostAndData.token) && !sponsoredTransactionsEnabled\n      ? transactionCostAndData.transactionValueWei +\n        transactionCostAndData.gasCostWei\n      : transactionCostAndData.transactionValueWei;\n  const insufficientFunds =\n    transactionCostAndData.walletBalance.value < totalCost;\n  const requiredFunds = transactionCostAndData.walletBalance.value\n    ? totalCost - transactionCostAndData.walletBalance.value\n    : totalCost;\n\n  const openFaucetLink = () => {\n    window.open(\n      `https://thirdweb.com/${props.tx.chain.id}?utm_source=ub_deposit`,\n    );\n  };\n\n  return (\n    <Container p=\"lg\">\n      <ModalHeader title={\"Insufficient funds\"} onBack={props.onBack} />\n\n      <Spacer y=\"lg\" />\n\n      <Container flex=\"column\" gap=\"sm\">\n        {insufficientFunds && (\n          <div>\n            <Text size=\"xs\" center color=\"danger\" multiline>\n              You need{\" \"}\n              {formatNumber(\n                Number.parseFloat(\n                  toTokens(requiredFunds, transactionCostAndData.decimals),\n                ),\n                5,\n              )}{\" \"}\n              {transactionCostAndData.token.symbol} to continue\n            </Text>\n          </div>\n        )}\n        <Container\n          flex=\"row\"\n          style={{\n            justifyContent: \"space-between\",\n            padding: spacing.sm,\n            marginBottom: spacing.sm,\n            borderRadius: spacing.md,\n            backgroundColor: theme.colors.tertiaryBg,\n            border: `1px solid ${theme.colors.borderColor}`,\n          }}\n        >\n          {activeAccount && (\n            <WalletRow\n              address={activeAccount?.address}\n              iconSize=\"md\"\n              client={client}\n            />\n          )}\n          {transactionCostAndData.walletBalance.value !== undefined &&\n          !transactionCostAndDataFetching ? (\n            <Container flex=\"row\" gap=\"3xs\" center=\"y\">\n              <Text size=\"xs\" color=\"secondaryText\" weight={500}>\n                {formatTokenBalance(\n                  transactionCostAndData.walletBalance,\n                  false,\n                )}\n              </Text>\n              <TokenSymbol\n                token={transactionCostAndData.token}\n                chain={props.tx.chain}\n                size=\"xs\"\n                color=\"secondaryText\"\n              />\n            </Container>\n          ) : (\n            <Container flex=\"row\" gap=\"3xs\" center=\"y\">\n              <Skeleton width=\"70px\" height={fontSize.xs} />\n            </Container>\n          )}\n        </Container>\n      </Container>\n\n      <WalletAddressContainer onClick={onCopy}>\n        <Container flex=\"column\" gap=\"md\" center=\"both\" expand>\n          <Container flex=\"row\" center=\"x\">\n            <QRCode\n              qrCodeUri={address}\n              size={250}\n              QRIcon={\n                activeWallet && (\n                  <WalletImage\n                    id={activeWallet.id}\n                    size={iconSize.xl}\n                    client={client}\n                  />\n                )\n              }\n            />\n          </Container>\n          <Container flex=\"row\" center=\"x\" gap=\"xs\">\n            <Text color=\"primaryText\" size=\"md\">\n              {address ? shortenAddress(address) : \"\"}\n            </Text>\n            <CopyIcon\n              text={address || \"\"}\n              tip=\"Copy address\"\n              hasCopied={hasCopied}\n            />\n          </Container>\n        </Container>\n      </WalletAddressContainer>\n\n      <Spacer y=\"md\" />\n\n      <Text\n        multiline\n        center\n        balance\n        size=\"sm\"\n        className=\"receive_fund_screen_instruction\"\n      >\n        {locale.instruction}\n      </Text>\n\n      <Spacer y=\"md\" />\n\n      {insufficientFunds ? (\n        <WaitingBadge>\n          Waiting for funds on {transactionCostAndData.chainMetadata.name}...\n        </WaitingBadge>\n      ) : (\n        <Button variant=\"accent\" onClick={props.onDone} fullWidth>\n          Continue\n        </Button>\n      )}\n      {insufficientFunds && isTestnet && (\n        <>\n          <Spacer y=\"md\" />\n          <Button variant=\"link\" onClick={openFaucetLink} fullWidth>\n            <Container flex=\"row\" center=\"x\" gap=\"xs\" color=\"accentText\">\n              <CoinsIcon size={iconSize.sm} />\n              <Text size=\"xs\" color=\"accentText\" weight={500} center>\n                Get testnet funds\n              </Text>\n            </Container>\n          </Button>\n        </>\n      )}\n    </Container>\n  );\n}\n\nconst WalletAddressContainer = /* @__PURE__ */ StyledButton((_) => {\n  const theme = useCustomTheme();\n  return {\n    all: \"unset\",\n    width: \"100%\",\n    boxSizing: \"border-box\",\n    cursor: \"pointer\",\n    padding: spacing.md,\n    display: \"flex\",\n    justifyContent: \"space-between\",\n    border: `1px solid ${theme.colors.borderColor}`,\n    borderRadius: radius.lg,\n    transition: \"border-color 200ms ease\",\n    \"&:hover\": {\n      borderColor: theme.colors.accentText,\n    },\n  };\n});\n", "import { CheckCircledIcon, ExternalLinkIcon } from \"@radix-ui/react-icons\";\nimport { useCallback, useEffect, useRef, useState } from \"react\";\nimport type { Hex } from \"viem\";\nimport type { WaitForReceiptOptions } from \"../../../../transaction/actions/wait-for-tx-receipt.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { formatExplorerTxUrl } from \"../../../../utils/url.js\";\nimport { iconSize } from \"../../../core/design-system/index.js\";\nimport { useChainExplorers } from \"../../../core/hooks/others/useChainQuery.js\";\nimport { useSendTransaction } from \"../../hooks/transaction/useSendTransaction.js\";\nimport { AccentFailIcon } from \"../ConnectWallet/icons/AccentFailIcon.js\";\nimport { Spacer } from \"../components/Spacer.js\";\nimport { Spinner } from \"../components/Spinner.js\";\nimport { Container, ModalHeader } from \"../components/basic.js\";\nimport { Button, ButtonLink } from \"../components/buttons.js\";\nimport { Text } from \"../components/text.js\";\n\nexport function ExecutingTxScreen(props: {\n  tx: PreparedTransaction;\n  closeModal: () => void;\n  onTxSent: (data: WaitForReceiptOptions) => void;\n  onBack?: () => void;\n}) {\n  const sendTxCore = useSendTransaction({\n    payModal: false,\n  });\n  const [txHash, setTxHash] = useState<Hex | undefined>();\n  const [txError, setTxError] = useState<Error | undefined>();\n  const chainExplorers = useChainExplorers(props.tx.chain);\n  const [status, setStatus] = useState<\"loading\" | \"failed\" | \"sent\">(\n    \"loading\",\n  );\n\n  const sendTx = useCallback(async () => {\n    setStatus(\"loading\");\n    setTxError(undefined);\n    try {\n      const txData = await sendTxCore.mutateAsync(props.tx);\n      setTxHash(txData.transactionHash);\n      props.onTxSent(txData);\n      setStatus(\"sent\");\n    } catch (e) {\n      // Do not reject the transaction here, because the user may want to try again\n      // we only reject on modal close\n      console.error(e);\n      setTxError(e as Error);\n      setStatus(\"failed\");\n    }\n  }, [sendTxCore, props.tx, props.onTxSent]);\n\n  const done = useRef(false);\n  useEffect(() => {\n    if (done.current) {\n      return;\n    }\n\n    done.current = true;\n    sendTx();\n  }, [sendTx]);\n\n  return (\n    <Container p=\"lg\">\n      <ModalHeader title=\"Transaction\" onBack={props.onBack} />\n\n      <Spacer y=\"xxl\" />\n\n      <Container flex=\"row\" center=\"x\">\n        {status === \"loading\" && <Spinner size=\"xxl\" color=\"accentText\" />}\n        {status === \"failed\" && <AccentFailIcon size={iconSize[\"3xl\"]} />}\n        {status === \"sent\" && (\n          <Container color=\"success\" flex=\"row\" center=\"both\">\n            <CheckCircledIcon\n              width={iconSize[\"3xl\"]}\n              height={iconSize[\"3xl\"]}\n            />\n          </Container>\n        )}\n      </Container>\n      <Spacer y=\"lg\" />\n\n      <Text color=\"primaryText\" center size=\"lg\">\n        {status === \"loading\" && \"Sending transaction\"}\n        {status === \"failed\" && \"Transaction failed\"}\n        {status === \"sent\" && \"Transaction sent\"}\n      </Text>\n      <Spacer y=\"sm\" />\n      <Text color=\"danger\" center size=\"sm\">\n        {status === \"failed\" && txError ? txError.message || \"\" : \"\"}\n      </Text>\n\n      <Spacer y=\"xxl\" />\n\n      {status === \"failed\" && (\n        <Button variant=\"accent\" fullWidth onClick={sendTx}>\n          Try Again\n        </Button>\n      )}\n\n      {status === \"sent\" && (\n        <>\n          <Button variant=\"accent\" fullWidth onClick={props.closeModal}>\n            Done\n          </Button>\n          {txHash && (\n            <>\n              <Spacer y=\"sm\" />\n              <ButtonLink\n                fullWidth\n                variant=\"outline\"\n                href={formatExplorerTxUrl(\n                  chainExplorers.explorers[0]?.url ?? \"\",\n                  txHash,\n                )}\n                target=\"_blank\"\n                as=\"a\"\n                gap=\"xs\"\n                style={{\n                  textDecoration: \"none\",\n                  color: \"inherit\",\n                }}\n              >\n                View on Explorer\n                <ExternalLinkIcon width={iconSize.sm} height={iconSize.sm} />\n              </ButtonLink>\n            </>\n          )}\n        </>\n      )}\n    </Container>\n  );\n}\n", "\"use client\";\nimport { CustomThemeProvider } from \"../../../core/design-system/CustomThemeProvider.js\";\nimport {\n  type TransactionButtonProps,\n  useTransactionButtonMutation,\n} from \"../../../core/hooks/transaction/transaction-button-utils.js\";\nimport { useActiveAccount } from \"../../../core/hooks/wallets/useActiveAccount.js\";\nimport { useSendTransaction } from \"../../hooks/transaction/useSendTransaction.js\";\nimport { Spinner } from \"../components/Spinner.js\";\nimport { Button } from \"../components/buttons.js\";\n\n/**\n * TransactionButton component is used to render a button that triggers a transaction.\n * It shows a \"Switch Network\" button if the connected wallet is on a different chain than the transaction.\n * @param props - The props for this component.\n * Refer to [TransactionButtonProps](https://portal.thirdweb.com/references/typescript/v5/TransactionButtonProps) for details.\n * @example\n *\n * ### Basic usage\n * ```tsx\n * <TransactionButton\n *   transaction={() => {}}\n *   onTransactionConfirmed={handleSuccess}\n *   onError={handleError}\n * >\n *   Confirm Transaction\n * </TransactionButton>\n * ```\n *\n * ### Customize the styling by passing the `unstyled` prop and your inline styles and/or classes:\n * ```tsx\n * <TransactionButton\n *   transaction={() => {}}\n *   unstyled\n *   className=\"bg-white text-black rounded-md p-4 flex items-center justify-center\"\n * >\n *   Confirm Transaction\n * </TransactionButton>\n * ```\n *\n * ### Handle errors\n * ```tsx\n * <TransactionButton\n *   transaction={() => ...}\n *   onError={(err) => {\n *     alert(err.message);\n *     // Add your own logic here\n *   }}\n * >\n *   Confirm Transaction\n * </TransactionButton>\n * ```\n *\n * ### Alert when a transaction is sent\n * ```tsx\n * <TransactionButton\n *   transaction={() => ...}\n *   onTransactionSent={(tx) => {\n *     alert(\"transaction sent!\");\n *     // Add your own logic here. For example, a toast\n *   }}\n * >\n *   Confirm Transaction\n * </TransactionButton>\n * ```\n *\n * ### Alert when a transaction is completed\n * ```tsx\n * <TransactionButton\n *   transaction={() => ...}\n *   onTransactionConfirmed={(tx) => {\n *     alert(\"transaction sent!\");\n *     console.log(tx);\n *     // Add your own logic here. For example, a toast\n *   }}\n * >\n *   Confirm Transaction\n * </TransactionButton>\n * ```\n *\n * ### The onClick prop, if provided, will be called before the transaction is sent.\n * ```tsx\n * <TransactionButton\n *   onClick={() => alert(\"Transaction is about to be sent\")}\n *   transaction={...}\n * >\n *   ...\n * </TransactionButton>\n * ```\n *\n * ### Attach custom Pay metadata\n * ```tsx\n * <TransactionButton\n *   payModal={{\n *     // This image & title will show up in the Pay modal\n *     metadata: {\n *       name: \"Van Gogh Starry Night\",\n *       image: \"https://unsplash.com/starry-night.png\"\n *     }\n *   }}\n * >\n *   ...\n * </TransactionButton>\n * ```\n *\n * ### Gasless usage with [thirdweb Engine](https://portal.thirdweb.com/engine)\n * ```tsx\n * <TransactionButton\n *   gasless={{\n *     provider: \"engine\",\n *     relayerUrl: \"https://thirdweb.engine-***.thirdweb.com/relayer/***\",\n *     relayerForwarderAddress: \"0x...\",\n *   }}\n * >\n *   ...\n * </TransactionButton>\n * ```\n *\n * ### Gasless usage with OpenZeppelin\n * ```tsx\n * <TransactionButton\n *   gasless={{\n *     provider: \"openzeppelin\",\n *     relayerUrl: \"https://...\",\n *     relayerForwarderAddress: \"0x...\",\n *   }}\n * >\n *   ...\n * </TransactionButton>\n * ```\n * @component\n * @transaction\n */\nexport function TransactionButton(props: TransactionButtonProps) {\n  const {\n    children,\n    transaction,\n    onTransactionSent,\n    onTransactionConfirmed,\n    onError,\n    onClick,\n    gasless,\n    payModal,\n    disabled,\n    unstyled,\n    ...buttonProps\n  } = props;\n  const account = useActiveAccount();\n  const sendTransaction = useSendTransaction({ gasless, payModal });\n  const { mutate: handleClick, isPending } = useTransactionButtonMutation(\n    props,\n    sendTransaction.mutateAsync,\n  );\n\n  return (\n    <CustomThemeProvider theme={props.theme}>\n      <Button\n        gap=\"xs\"\n        disabled={!account || disabled || isPending}\n        variant=\"primary\"\n        unstyled={unstyled}\n        data-is-loading={isPending}\n        onClick={() => handleClick()}\n        {...buttonProps}\n        style={\n          !unstyled\n            ? {\n                opacity: !account || disabled ? 0.5 : 1,\n                minWidth: \"165px\",\n                position: \"relative\",\n                ...buttonProps.style,\n              }\n            : {\n                position: \"relative\",\n                ...buttonProps.style,\n              }\n        }\n      >\n        <span style={{ visibility: isPending ? \"hidden\" : \"visible\" }}>\n          {children}\n        </span>\n        {isPending && (\n          <div\n            style={{\n              position: \"absolute\",\n              display: \"flex\",\n              alignItems: \"center\",\n              height: \"100%\",\n              top: 0,\n              bottom: 0,\n              margin: \"auto\",\n            }}\n          >\n            <Spinner size=\"md\" color=\"primaryButtonText\" />\n          </div>\n        )}\n      </Button>\n    </CustomThemeProvider>\n  );\n}\n", "\"use client\";\nimport { useMemo } from \"react\";\nimport { webLocalStorage } from \"../../../utils/storage/webStorage.js\";\nimport {\n  type ConnectionManager,\n  createConnectionManager,\n} from \"../../../wallets/manager/index.js\";\nimport { ThirdwebProviderCore } from \"../../core/providers/thirdweb-provider.js\";\n\n/**\n * The ThirdwebProvider is component is a provider component that sets up the React Query client.\n * @param props - The props for the ThirdwebProvider\n * @example\n * ```jsx\n * import { ThirdwebProvider } from \"thirdweb/react\";\n *\n * function Example() {\n *  return (\n *    <ThirdwebProvider>\n *      <App />\n *    </ThirdwebProvider>\n *   )\n * }\n * ```\n * @component\n * @walletConnection\n */\nexport function ThirdwebProvider(\n  props: React.PropsWithChildren<{\n    connectionManager?: ConnectionManager;\n  }>,\n) {\n  const connectionManager = useMemo(\n    () => props.connectionManager || createConnectionManager(webLocalStorage),\n    [props.connectionManager],\n  );\n\n  return (\n    <ThirdwebProviderCore manager={connectionManager}>\n      {props.children}\n    </ThirdwebProviderCore>\n  );\n}\n", "\"use client\";\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { useState } from \"react\";\nimport { waitForReceipt } from \"../../../transaction/actions/wait-for-tx-receipt.js\";\nimport { isBaseTransactionOptions } from \"../../../transaction/types.js\";\nimport type { Hex } from \"../../../utils/encoding/hex.js\";\nimport { isObjectWithKeys } from \"../../../utils/type-guards.js\";\nimport type { ConnectionManager } from \"../../../wallets/manager/index.js\";\nimport { structuralSharing } from \"../utils/structuralSharing.js\";\nimport { SetRootElementContext } from \"./RootElementContext.js\";\nimport { ConnectionManagerCtx } from \"./connection-manager.js\";\nimport { invalidateWalletBalance } from \"./invalidateWalletBalance.js\";\n\n/**\n * @internal\n */\nexport function ThirdwebProviderCore(props: {\n  manager: ConnectionManager;\n  children: React.ReactNode;\n}) {\n  const [el, setEl] = useState<React.ReactNode>(null);\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          mutations: {\n            onSettled: (data, _error, variables) => {\n              if (isBaseTransactionOptions(variables)) {\n                if (\n                  isObjectWithKeys(data, [\"transactionHash\"]) &&\n                  isObjectWithKeys(variables, [\"client\", \"chain\"])\n                ) {\n                  waitForReceipt({\n                    transactionHash: data.transactionHash as Hex, // We know it exists from the if\n                    client: variables.client,\n                    chain: variables.chain,\n                  })\n                    .catch((e) => {\n                      // swallow errors for receipts, but log\n                      console.error(\"[Transaction Error]\", e);\n                    })\n                    .then(() => {\n                      return Promise.all([\n                        queryClient.invalidateQueries({\n                          queryKey:\n                            // invalidate any readContract queries for this chainId:contractAddress\n                            [\n                              \"readContract\",\n                              variables.__contract?.chain.id ||\n                                variables.chain.id,\n                              variables.__contract?.address || variables.to,\n                            ] as const,\n                        }),\n                        invalidateWalletBalance(\n                          queryClient,\n                          variables.__contract?.chain.id || variables.chain.id,\n                        ),\n                      ]);\n                    });\n                }\n              }\n            },\n          },\n          queries: {\n            // With SSR, we usually want to set some default staleTime\n            // above 0 to avoid refetching immediately on the client\n            staleTime: 60 * 1000,\n            structuralSharing,\n          },\n        },\n      }),\n  );\n\n  return (\n    <ConnectionManagerCtx.Provider value={props.manager}>\n      <QueryClientProvider client={queryClient}>\n        <SetRootElementContext.Provider value={setEl}>\n          {props.children}\n        </SetRootElementContext.Provider>\n        {el}\n      </QueryClientProvider>\n    </ConnectionManagerCtx.Provider>\n  );\n}\n", "import { replaceEqualDeep } from \"@tanstack/react-query\";\n\n/** Forked from https://github.com/epoberezkin/fast-deep-equal */\n// biome-ignore lint/suspicious/noExplicitAny: This function by nature takes any object\nfunction deepEqual(a: any, b: any) {\n  if (a === b) return true;\n\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    if (a.constructor !== b.constructor) return false;\n\n    let length: number;\n    let i: number;\n\n    if (Array.isArray(a) && Array.isArray(b)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0; ) if (!deepEqual(a[i], b[i])) return false;\n      return true;\n    }\n\n    if (a.valueOf !== Object.prototype.valueOf)\n      return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString)\n      return a.toString() === b.toString();\n\n    const keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0; )\n      // biome-ignore lint/style/noNonNullAssertion: We know its there\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i]!)) return false;\n\n    for (i = length; i-- !== 0; ) {\n      const key = keys[i];\n\n      if (key && !deepEqual(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  // biome-ignore lint/suspicious/noSelfCompare: <explanation>\n  return a !== a && b !== b;\n}\n\nexport function structuralSharing<T>(oldData: T | undefined, newData: T) {\n  if (deepEqual(oldData, newData)) {\n    return oldData as T;\n  }\n  return replaceEqualDeep(oldData, newData) as T;\n}\n", "import { useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport { isEcosystemWallet } from \"../../../../wallets/ecosystem/is-ecosystem-wallet.js\";\nimport type { AuthArgsType } from \"../../../../wallets/in-app/core/authentication/types.js\";\nimport type { Ecosystem } from \"../../../../wallets/in-app/core/wallet/types.js\";\nimport { linkProfile } from \"../../../../wallets/in-app/web/lib/auth/index.js\";\nimport { useConnectedWallets } from \"../../../core/hooks/wallets/useConnectedWallets.js\";\n\n/**\n * Links a web2 or web3 profile to the connected in-app or ecosystem account.\n * **When a profile is linked to the account, that profile can then be used to sign into the same account.**\n *\n * @example\n *\n * ### Linking a social profile\n *\n * ```jsx\n * import { useLinkProfile } from \"thirdweb/react\";\n *\n * const { mutate: linkProfile } = useLinkProfile();\n *\n * const onClick = () => {\n *   linkProfile({\n *     client,\n *     strategy: \"discord\", // or \"google\", \"x\", \"telegram\", etc\n *   });\n * };\n * ```\n *\n * ### Linking an email\n *\n * ```jsx\n * import { useLinkProfile } from \"thirdweb/react\";\n * import { preAuthenticate } from \"thirdweb/wallets\";\n *\n * const { mutate: linkProfile } = useLinkProfile();\n *\n * // send a verification email first\n * const sendEmail = async () => {\n *   const email = await preAuthenticate({\n *     client,\n *     strategy: \"email\",\n *     email: \"<EMAIL>\",\n *   });\n * };\n *\n * // then link the profile with the verification code\n * const onClick = (code: string) => {\n *   linkProfile({\n *     client,\n *     strategy: \"email\",\n *     email: \"<EMAIL>\",\n *     verificationCode: code,\n *   });\n * };\n * ```\n *\n * The same process can be used for phone and email, simply swap out the `strategy` parameter.\n *\n * ### Linking a wallet\n *\n * ```jsx\n * import { useLinkProfile } from \"thirdweb/react\";\n *\n * const { mutate: linkProfile } = useLinkProfile();\n *\n * const onClick = () => {\n *   linkProfile({\n *     client,\n *     strategy: \"wallet\",\n *     wallet: createWallet(\"io.metamask\"), // autocompletion for 400+ wallet ids\n *     chain: sepolia, // any chain works, needed for SIWE signature\n *   });\n * };\n * ```\n *\n * @wallet\n */\nexport function useLinkProfile() {\n  const wallets = useConnectedWallets();\n  const queryClient = useQueryClient();\n  return useMutation({\n    mutationKey: [\"profiles\"],\n    mutationFn: async (options: AuthArgsType) => {\n      const ecosystemWallet = wallets.find((w) => isEcosystemWallet(w));\n      const ecosystem: Ecosystem | undefined = ecosystemWallet\n        ? {\n            id: ecosystemWallet.id,\n            partnerId: ecosystemWallet.getConfig()?.partnerId,\n          }\n        : undefined;\n      const optionsWithEcosystem = { ...options, ecosystem } as AuthArgsType;\n      return linkProfile(optionsWithEcosystem);\n    },\n    onSuccess() {\n      setTimeout(() => {\n        queryClient.invalidateQueries({ queryKey: [\"profiles\"] });\n      }, 500);\n    },\n  });\n}\n", "import { getAddress } from \"../../utils/address.js\";\nimport type { Prettify } from \"../../utils/type-utils.js\";\nimport {\n  type CoinbaseWalletCreationOptions,\n  isCoinbaseSDKWallet,\n} from \"../coinbase/coinbase-web.js\";\nimport { isInAppWallet } from \"../in-app/core/wallet/index.js\";\nimport { getInjectedProvider } from \"../injected/index.js\";\nimport type { Ethereum } from \"../interfaces/ethereum.js\";\nimport type { Wallet } from \"../interfaces/wallet.js\";\nimport { isWalletConnect } from \"../wallet-connect/controller.js\";\nimport type { WalletId } from \"../wallet-types.js\";\nimport type { WalletCapabilities, WalletCapabilitiesRecord } from \"./types.js\";\n\nexport type GetCapabilitiesOptions<ID extends WalletId = WalletId> = {\n  wallet: Wallet<ID>;\n  chainId?: number;\n};\n\nexport type GetCapabilitiesResult = Prettify<\n  WalletCapabilitiesRecord<WalletCapabilities, number>\n>;\n\n/**\n * Get the capabilities of a wallet based on the [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792) specification.\n *\n *  This function is dependent on the wallet's support for EIP-5792, but will not throw.\n * **The returned object contains a `message` field detailing any issues with the wallet's support for EIP-5792.**\n *\n * @param {GetCapabilitiesOptions} options\n * @param {Wallet} options.wallet - The wallet to get the capabilities of.\n * @returns {Promise<GetCapabilitiesResult>} - A promise that resolves to the capabilities of the wallet based on the [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792) spec.\n * @beta\n * @example\n * ```ts\n * import { getCapabilities } from \"thirdweb/wallets/eip5792\";\n *\n * const wallet = createWallet(\"com.coinbase.wallet\");\n * const capabilities = await getCapabilities({ wallet });\n * ```\n *\n * @extension EIP5792\n */\nexport async function getCapabilities<const ID extends WalletId = WalletId>({\n  wallet,\n  chainId,\n}: GetCapabilitiesOptions<ID>): Promise<GetCapabilitiesResult> {\n  const account = wallet.getAccount();\n  if (!account) {\n    return {\n      message: `Can't get capabilities, no account connected for wallet: ${wallet.id}`,\n    };\n  }\n\n  if (wallet.id === \"smart\") {\n    const { smartWalletGetCapabilities } = await import(\n      \"../smart/lib/smart-wallet-capabilities.js\"\n    );\n    return smartWalletGetCapabilities({ wallet });\n  }\n\n  if (isInAppWallet(wallet)) {\n    const { inAppWalletGetCapabilities } = await import(\n      \"../in-app/core/eip5972/in-app-wallet-capabilities.js\"\n    );\n    return inAppWalletGetCapabilities({ wallet });\n  }\n\n  // TODO: Add Wallet Connect support\n  if (isWalletConnect(wallet)) {\n    return {\n      message: \"getCapabilities is not yet supported with Wallet Connect\",\n    };\n  }\n\n  let provider: Ethereum;\n  if (isCoinbaseSDKWallet(wallet)) {\n    const { getCoinbaseWebProvider } = await import(\n      \"../coinbase/coinbase-web.js\"\n    );\n    const config = wallet.getConfig() as CoinbaseWalletCreationOptions;\n    provider = (await getCoinbaseWebProvider(config)) as Ethereum;\n  } else {\n    provider = getInjectedProvider(wallet.id);\n  }\n\n  try {\n    const result = await provider.request({\n      method: \"wallet_getCapabilities\",\n      params: [getAddress(account.address)],\n    });\n    const capabilities = {} as WalletCapabilitiesRecord<\n      WalletCapabilities,\n      number\n    >;\n    for (const [chainId, capabilities_] of Object.entries(result)) {\n      capabilities[Number(chainId)] = {};\n      const capabilitiesCopy = {} as WalletCapabilities;\n      for (const [key, value] of Object.entries(capabilities_)) {\n        capabilitiesCopy[key] = value;\n      }\n      capabilities[Number(chainId)] = capabilitiesCopy;\n    }\n    return (\n      typeof chainId === \"number\" ? capabilities[chainId] : capabilities\n    ) as never;\n  } catch (error: unknown) {\n    if (/unsupport|not support|not available/i.test((error as Error).message)) {\n      return {\n        message: `${wallet.id} does not support wallet_getCapabilities, reach out to them directly to request EIP-5792 support.`,\n      };\n    }\n    throw error;\n  }\n}\n", "import { type UseQueryResult, useQuery } from \"@tanstack/react-query\";\nimport {\n  type GetCapabilitiesResult,\n  getCapabilities,\n} from \"../../../../wallets/eip5792/get-capabilities.js\";\nimport { useActiveWallet } from \"./useActiveWallet.js\";\n\n/**\n * A hook to get the current wallet's capabilities according to [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792).\n *\n *  This function is dependent on the wallet's support for EIP-5792, but will not throw.\n * **The returned object contains a `message` field detailing any issues with the wallet's support for EIP-5792.**\n *\n * @returns a React Query object.\n * @beta\n * @example\n * ```tsx\n * import { useCapabilities } from \"thirdweb/react\";\n * const { data: capabilities, isLoading } = useCapabilities();\n * ```\n * @extension EIP5792\n */\nexport function useCapabilities(options?: {\n  chainId?: number;\n  queryOptions?: {\n    enabled?: boolean;\n    retry?: number;\n  };\n}): UseQueryResult<GetCapabilitiesResult> {\n  const wallet = useActiveWallet();\n  return useQuery({\n    queryKey: [\"getCapabilities\", wallet?.id, options?.chainId] as const,\n    queryFn: async () => {\n      if (!wallet) {\n        return {\n          message: \"Can't get capabilities, no wallet connected\",\n        } as const;\n      }\n      return getCapabilities({\n        wallet,\n        chainId: options?.chainId,\n      });\n    },\n    retry: false,\n    ...options?.queryOptions,\n  });\n}\n", "import type { <PERSON><PERSON>, AbiFunction } from \"abitype\";\nimport type { WalletSendCallsParameters as ViemWalletSendCallsParameters } from \"viem\";\nimport type { Chain } from \"../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { encode } from \"../../transaction/actions/encode.js\";\nimport type { PreparedTransaction } from \"../../transaction/prepare-transaction.js\";\nimport { type Address, getAddress } from \"../../utils/address.js\";\nimport { type Hex, numberToHex } from \"../../utils/encoding/hex.js\";\nimport { stringify } from \"../../utils/json.js\";\nimport {\n  type PromisedObject,\n  resolvePromisedValue,\n} from \"../../utils/promise/resolve-promised-value.js\";\nimport type { OneOf, Prettify } from \"../../utils/type-utils.js\";\nimport {\n  type CoinbaseWalletCreationOptions,\n  isCoinbaseSDKWallet,\n} from \"../coinbase/coinbase-web.js\";\nimport { isInAppWallet } from \"../in-app/core/wallet/index.js\";\nimport { getInjectedProvider } from \"../injected/index.js\";\nimport type { Ethereum } from \"../interfaces/ethereum.js\";\nimport type { Wallet } from \"../interfaces/wallet.js\";\nimport { isSmartWallet } from \"../smart/index.js\";\nimport { isWalletConnect } from \"../wallet-connect/controller.js\";\nimport type { WalletId } from \"../wallet-types.js\";\nimport type {\n  EIP5792Call,\n  WalletSendCallsId,\n  WalletSendCallsParameters,\n} from \"./types.js\";\n\ntype WalletCall = OneOf<{\n  to?: string | undefined; // TODO: Make this required but compatible with StaticPrepareTransactionOptions to prevent runtime error\n  data?: Hex | undefined;\n  value?: bigint | undefined;\n}>;\n\nexport type PreparedSendCall<\n  abi extends Abi = [],\n  abiFunction extends AbiFunction = AbiFunction,\n> = PreparedTransaction<abi, abiFunction, PrepareCallOptions>;\n\nexport type PrepareCallOptions = {\n  chain: Chain;\n  client: ThirdwebClient;\n} & PromisedObject<WalletCall>;\n\nexport type SendCallsOptions<\n  ID extends WalletId = WalletId,\n  abi extends Abi = [],\n  abiFunction extends AbiFunction = AbiFunction,\n> = Prettify<{\n  wallet: Wallet<ID>;\n  calls: PreparedSendCall<abi, abiFunction>[];\n  capabilities?: WalletSendCallsParameters[number][\"capabilities\"];\n  version?: WalletSendCallsParameters[number][\"version\"];\n  chain?: Chain;\n  atomicRequired?: boolean;\n}>;\n\nexport type SendCallsResult = Prettify<{\n  id: WalletSendCallsId;\n  client: ThirdwebClient;\n  chain: Chain;\n  wallet: Wallet;\n}>;\n\n/**\n * Send [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792) calls to a wallet.\n * This function works with all Thirdweb wallets (in-app and smart) and certain injected wallets that already support EIP-5792.\n * Transactions will be bundled and sponsored when those capabilities are supported, otherwise they will be sent as individual transactions.\n *\n *  This function is dependent on the wallet's support for EIP-5792 and could fail.\n *\n * @param {SendCallsOptions} options\n * @param {Wallet} options.wallet - The wallet to send the calls to.\n * @param {PreparedSendCall[]} options.calls - An array of prepared transactions to send.\n * @param {WalletSendCallsParameters[number][\"capabilities\"]} [options.capabilities] - Capabilities objects to use, see the [EIP-5792 spec](https://eips.ethereum.org/EIPS/eip-5792) for details.\n * @param {string} [options.version=\"1.0\"] - The `wallet_sendCalls` version to use, defaults to \"1.0\".\n * @param {Chain} [options.chain] - A {@link Chain} instance to override the wallet's current chain.\n * @throws an error if the wallet does not support EIP-5792.\n * @returns The ID of the bundle of the calls.\n *\n * @see getCallsStatus for how to retrieve the status of the bundle.\n * @see getCapabilities for how to retrieve the capabilities of the wallet.\n * @beta\n * @example\n * ```ts\n * import { createThirdwebClient } from \"thirdweb\";\n * import { sendCalls } from \"thirdweb/wallets/eip5792\";\n *\n * const client = createThirdwebClient({ clientId: ... });\n * const wallet = createWallet(\"com.coinbase.wallet\");\n * await wallet.connect({ client });\n *\n * const sendTx1 = approve({\n      contract: USDT_CONTRACT,\n      amount: 100,\n      spender: \"******************************************\",\n    });\n * const sendTx2 = approve({\n      contract: USDT_CONTRACT,\n      amount: 100,\n      spender: \"******************************************\",\n    });\n * const bundleId = await sendCalls({\n *   wallet,\n *   client,\n *   calls: [sendTx1, sendTx2],\n * });\n * ```\n * Sponsor transactions with a paymaster:\n * ```ts\n * const bundleId = await sendCalls({\n *   wallet,\n *   client,\n *   calls: [send1, send2],\n *   capabilities: {\n *     paymasterService: {\n *       url: `https://${CHAIN.id}.bundler.thirdweb.com/${client.clientId}`\n *     }\n *   }\n * });\n * ```\n * We recommend proxying any paymaster calls via an API route you setup and control.\n * \n * @extension EIP5792\n */\nexport async function sendCalls<const ID extends WalletId>(\n  options: SendCallsOptions<ID>,\n): Promise<SendCallsResult> {\n  const {\n    wallet,\n    calls,\n    capabilities,\n    version = \"2.0.0\",\n    chain = wallet.getChain(),\n  } = options;\n\n  if (!chain) {\n    throw new Error(\n      `Cannot send calls, no active chain found for wallet: ${wallet.id}`,\n    );\n  }\n\n  const account = wallet.getAccount();\n  if (!account) {\n    throw new Error(\n      `Cannot send calls, no account connected for wallet: ${wallet.id}`,\n    );\n  }\n\n  const firstCall = options.calls[0];\n  if (!firstCall) {\n    throw new Error(\"No calls to send\");\n  }\n  const client = firstCall.client;\n\n  // These conveniently operate the same\n  if (isSmartWallet(wallet) || isInAppWallet(wallet)) {\n    const { inAppWalletSendCalls } = await import(\n      \"../in-app/core/eip5972/in-app-wallet-calls.js\"\n    );\n    const id = await inAppWalletSendCalls({ account, calls });\n    return { id, client, chain, wallet };\n  }\n\n  const preparedCalls: EIP5792Call[] = await Promise.all(\n    calls.map(async (call) => {\n      const { to, value } = call;\n      if (to === undefined && call.data === undefined) {\n        throw new Error(\"Cannot send call, `to` or `data` must be provided.\");\n      }\n\n      const [_to, _data, _value] = await Promise.all([\n        resolvePromisedValue(to),\n        encode(call),\n        resolvePromisedValue(value),\n      ]);\n\n      return {\n        to: _to as Address,\n        data: _data as Hex,\n        value:\n          typeof _value === \"bigint\" || typeof _value === \"number\"\n            ? numberToHex(_value)\n            : undefined,\n      };\n    }),\n  );\n\n  const injectedWalletCallParams: WalletSendCallsParameters = [\n    {\n      from: getAddress(account.address),\n      calls: preparedCalls,\n      capabilities,\n      version,\n      chainId: numberToHex(chain.id),\n      // see: https://eips.ethereum.org/EIPS/eip-5792#wallet_sendcalls\n      atomicRequired: options.atomicRequired ?? false,\n    },\n  ];\n\n  if (isWalletConnect(wallet)) {\n    throw new Error(\"sendCalls is not yet supported for Wallet Connect\");\n  }\n\n  let provider: Ethereum;\n  if (isCoinbaseSDKWallet(wallet)) {\n    const { getCoinbaseWebProvider } = await import(\n      \"../coinbase/coinbase-web.js\"\n    );\n    const config = wallet.getConfig() as CoinbaseWalletCreationOptions;\n    provider = (await getCoinbaseWebProvider(config)) as Ethereum;\n  } else {\n    provider = getInjectedProvider(wallet.id);\n  }\n\n  try {\n    const callId = await provider.request({\n      method: \"wallet_sendCalls\",\n      params: injectedWalletCallParams as ViemWalletSendCallsParameters, // The viem type definition is slightly different\n    });\n    if (typeof callId === \"object\" && \"id\" in callId) {\n      return { id: callId.id, client, chain, wallet };\n    }\n    return { id: callId, client, chain, wallet };\n  } catch (error) {\n    if (/unsupport|not support/i.test((error as Error).message)) {\n      throw new Error(\n        `${wallet.id} errored calling wallet_sendCalls, with error: ${error instanceof Error ? error.message : stringify(error)}`,\n      );\n    }\n    throw error;\n  }\n}\n", "import type { ThirdwebClient } from \"../../client/client.js\";\nimport { hexToBigInt } from \"../../utils/encoding/hex.js\";\nimport { hexToNumber } from \"../../utils/encoding/hex.js\";\nimport { isCoinbaseSDKWallet } from \"../coinbase/coinbase-web.js\";\nimport { isInAppWallet } from \"../in-app/core/wallet/index.js\";\nimport { getInjectedProvider } from \"../injected/index.js\";\nimport type { Ethereum } from \"../interfaces/ethereum.js\";\nimport type { Wallet } from \"../interfaces/wallet.js\";\nimport { isSmartWallet } from \"../smart/index.js\";\nimport { isWalletConnect } from \"../wallet-connect/controller.js\";\nimport type {\n  GetCallsStatusRawResponse,\n  GetCallsStatusResponse,\n  WalletSendCallsId,\n} from \"./types.js\";\n\nexport type GetCallsStatusOptions = {\n  wallet: Wallet;\n  client: ThirdwebClient;\n  id: WalletSendCallsId;\n};\n\n/**\n * Get the status of an [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792) bundle.\n *\n *  This function is dependent on the wallet's support for EIP-5792 and could fail.\n *\n * @param {GetCallsStatusOptions} options\n * @param {Wallet} options.wallet - The wallet that send the original calls.\n * @param {ThirdwebClient} options.client - A {@link ThirdwebClient} instance.\n * @param {WalletSendCallsId} options.bundleId - The ID of the bundle to get the status of.\n * @throws an error if the wallet does not support EIP-5792.\n * @returns {Promise<GetCallsStatusResponse>} - A promise that resolves to the bundle's status and receipts (if available). {@link GetCallsStatusResponse}\n * @beta\n * @example\n * ```ts\n *  import { createThirdwebClient } from \"thirdweb\";\n *  import { sendCalls, getCallsStatus } from \"thirdweb/wallets/eip5792\";\n *\n *  const client = createThirdwebClient({ clientId: ... });\n *\n *  const result = await sendCalls({ wallet, client, calls });\n *\n *  let result;\n *  while (result.status !== \"success\") {\n *    result = await getCallsStatus(result);\n *  }\n * ```\n * @extension EIP5792\n */\nexport async function getCallsStatus({\n  wallet,\n  client,\n  id,\n}: GetCallsStatusOptions): Promise<GetCallsStatusResponse> {\n  const account = wallet.getAccount();\n  if (!account) {\n    throw new Error(\n      `Failed to get call status, no account found for wallet ${wallet.id}`,\n    );\n  }\n\n  // These conveniently operate the same\n  if (isSmartWallet(wallet) || isInAppWallet(wallet)) {\n    const { inAppWalletGetCallsStatus } = await import(\n      \"../in-app/core/eip5972/in-app-wallet-calls.js\"\n    );\n    return inAppWalletGetCallsStatus({ wallet, client, id });\n  }\n\n  if (isWalletConnect(wallet)) {\n    throw new Error(\"getCallsStatus is not yet supported for Wallet Connect\");\n  }\n\n  let provider: Ethereum;\n  if (isCoinbaseSDKWallet(wallet)) {\n    const { getCoinbaseWebProvider } = await import(\n      \"../coinbase/coinbase-web.js\"\n    );\n    const config = wallet.getConfig();\n    provider = (await getCoinbaseWebProvider(config)) as Ethereum;\n  } else {\n    provider = getInjectedProvider(wallet.id);\n  }\n\n  try {\n    const {\n      atomic = false,\n      chainId,\n      receipts,\n      version = \"2.0.0\",\n      ...response\n    } = (await provider.request({\n      method: \"wallet_getCallsStatus\",\n      params: [id],\n    })) as GetCallsStatusRawResponse;\n    const [status, statusCode] = (() => {\n      const statusCode = response.status;\n      if (statusCode >= 100 && statusCode < 200)\n        return [\"pending\", statusCode] as const;\n      if (statusCode >= 200 && statusCode < 300)\n        return [\"success\", statusCode] as const;\n      if (statusCode >= 300 && statusCode < 700)\n        return [\"failure\", statusCode] as const;\n      // @ts-expect-error: for backwards compatibility\n      if (statusCode === \"CONFIRMED\") return [\"success\", 200] as const;\n      // @ts-expect-error: for backwards compatibility\n      if (statusCode === \"PENDING\") return [\"pending\", 100] as const;\n      return [undefined, statusCode];\n    })();\n    return {\n      ...response,\n      atomic,\n      // @ts-expect-error: for backwards compatibility\n      chainId: chainId ? hexToNumber(chainId) : undefined,\n      receipts:\n        receipts?.map((receipt) => ({\n          ...receipt,\n          blockNumber: hexToBigInt(receipt.blockNumber),\n          gasUsed: hexToBigInt(receipt.gasUsed),\n          status: receiptStatuses[receipt.status as \"0x0\" | \"0x1\"],\n        })) ?? [],\n      statusCode,\n      status,\n      version,\n    };\n  } catch (error) {\n    if (/unsupport|not support/i.test((error as Error).message)) {\n      throw new Error(\n        `${wallet.id} does not support wallet_getCallsStatus, reach out to them directly to request EIP-5792 support.`,\n      );\n    }\n    throw error;\n  }\n}\n\nconst receiptStatuses = {\n  \"0x0\": \"reverted\",\n  \"0x1\": \"success\",\n} as const;\n", "import type { Chain } from \"../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { watchBlockNumber } from \"../../rpc/watchBlockNumber.js\";\nimport type { Prettify } from \"../../utils/type-utils.js\";\nimport type { Wallet } from \"../interfaces/wallet.js\";\nimport { getCallsStatus } from \"./get-calls-status.js\";\nimport type { GetCallsStatusResponse } from \"./types.js\";\nconst DEFAULT_MAX_BLOCKS_WAIT_TIME = 100;\n\nconst map = new Map<string, Promise<GetCallsStatusResponse>>();\n\nexport type WaitForCallsReceiptOptions = Prettify<{\n  id: string;\n  client: ThirdwebClient;\n  chain: Chain;\n  wallet: Wallet;\n  maxBlocksWaitTime?: number;\n}>;\n/**\n * Waits for the [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792) bundle to be confirmed.\n *\n *  This function is dependent on the wallet's support for EIP-5792 and could fail.\n *\n * @param options - The options for waiting for the bundle.\n * By default, the max wait time is 100 blocks.\n * @returns A promise that resolves with the final {@link getCallsStatus} result.\n * @throws an error if the wallet does not support EIP-5792.\n * @beta\n * @example\n * ```ts\n * import { waitForCallsReceipt } from \"thirdweb/wallets/eip5792\";\n * const result = await waitForCallsReceipt({\n *   client,\n *   chain,\n *   wallet,\n *   id: \"0x123...\",\n * });\n * ```\n *\n * Example with useSendCalls:\n * ```ts\n * const sendResult = await sendCalls({\n *   client,\n *   chain,\n *   wallet,\n *   calls: [...],\n * });\n * const confirmResult = await waitForCallsReceipt(sendResult);\n * console.log(\"Transaction confirmed: \", confirmResult.receipts?.[0].transactionHash);\n * ```\n * @extension EIP5792\n */\nexport function waitForCallsReceipt(\n  options: WaitForCallsReceiptOptions,\n): Promise<GetCallsStatusResponse> {\n  const { id, chain, wallet, client } = options;\n\n  const chainId = chain.id;\n  const key = `${chainId}:calls_${id}`;\n  const maxBlocksWaitTime =\n    options.maxBlocksWaitTime ?? DEFAULT_MAX_BLOCKS_WAIT_TIME;\n\n  if (map.has(key)) {\n    // biome-ignore lint/style/noNonNullAssertion: the `has` above ensures that this will always be set\n    return map.get(key)!;\n  }\n  const promise = new Promise<GetCallsStatusResponse>((resolve, reject) => {\n    // start at -1 because the first block doesn't count\n    let blocksWaited = -1;\n\n    const unwatch = watchBlockNumber({\n      client: client,\n      chain: chain,\n      onNewBlockNumber: async () => {\n        blocksWaited++;\n        if (blocksWaited >= maxBlocksWaitTime) {\n          unwatch();\n          reject(\n            new Error(`Bundle not confirmed after ${maxBlocksWaitTime} blocks`),\n          );\n          return;\n        }\n        try {\n          const result = await getCallsStatus({\n            wallet,\n            client,\n            id,\n          });\n          if (result.status === \"success\" || result.status === \"failure\") {\n            // stop the polling\n            unwatch();\n            // resolve the top level promise with the result\n            resolve(result);\n            return;\n          }\n        } catch {\n          // noop, we'll try again on the next blocks\n        }\n      },\n    });\n    // remove the promise from the map when it's done (one way or the other)\n  }).finally(() => {\n    map.delete(key);\n  });\n\n  map.set(key, promise);\n  return promise;\n}\n", "import {\n  type UseMutationResult,\n  useMutation,\n  useQueryClient,\n} from \"@tanstack/react-query\";\nimport {\n  type SendCallsOptions,\n  type SendCallsResult,\n  sendCalls,\n} from \"../../../../wallets/eip5792/send-calls.js\";\nimport { waitForCallsReceipt } from \"../../../../wallets/eip5792/wait-for-calls-receipt.js\";\nimport type { Wallet } from \"../../../../wallets/interfaces/wallet.js\";\nimport { invalidateWalletBalance } from \"../../providers/invalidateWalletBalance.js\";\nimport { useActiveWallet } from \"./useActiveWallet.js\";\n\n/**\n * A hook to send [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792) calls to a wallet.\n * This hook works with all Thirdweb wallets (in-app and smart) and certain injected wallets that already support EIP-5792.\n * Transactions will be bundled and sponsored when those capabilities are supported, otherwise they will be sent as individual transactions.\n *\n * When calls are sent, all contracts that are interacted with will have their corresponding reads revalidated via React Query.\n *\n *  This hook is dependent on the wallet's support for EIP-5792 and could fail.\n *  The mutatuon function will use your currently connected wallet by default, but you can pass it a specific wallet to use if you'd like.\n *\n * @returns A React Query mutatuon object to interact with {@link sendCalls}\n * @throws an error if the wallet does not support EIP-5792.\n * @returns The ID of the bundle of the calls.\n *\n * @beta\n * @example\n * ```tsx\n * import { useSendCalls } from \"thirdweb/react\";\n *\n * const sendTx1 = approve({\n      contract: USDT_CONTRACT,\n      amount: 100,\n      spender: \"******************************************\",\n    });\n * const sendTx2 = approve({\n      contract: USDT_CONTRACT,\n      amount: 100,\n      spender: \"******************************************\",\n    });\n * const { mutate: sendCalls, data: id } = useSendCalls();\n * await sendCalls({\n *   wallet,\n *   client,\n *   calls: [sendTx1, sendTx2],\n * });\n * ```\n * Await the bundle's full confirmation:\n * ```tsx\n * const { mutate: sendCalls, data } = useSendCalls();\n * const { data: result, isLoading } = useWaitForCallsReceipt(data); \n * await sendCalls({\n *   wallet,\n *   client,\n *   calls: [sendTx1, sendTx2],\n * });\n * \n * console.log(\"Receipts:\", result.receipts);\n * ```\n * Sponsor transactions with a paymaster:\n * ```ts\n * const { mutate: sendCalls, data: id } = useSendCalls();\n * await sendCalls({\n *   client,\n *   calls: [sendTx1, sendTx2],\n *   capabilities: {\n *     paymasterService: {\n *       url: `https://${CHAIN.id}.bundler.thirdweb.com/${client.clientId}`\n *     }\n *   }\n * });\n * ```\n *  We recommend proxying any paymaster calls via an API route you setup and control.\n * @extension EIP5792\n */\nexport function useSendCalls(): UseMutationResult<\n  SendCallsResult,\n  Error,\n  Omit<SendCallsOptions, \"chain\" | \"wallet\"> & { wallet?: Wallet } // Optional wallet override\n> {\n  const activeWallet = useActiveWallet();\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: async (options) => {\n      const { wallet = activeWallet } = options;\n      const chain = wallet?.getChain();\n      if (!wallet || !chain) {\n        throw new Error(\n          \"Failed to send transactions, no connected wallet found.\",\n        );\n      }\n\n      return sendCalls({ ...options, wallet });\n    },\n    onSettled: async (result, _error, variables) => {\n      // Attempt to invalidate any reads related to the sent transactions\n      if (!result) {\n        return;\n      }\n      const call = variables.calls[0];\n      if (!call) {\n        return;\n      }\n      const chain = call.__contract?.chain || call.chain;\n\n      waitForCallsReceipt(result)\n        .then(() => {\n          for (const call of variables.calls) {\n            queryClient.invalidateQueries({\n              queryKey: [\n                \"readContract\",\n                call.__contract?.chain.id || chain.id,\n                call.__contract?.address || call.to,\n              ],\n            });\n          }\n          invalidateWalletBalance(queryClient, chain.id);\n        })\n        .catch((error) => {\n          console.error(\n            \"Failed to confirm sent bundle and invalidate queries\",\n            result,\n            error,\n          );\n          return undefined;\n        });\n    },\n  });\n}\n", "import type { WalletId } from \"../wallet-types.js\";\nimport { type SendCallsOptions, sendCalls } from \"./send-calls.js\";\nimport type { GetCallsStatusResponse } from \"./types.js\";\nimport { waitForCallsReceipt } from \"./wait-for-calls-receipt.js\";\n\n/**\n * Send and confirm calls in a single transaction.\n *\n * This is a convenience function that sends the calls with `sendCalls` and then waits for the receipts with `waitForCallsReceipt`.\n *\n * @param options - The options for sending and confirming calls.\n * @returns The receipts of the calls.\n * @example\n * ```ts\n * const call1 = approve({\n *   contract: USDT_CONTRACT,\n *   amount: 100,\n *   spender: \"******************************************\",\n * });\n * const call2 = transfer({\n *   contract: USDT_CONTRACT,\n *   to: \"******************************************\",\n *   amount: 100,\n * });\n * const result = await sendAndConfirmCalls({\n *   calls: [call1, call2],\n *   wallet: wallet,\n * });\n * console.log(\"Transaction receipts:\", result.receipts);\n * ```\n * @extension EIP5792\n * @beta\n */\nexport async function sendAndConfirmCalls<const ID extends WalletId>(\n  options: SendCallsOptions<ID> & {\n    /**\n     * The maximum number of blocks to wait for the calls to be confirmed.\n     * @defaultValue 100\n     */\n    maxBlocksWaitTime?: number;\n  },\n): Promise<GetCallsStatusResponse> {\n  const sendCallsResult = await sendCalls(options);\n  return waitForCallsReceipt({\n    ...sendCallsResult,\n    maxBlocksWaitTime: options.maxBlocksWaitTime,\n  });\n}\n", "import {\n  type UseMutationResult,\n  useMutation,\n  useQueryClient,\n} from \"@tanstack/react-query\";\nimport { sendAndConfirmCalls } from \"../../../../wallets/eip5792/send-and-confirm-calls.js\";\nimport type { SendCallsOptions } from \"../../../../wallets/eip5792/send-calls.js\";\nimport type { GetCallsStatusResponse } from \"../../../../wallets/eip5792/types.js\";\nimport type { Wallet } from \"../../../../wallets/interfaces/wallet.js\";\nimport { invalidateWalletBalance } from \"../../providers/invalidateWalletBalance.js\";\nimport { useActiveWallet } from \"./useActiveWallet.js\";\n\n/**\n * A hook to send [EIP-5792](https://eips.ethereum.org/EIPS/eip-5792) calls to a wallet.\n * This hook works with all Thirdweb wallets (in-app and smart) and certain injected wallets that already support EIP-5792.\n * Transactions will be batched and sponsored when those capabilities are supported, otherwise they will be sent as individual transactions.\n *\n * When calls are sent, all contracts that are interacted with will have their corresponding reads revalidated via React Query.\n *\n * This hook is dependent on the wallet's support for EIP-5792 and could fail.\n * The mutation function will use your currently connected wallet by default, but you can pass it a specific wallet to use if you'd like.\n *\n * @returns A React Query mutation object to interact with {@link sendAndConfirmCalls}\n * @throws an error if the wallet does not support EIP-5792.\n * @returns The ID of the bundle of the calls.\n *\n * @beta\n * @example\n * ```tsx\n * import { useSendCalls } from \"thirdweb/react\";\n *\n * const sendTx1 = approve({\n      contract: USDT_CONTRACT,\n      amount: 100,\n      spender: \"******************************************\",\n    });\n * const sendTx2 = approve({\n      contract: USDT_CONTRACT,\n      amount: 100,\n      spender: \"******************************************\",\n    });\n * const { mutate: sendCalls, data: result } = useSendAndConfirmCalls();\n * await sendCalls({\n *   client,\n *   calls: [sendTx1, sendTx2],\n * });\n * \n * console.log(\"Transaction hash:\", result.receipts?.[0]?.transactionHash);\n * ```\n\n * Sponsor transactions with a paymaster:\n * ```ts\n * const { mutate: sendAndConfirmCalls, data: id } = useSendAndConfirmCalls();\n * const result = await sendAndConfirmCalls({\n *   client,\n *   calls: [sendTx1, sendTx2],\n *   capabilities: {\n *     paymasterService: {\n *       url: `https://${CHAIN.id}.bundler.thirdweb.com/${client.clientId}`\n *     }\n *   }\n * });\n * console.log(\"Transaction hash:\", result.receipts?.[0]?.transactionHash);\n * ```\n *\n *  We recommend proxying any paymaster calls via an API route you setup and control.\n * @extension EIP5792\n */\nexport function useSendAndConfirmCalls(args?: {\n  maxBlocksWaitTime?: number;\n}): UseMutationResult<\n  GetCallsStatusResponse,\n  Error,\n  Omit<SendCallsOptions, \"chain\" | \"wallet\"> & { wallet?: Wallet } // Optional wallet override\n> {\n  const activeWallet = useActiveWallet();\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: async (options) => {\n      const { wallet = activeWallet } = options;\n      if (!wallet) {\n        throw new Error(\n          \"Failed to send transactions, no connected wallet found.\",\n        );\n      }\n\n      return sendAndConfirmCalls({\n        ...options,\n        wallet,\n        maxBlocksWaitTime: args?.maxBlocksWaitTime,\n      });\n    },\n    onSettled: async (_result, _error, variables) => {\n      // Attempt to invalidate any reads related to the sent transactions\n      const call = variables.calls[0];\n      if (!call) {\n        return;\n      }\n      const chain = call.__contract?.chain || call.chain;\n\n      for (const call of variables.calls) {\n        queryClient.invalidateQueries({\n          queryKey: [\n            \"readContract\",\n            call.__contract?.chain.id || call.chain.id,\n            call.__contract?.address || call.to,\n          ],\n        });\n      }\n      invalidateWalletBalance(queryClient, chain.id);\n    },\n  });\n}\n", "import { useQuery } from \"@tanstack/react-query\";\nimport type { SendCallsResult } from \"../../../../wallets/eip5792/send-calls.js\";\nimport { waitForCallsReceipt } from \"../../../../wallets/eip5792/wait-for-calls-receipt.js\";\n\n/**\n * A hook to wait for the receipt of eip5792 calls.\n * @param options - The options for the hook.\n * @returns A useQuery object.\n * @example\n * ```tsx\n * const { data: receipt, isLoading } = useWaitForCallsReceipt({ id, client, chain, wallet });\n * ```\n *\n * Example with useSendCalls:\n * ```tsx\n * const { mutate: sendCalls, data } = useSendCalls();\n * const { data: receipt, isLoading } = useWaitForCallsReceipt(data);\n * ```\n * @extension EIP5792\n */\nexport function useWaitForCallsReceipt(\n  args:\n    | (SendCallsResult & {\n        maxBlocksWaitTime?: number;\n        queryOptions?: { enabled?: boolean };\n      })\n    | undefined,\n) {\n  return useQuery({\n    queryKey: [\"waitForCallsReceipt\", args?.id] as const,\n    queryFn: async () => {\n      if (!args?.id) {\n        throw new Error(\"No call result provided\");\n      }\n      return waitForCallsReceipt({\n        ...args,\n        maxBlocksWaitTime: args.maxBlocksWaitTime,\n      });\n    },\n    enabled: !!args?.id && (args?.queryOptions?.enabled ?? true),\n    retry: false,\n  });\n}\n", "import {\n  type UseQueryResult,\n  queryOptions as defineQ<PERSON>y,\n  useQuery,\n} from \"@tanstack/react-query\";\nimport type { Abi, AbiFunction, ExtractAbiFunctionNames } from \"abitype\";\nimport type {\n  AbiOfLength,\n  AsyncGetAbiFunctionFromContract,\n} from \"../../../../contract/types.js\";\nimport type { Extension } from \"../../../../extensions/types.js\";\nimport {\n  type ReadContractOptions,\n  type ReadContractResult,\n  readContract,\n} from \"../../../../transaction/read-contract.js\";\nimport type {\n  BaseTransactionOptions,\n  ParseMethod,\n} from \"../../../../transaction/types.js\";\nimport type { PreparedMethod } from \"../../../../utils/abi/prepare-method.js\";\nimport { getFunctionId } from \"../../../../utils/function-id.js\";\nimport { stringify } from \"../../../../utils/json.js\";\nimport type {\n  PickedOnceQueryOptions,\n  WithPickedOnceQueryOptions,\n} from \"../types.js\";\n\n/**\n * A hook to read state from a contract that automatically updates when the contract changes.\n *\n * You can use raw read calls or read [extensions](https://portal.thirdweb.com/react/v5/extensions) to read from a\n * contract.\n *\n * @param options - The options for reading from a contract\n * @returns a UseQueryResult object.\n * @example\n * ```jsx\n * import { getContract } from \"thirdweb\";\n * import { sepolia } from \"thirdweb/chains\";\n * import { useReadContract } from \"thirdweb/react\";\n *\n * const contract = getContract({\n *   client,\n *   address: \"0x...\",\n *   chain: sepolia,\n * });\n *\n * const { data, isLoading } = useReadContract({\n *   contract,\n *   method: \"function tokenURI(uint256 tokenId) returns (string)\"\n *   params: [1n],\n * });\n * ```\n * @contract\n */\nexport function useReadContract<\n  const TAbi extends Abi,\n  const TMethod extends TAbi extends AbiOfLength<0>\n    ? AbiFunction | string\n    : ExtractAbiFunctionNames<TAbi>,\n>(\n  options: WithPickedOnceQueryOptions<ReadContractOptions<TAbi, TMethod>>,\n): UseQueryResult<\n  ReadContractResult<PreparedMethod<ParseMethod<TAbi, TMethod>>[2]>\n>;\n/**\n * A hook to read state from a contract that automatically updates when the contract changes.\n * You can use raw read calls or read [extensions](https://portal.thirdweb.com/react/v5/extensions) to read from a\n * contract.\n *\n * @param extension - An extension to call.\n * @param options - The read extension params.\n * @returns a UseQueryResult object.\n * @example\n *\n * Read a contract extension let you do complex contract queries with less code.\n *\n * ```jsx\n * import { useReadContract } from \"thirdweb/react\";\n * import { getOwnedNFTs } form \"thirdweb/extensions/erc721\";\n *\n * const { data, isLoading } = useReadContract(getOwnedNFTs, { contract, owner: address });\n * ```\n */\nexport function useReadContract<\n  const TAbi extends Abi,\n  const TParams extends object,\n  TResult,\n>(\n  extension: Extension<TAbi, TParams, TResult>,\n  options: WithPickedOnceQueryOptions<BaseTransactionOptions<TParams, TAbi>>,\n): UseQueryResult<TResult>;\n\nexport function useReadContract<\n  const TAbi extends Abi,\n  const TMethod extends TAbi extends AbiOfLength<0>\n    ? AbiFunction | `function ${string}` | AsyncGetAbiFunctionFromContract<TAbi>\n    : ExtractAbiFunctionNames<TAbi>,\n  const TParams extends object,\n  TResult,\n>(\n  extensionOrOptions:\n    | Extension<TAbi, TParams, TResult>\n    | WithPickedOnceQueryOptions<ReadContractOptions<TAbi, TMethod>>,\n  options?: WithPickedOnceQueryOptions<BaseTransactionOptions<TParams, TAbi>>,\n) {\n  type QueryKey = readonly [\n    \"readContract\",\n    number | string,\n    string,\n    string | PreparedMethod<ParseMethod<TAbi, TMethod>>,\n    string,\n  ];\n  type QueryFn = () => Promise<\n    TResult | ReadContractResult<PreparedMethod<ParseMethod<TAbi, TMethod>>[2]>\n  >;\n\n  let queryKey: QueryKey | undefined;\n  let queryFn: QueryFn | undefined;\n  let queryOpts: PickedOnceQueryOptions | undefined;\n\n  // extension case\n  if (typeof extensionOrOptions === \"function\") {\n    if (!options) {\n      throw new Error(\n        `Missing second argument for \"useReadContract(<extension>, <options>)\" hook.`,\n      ) as never;\n    }\n    const { queryOptions, contract, ...params } = options;\n    queryOpts = queryOptions;\n\n    queryKey = [\n      \"readContract\",\n      contract.chain.id,\n      contract.address,\n      getFunctionId(extensionOrOptions),\n      stringify(params),\n    ] as const;\n\n    queryFn = () =>\n      extensionOrOptions({\n        ...(params as TParams),\n        contract,\n      });\n  }\n  // raw tx case\n  if (\"method\" in extensionOrOptions) {\n    const { queryOptions, ...tx } = extensionOrOptions;\n    queryOpts = queryOptions;\n\n    queryKey = [\n      \"readContract\",\n      tx.contract.chain.id,\n      tx.contract.address,\n      tx.method,\n      stringify(tx.params),\n    ] as const;\n\n    queryFn = () => readContract(extensionOrOptions);\n  }\n\n  if (!queryKey || !queryFn) {\n    throw new Error(\n      `Invalid \"useReadContract\" options. Expected either a read extension or a transaction object.`,\n    ) as never;\n  }\n\n  return useQuery(\n    defineQuery({\n      queryKey: queryKey as QueryKey,\n      queryFn: queryFn as QueryFn,\n      ...(queryOpts ?? {}),\n    }),\n  );\n}\n", "import {\n  type UseQueryResult,\n  useQuery,\n  useQueryClient,\n} from \"@tanstack/react-query\";\nimport type { Abi, AbiEvent } from \"abitype\";\nimport { useEffect, useMemo, useRef } from \"react\";\nimport { getContractEvents } from \"../../../../event/actions/get-events.js\";\nimport type { ParseEventLogsResult } from \"../../../../event/actions/parse-logs.js\";\nimport {\n  type WatchContractEventsOptions,\n  watchContractEvents,\n} from \"../../../../event/actions/watch-events.js\";\nimport type { PreparedEvent } from \"../../../../event/prepare-event.js\";\nimport { eth_blockNumber } from \"../../../../rpc/actions/eth_blockNumber.js\";\nimport { getRpcClient } from \"../../../../rpc/rpc.js\";\n\ntype UseContractEventsOptions<\n  abi extends Abi,\n  abiEvents extends PreparedEvent<AbiEvent>[],\n> = Omit<WatchContractEventsOptions<abi, abiEvents, true>, \"onEvents\"> & {\n  blockRange?: number;\n  enabled?: boolean;\n  watch?: boolean;\n};\n\n/**\n * Watches contract events and returns the parsed logs.\n * @param options - The options for watching contract events.\n * @param options.contract - The contract to watch events for.\n * @param options.events - The events to watch. Shuould be an array of [prepared events](https://portal.thirdweb.com/references/typescript/v5/prepareEvent).\n * @param options.blockRange - The number of blocks to search for events.\n * @param options.enabled - Whether to enable the query.\n * @param options.watch - Whether to watch for new events.\n * @returns The contract events of the watched contract events.\n * @example\n *\n * ### Using event extensions\n *\n * The `thirdweb/extesions` export contains event definitions for many popular contracts.\n * You can use these event definitions to watch for specific events with a type-safe API.\n *\n * ```jsx\n * import { useContractEvents } from \"thirdweb/react\";\n * import { tokensClaimedEvent } from \"thirdweb/extensions/erc721\";\n *\n * const account = useActiveAccount();\n * const contractEvents = useContractEvents({\n *  contract,\n *  events: [tokensClaimedEvent({ claimer: account?.address })],\n * });\n * ```\n *\n * ### Using custom events\n *\n * You can also watch for custom events by passing an array of [prepared events](https://portal.thirdweb.com/references/typescript/v5/prepareEvent).\n *\n * ```jsx\n * import { useContractEvents } from \"thirdweb/react\";\n * import { prepareEvent } from \"thirdweb\";\n *\n * const myEvent = prepareEvent({\n *  signature: \"event MyEvent(uint256 myArg)\",\n * });\n *\n * const contractEvents = useContractEvents({\n *  contract,\n *  events: [myEvent],\n * });\n * ```\n *\n * @contract\n */\nexport function useContractEvents<\n  const abi extends Abi,\n  const abiEvents extends PreparedEvent<AbiEvent>[],\n>(\n  options: UseContractEventsOptions<abi, abiEvents>,\n): UseQueryResult<ParseEventLogsResult<abiEvents, true>, Error> {\n  const {\n    contract,\n    events,\n    blockRange = 2000,\n    enabled = true,\n    watch = true,\n  } = options;\n  const latestBlockNumber = useRef<bigint>(undefined); // We use this to keep track of the latest block number when new pollers are spawned\n\n  const queryClient = useQueryClient();\n\n  const eventsKey = useMemo(\n    () =>\n      events?.reduce((acc, curr) => {\n        // we can use the event hash as a unique identifier?\n        return `${acc}${curr.hash}_`;\n      }, \"\") || \"__all__\",\n    [events],\n  );\n\n  const queryKey = useMemo(\n    () => [contract.chain.id, contract.address, \"logs\", eventsKey] as const,\n    [contract.address, contract.chain, eventsKey],\n  );\n\n  const query = useQuery({\n    queryKey,\n    queryFn: async () => {\n      const rpcRequest = getRpcClient(contract);\n      const currentBlockNumber = await eth_blockNumber(rpcRequest);\n      latestBlockNumber.current = currentBlockNumber;\n      const initialEvents = await getContractEvents({\n        contract,\n        events: events,\n        fromBlock: currentBlockNumber - BigInt(blockRange),\n      });\n      return initialEvents;\n    },\n    enabled,\n  });\n\n  useEffect(() => {\n    if (!enabled || !watch) {\n      // don't watch if not enabled or if watch is false\n      return;\n    }\n\n    // the return is important here because it will unwatch the events\n    return watchContractEvents<abi, abiEvents>({\n      contract,\n      onEvents: (newEvents) => {\n        if (newEvents.length > 0 && newEvents[0]) {\n          latestBlockNumber.current = newEvents[0].blockNumber; // Update the latest block number to avoid duplicate events if a new poller is spawned during this block\n        }\n        // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n        queryClient.setQueryData(queryKey, (oldEvents: any = []) => [\n          ...oldEvents,\n          ...newEvents,\n        ]);\n      },\n      events,\n      latestBlockNumber: latestBlockNumber.current,\n    });\n  }, [contract, enabled, events, queryClient, queryKey, watch]);\n\n  return query;\n}\n", "import { type UseMutationResult, useMutation } from \"@tanstack/react-query\";\nimport type { Abi, AbiFunction } from \"abitype\";\nimport {\n  type SimulateOptions,\n  simulateTransaction,\n} from \"../../../../transaction/actions/simulate.js\";\n\n/**\n * A hook to simulate a transaction.\n * @returns A mutation object to simulate a transaction.\n * @example\n * ```jsx\n * import { useSimulateTransaction } from \"thirdweb/react\";\n * const { mutate: simulateTx } = useSimulateTransaction();\n *\n * // later\n * const result = await simulateTx(tx);\n * ```\n * @transaction\n */\nexport function useSimulateTransaction<\n  const abi extends Abi,\n  const abiFn extends AbiFunction,\n>(): UseMutationResult<\n  Awaited<ReturnType<typeof simulateTransaction>>,\n  Error,\n  SimulateOptions<abi, abiFn>\n> {\n  return useMutation({\n    mutationFn: (options) => simulateTransaction(options),\n  });\n}\n", "import { type UseMutationResult, useMutation } from \"@tanstack/react-query\";\nimport { sendBatchTransaction } from \"../../../../transaction/actions/send-batch-transaction.js\";\nimport type { WaitForReceiptOptions } from \"../../../../transaction/actions/wait-for-tx-receipt.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { useActiveAccount } from \"../wallets/useActiveAccount.js\";\n\n/**\n * A hook to send a transaction.\n * @returns A mutation object to send a transaction.\n * @example\n * ```jsx\n * import { useSendBatchTransaction } from \"thirdweb/react\";\n * const { mutate: sendBatch, data: transactionResult } = useSendBatchTransaction();\n *\n * // later\n * sendBatch([tx1, tx2]);\n * ```\n * @transaction\n */\nexport function useSendBatchTransaction(): UseMutationResult<\n  WaitForReceiptOptions,\n  Error,\n  PreparedTransaction[]\n> {\n  const account = useActiveAccount();\n  return useMutation({\n    mutationFn: async (transactions) => {\n      if (!account) {\n        throw new Error(\"No active account\");\n      }\n      return await sendBatchTransaction({\n        transactions,\n        account,\n      });\n    },\n  });\n}\n", "import { type UseMutationResult, useMutation } from \"@tanstack/react-query\";\nimport type { GaslessOptions } from \"../../../../transaction/actions/gasless/types.js\";\nimport { sendAndConfirmTransaction } from \"../../../../transaction/actions/send-and-confirm-transaction.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport type { TransactionReceipt } from \"../../../../transaction/types.js\";\nimport { useActiveAccount } from \"../wallets/useActiveAccount.js\";\n\n/**\n * Configuration for the `useSendTransaction` hook.\n */\ntype SendAndConfirmTransactionConfig = {\n  /**\n   * Configuration for gasless transactions.\n   * Refer to [`GaslessOptions`](https://portal.thirdweb.com/references/typescript/v5/GaslessOptions) for more details.\n   */\n  gasless?: GaslessOptions;\n};\n\n/**\n * A hook to send a transaction.\n * @returns A mutation object to send a transaction.\n * @example\n * ```jsx\n * import { useSendAndConfirmTransaction } from \"thirdweb/react\";\n * const { mutate: sendAndConfirmTx, data: transactionReceipt } = useSendAndConfirmTransaction();\n *\n * // later\n * sendAndConfirmTx(tx);\n * ```\n *\n *\n * ### Gasless usage with [thirdweb Engine](https://portal.thirdweb.com/engine)\n * ```tsx\n * import { useSendAndConfirmTransaction } from \"thirdweb/react\";\n * const mutation = useSendAndConfirmTransaction({\n *   gasless: {\n *     provider: \"engine\",\n *     relayerUrl: \"https://thirdweb.engine-***.thirdweb.com/relayer/***\",\n *     relayerForwarderAddress: \"0x...\",\n *   }\n * });\n * ```\n *\n * ### Gasless usage with OpenZeppelin\n * ```tsx\n * import { useSendAndConfirmTransaction } from \"thirdweb/react\";\n * const mutation = useSendAndConfirmTransaction({\n *   gasless: {\n *     provider: \"openzeppelin\",\n *     relayerUrl: \"https://...\",\n *     relayerForwarderAddress: \"0x...\",\n *   }\n * });\n * ```\n * @transaction\n */\nexport function useSendAndConfirmTransaction(\n  config: SendAndConfirmTransactionConfig = {},\n): UseMutationResult<TransactionReceipt, Error, PreparedTransaction> {\n  const account = useActiveAccount();\n  const { gasless } = config;\n  return useMutation({\n    mutationFn: async (transaction) => {\n      if (!account) {\n        throw new Error(\"No active account\");\n      }\n      return await sendAndConfirmTransaction({\n        transaction,\n        account,\n        gasless,\n      });\n    },\n  });\n}\n", "import { type UseMutationResult, useMutation } from \"@tanstack/react-query\";\nimport {\n  type EstimateGasResult,\n  estimateGas,\n} from \"../../../../transaction/actions/estimate-gas.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { useActiveAccount } from \"../wallets/useActiveAccount.js\";\n\n/**\n * A hook to estimate the gas for a given transaction.\n * @returns A mutation object to estimate gas.\n * @example\n * ```jsx\n * import { useEstimateGas } from \"thirdweb/react\";\n * const { mutate: estimateGas, data: gasEstimate } = useEstimateGas();\n *\n * // later\n * const estimatedGas = await estimateGas(tx);\n * ```\n * @transaction\n */\nexport function useEstimateGas(): UseMutationResult<\n  EstimateGasResult,\n  Error,\n  PreparedTransaction\n> {\n  const account = useActiveAccount();\n  return useMutation({\n    mutationFn: (transaction) => estimateGas({ transaction, account }),\n  });\n}\n", "import { type UseMutationResult, useMutation } from \"@tanstack/react-query\";\nimport {\n  type EstimateGasCostResult,\n  estimateGasCost,\n} from \"../../../../transaction/actions/estimate-gas-cost.js\";\nimport type { PreparedTransaction } from \"../../../../transaction/prepare-transaction.js\";\nimport { useActiveAccount } from \"../wallets/useActiveAccount.js\";\n\n/**\n * A hook to estimate the gas cost in ether and wei for a given transaction.\n * @returns A mutation object to estimate gas cost.\n * @example\n * ```jsx\n * import { useEstimateGasCost } from \"thirdweb/react\";\n * const { mutate: estimateGasCost, data: gasEstimate } = useEstimateGas();\n *\n * // later\n * const estimatedGas = await estimateGasCost(tx);\n * console.log(\"gas cost in ether\", estimatedGas.ether);\n * ```\n * @transaction\n */\nexport function useEstimateGasCost(): UseMutationResult<\n  EstimateGasCostR<PERSON>ult,\n  Error,\n  PreparedTransaction\n> {\n  const account = useActiveAccount();\n  return useMutation({\n    mutationFn: (transaction) => estimateGasCost({ transaction, account }),\n  });\n}\n", "import { useQuery, useQueryClient } from \"@tanstack/react-query\";\nimport { useEffect, useMemo } from \"react\";\nimport type { Chain } from \"../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { eth_blockNumber } from \"../../../../rpc/actions/eth_blockNumber.js\";\nimport { getRpcClient } from \"../../../../rpc/rpc.js\";\nimport { watchBlockNumber } from \"../../../../rpc/watchBlockNumber.js\";\n\nexport type UseBlockNumberOptions = {\n  client: ThirdwebClient;\n  chain: Chain;\n  enabled?: boolean;\n  watch?: boolean;\n};\n\n/**\n * Hook that watches for changes in the block number on a given chain.\n * @param options - The options for the hook.\n * @returns The latest block number.\n * @example\n * ```ts\n * import { useBlockNumber } from \"thirdweb/react\";\n * const blockNumber = useBlockNumber({client, chain});\n * ```\n * @utils\n */\nexport function useBlockNumber(options: UseBlockNumberOptions) {\n  const { client, chain, enabled = true, watch = true } = options;\n\n  const queryClient = useQueryClient();\n\n  const queryKey = useMemo(() => [chain.id, \"blockNumber\"] as const, [chain]);\n  const query = useQuery({\n    // TODO: technically client should be part of the queryKey here...\n\n    queryKey: queryKey,\n    queryFn: async () => {\n      const rpcRequest = getRpcClient({ client, chain });\n      return await eth_blockNumber(rpcRequest);\n    },\n    enabled,\n  });\n\n  useEffect(() => {\n    if (!enabled || !watch) {\n      // don't watch if not enabled or not watching\n      return;\n    }\n    return watchBlockNumber({\n      client,\n      chain,\n      onNewBlockNumber: (newBlockNumber) => {\n        queryClient.setQueryData(queryKey, newBlockNumber);\n      },\n    });\n  }, [client, chain, enabled, queryClient, queryKey, watch]);\n\n  return query.data;\n}\n", "import { type UseQueryResult, useQuery } from \"@tanstack/react-query\";\nimport type { Abi } from \"abitype\";\nimport type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport { getFunctionId } from \"../../../utils/function-id.js\";\nimport { stringify } from \"../../../utils/json.js\";\n\nconst CONTRACT_QUERY_CACHE = new WeakMap();\n\n/**\n * Creates a `useQuery` hook for a contract call.\n * @param readCall - A function that performs the contract function call and returns the result.\n * @returns An object containing the created `useRead` hook.\n * @example\n * ```jsx\n * import { createContractQuery } from \"thirdweb/react\";\n * import { totalSupply } from \"thirdweb/extensions/erc20\";\n * const useTotalSupply = createContractQuery(totalSupply);\n * const { data, isLoading } = useTotalSupply({contract})\n * ```\n * @transaction\n */\nexport function createContractQuery<\n  opts extends object,\n  result,\n  abi extends Abi,\n>(\n  readCall: (options: BaseTransactionOptions<opts, abi>) => Promise<result>,\n): (\n  options: BaseTransactionOptions<opts, abi> & {\n    queryOptions?: Partial<{ enabled: boolean }>;\n  },\n) => UseQueryResult<result, Error> {\n  if (CONTRACT_QUERY_CACHE.has(readCall)) {\n    return CONTRACT_QUERY_CACHE.get(readCall) as (\n      options: BaseTransactionOptions<opts, abi>,\n    ) => UseQueryResult<result, Error>;\n  }\n  function useRead(\n    options: BaseTransactionOptions<opts, abi> & {\n      queryOptions?: Partial<{ enabled: boolean }>;\n    },\n  ) {\n    const { contract, queryOptions, ...params } = options;\n\n    return useQuery({\n      queryKey: [\n        \"readContract\",\n        contract.chain.id,\n        contract.address,\n        getFunctionId(readCall),\n        stringify(params),\n      ] as const,\n      queryFn: () => readCall(options),\n      ...queryOptions,\n    });\n  }\n  CONTRACT_QUERY_CACHE.set(readCall, useRead);\n  return useRead;\n}\n", "import { useQueryClient } from \"@tanstack/react-query\";\n\n/**\n * @internal\n */\nexport function useInvalidateContractQuery() {\n  const queryClient = useQueryClient();\n\n  return ({\n    chainId,\n    contractAddress,\n  }: {\n    chainId: number;\n    contractAddress: string;\n  }) => {\n    queryClient.invalidateQueries({\n      queryKey: [\"readContract\", chainId, contractAddress],\n    });\n  };\n}\n", "import {\n  type UseQueryOptions,\n  type UseQueryResult,\n  useQuery,\n} from \"@tanstack/react-query\";\nimport {\n  type BuyWithCryptoHistoryData,\n  type BuyWithCryptoHistoryParams,\n  getBuyWithCryptoHistory,\n} from \"../../../../pay/buyWithCrypto/getHistory.js\";\n\n/**\n * @internal\n */\nexport type BuyWithCryptoHistoryQueryOptions = Omit<\n  UseQueryOptions<BuyWithCryptoHistoryData>,\n  \"queryFn\" | \"queryKey\" | \"enabled\"\n>;\n\n/**\n * Hook to get the \"Buy with crypto\" transaction history for a given wallet address.\n *\n * This hook is a React Query wrapper of the [`getBuyWithCryptoHistory`](https://portal.thirdweb.com/references/typescript/v5/getBuyWithCryptoHistory) function.\n * You can also use that function directly\n * @param params - object of type [`BuyWithCryptoHistoryParams`](https://portal.thirdweb.com/references/typescript/v5/BuyWithCryptoHistoryParams)\n * @param queryParams - options to configure the react query\n * @returns A React Query object which contains the data of type [`BuyWithCryptoHistoryData`](https://portal.thirdweb.com/references/typescript/v5/BuyWithCryptoHistoryData)\n * @example\n * ```tsx\n * import { useBuyWithCryptoHistory } from \"thirdweb/react\";\n *\n * function Component() {\n *  const buyWithCryptoHistory = useBuyWithCryptoHistory(params);\n *  return <div> ... </div>\n * }\n * ```\n * @deprecated\n * @buyCrypto\n */\nexport function useBuyWithCryptoHistory(\n  params?: BuyWithCryptoHistoryParams,\n  queryParams?: BuyWithCryptoHistoryQueryOptions,\n): UseQueryResult<BuyWithCryptoHistoryData> {\n  return useQuery({\n    ...queryParams,\n    queryKey: [\"getBuyWithCryptoHistory\", params],\n    queryFn: () => {\n      if (!params) {\n        throw new Error(\"Swap params are required\");\n      }\n      return getBuyWithCryptoHistory(params);\n    },\n    enabled: !!params,\n  });\n}\n", "import type { ThirdwebClient } from \"../../client/client.js\";\nimport { getClientFetch } from \"../../utils/fetch.js\";\nimport { getPayBuyWithFiatHistoryEndpoint } from \"../utils/definitions.js\";\nimport type { BuyWithFiatStatus } from \"./getStatus.js\";\n\n/**\n * The parameters for [`getBuyWithFiatHistory`](https://portal.thirdweb.com/references/typescript/v5/getBuyWithFiatHistory) function\n * @deprecated\n * @buyCrypto\n */\nexport type BuyWithFiatHistoryParams = {\n  /**\n   * A client is the entry point to the thirdweb SDK. It is required for all other actions.\n   *\n   * You can create a client using the `createThirdwebClient` function.\n   * Refer to the [Creating a Client](https://portal.thirdweb.com/typescript/v5/client) documentation for more information.\n   */\n  client: ThirdwebClient;\n  /**\n   * The address of the wallet to get the wallet history for\n   */\n  walletAddress: string;\n  /**\n   * The number of results to return in a single page. The default value is `10`.\n   */\n  count: number;\n  /**\n   * index of the first result to return. The default value is `0`.\n   *\n   * If you want to start the list from nth item, you can set the start value to (n-1).\n   */\n  start: number;\n};\n\n/**\n * The results for [`getBuyWithFiatHistory`](https://portal.thirdweb.com/references/typescript/v5/getBuyWithFiatHistory) function\n * @buyCrypto\n */\nexport type BuyWithFiatHistoryData = {\n  page: BuyWithFiatStatus[];\n  hasNextPage: boolean;\n};\n\n/**\n * Get the \"Buy with fiat\" transaction history for a given wallet address\n * @param params Object of type [`BuyWithFiatHistoryParams`](https://portal.thirdweb.com/references/typescript/v5/BuyWithFiatHistoryParams)\n * @example\n *\n * ```ts\n * import { createThirdwebClient } from \"thirdweb\";\n * import { getBuyWithFiatHistory } from \"thirdweb/pay\";\n *\n * const client = createThirdwebClient({ clientId: \"...\" });\n *\n * // get the 10 latest \"Buy with fiat\" transactions dony by the wallet\n * const history = await getBuyWithFiatHistory({\n *  client: client,\n *  walletAddress: '0x...',\n *  start: 0,\n *  count: 10,\n * })\n * ```\n * @returns Object of type [`BuyWithFiatHistoryData`](https://portal.thirdweb.com/references/typescript/v5/BuyWithFiatHistoryData)\n * @deprecated\n * @buyCrypto\n */\nexport async function getBuyWithFiatHistory(\n  params: BuyWithFiatHistoryParams,\n): Promise<BuyWithFiatHistoryData> {\n  try {\n    const queryParams = new URLSearchParams();\n    queryParams.append(\"walletAddress\", params.walletAddress);\n    queryParams.append(\"start\", params.start.toString());\n    queryParams.append(\"count\", params.count.toString());\n\n    const queryString = queryParams.toString();\n    const url = `${getPayBuyWithFiatHistoryEndpoint()}?${queryString}`;\n\n    const response = await getClientFetch(params.client)(url);\n\n    // Assuming the response directly matches the BuyWithFiatStatus response interface\n    if (!response.ok) {\n      const error = await response.text().catch(() => null);\n      throw new Error(\n        `HTTP error! status: ${response.status} - ${response.statusText}: ${error || \"unknown error\"}`,\n      );\n    }\n\n    const data: BuyWithFiatHistoryData = (await response.json()).result;\n    return data;\n  } catch (error) {\n    throw new Error(`Fetch failed: ${error}`);\n  }\n}\n", "import {\n  type UseQueryOptions,\n  type UseQueryResult,\n  useQuery,\n} from \"@tanstack/react-query\";\nimport {\n  type BuyWithFiatHistoryData,\n  type BuyWithFiatHistoryParams,\n  getBuyWithFiatHistory,\n} from \"../../../../pay/buyWithFiat/getHistory.js\";\n\n/**\n * @internal\n */\nexport type BuyWithFiatHistoryQueryOptions = Omit<\n  UseQueryOptions<BuyWithFiatHistoryData>,\n  \"queryFn\" | \"queryKey\" | \"enabled\"\n>;\n\n/**\n * Hook to get the \"Buy with Fiat\" transaction history for a given wallet address.\n *\n * This hook is a React Query wrapper of the [`getBuyWithFiatHistory`](https://portal.thirdweb.com/references/typescript/v5/getBuyWithFiatHistory) function.\n * You can also use that function directly\n * @param params - object of type [`BuyWithFiatHistoryParams`](https://portal.thirdweb.com/references/typescript/v5/BuyWithFiatHistoryParams)\n * @param queryParams - options to configure the react query\n * @returns A React Query object which contains the data of type [`BuyWithFiatHistoryData`](https://portal.thirdweb.com/references/typescript/v5/BuyWithFiatHistoryData)\n * @example\n * ```tsx\n * import { useBuyWithFiatHistory } from \"thirdweb/react\";\n *\n * function Component() {\n *  const historyQuery = useBuyWithFiatHistory(params);\n *  return <div> ... </div>\n * }\n * ```\n * @deprecated\n * @buyCrypto\n */\nexport function useBuyWithFiatHistory(\n  params?: BuyWithFiatHistoryParams,\n  queryParams?: BuyWithFiatHistoryQueryOptions,\n): UseQueryResult<BuyWithFiatHistoryData> {\n  return useQuery({\n    ...queryParams,\n    queryKey: [\"buyWithFiatHistory\", params],\n    queryFn: () => {\n      if (!params) {\n        throw new Error(\"params are required\");\n      }\n      return getBuyWithFiatHistory(params);\n    },\n    enabled: !!params,\n  });\n}\n", "import type { ThirdwebClient } from \"../client/client.js\";\nimport { getClientFetch } from \"../utils/fetch.js\";\nimport type { BuyWithCryptoStatus } from \"./buyWithCrypto/getStatus.js\";\nimport type { BuyWithFiatStatus } from \"./buyWithFiat/getStatus.js\";\nimport { getPayBuyHistoryEndpoint } from \"./utils/definitions.js\";\n\n/**\n * The parameters for [`getBuyHistory`](https://portal.thirdweb.com/references/typescript/v5/getBuyHistory) function\n * @buyCrypto\n */\nexport type BuyHistoryParams = {\n  /**\n   * A client is the entry point to the thirdweb SDK. It is required for all other actions.\n   *\n   * You can create a client using the `createThirdwebClient` function.\n   * Refer to the [Creating a Client](https://portal.thirdweb.com/typescript/v5/client) documentation for more information.\n   */\n  client: ThirdwebClient;\n  /**\n   * The wallet address to get the buy history for.\n   */\n  walletAddress: string;\n  /**\n   * The number of results to return.\n   *\n   * The default value is `10`.\n   */\n  count: number;\n  /**\n   * Index of the first result to return. The default value is `0`.\n   */\n  start: number;\n};\n\n/**\n * The result for [`getBuyHistory`](https://portal.thirdweb.com/references/typescript/v5/getBuyWithCryptoHistory) function\n *\n * It includes both \"Buy with Crypto\" and \"Buy with Fiat\" transactions\n *\n * @buyCrypto\n */\nexport type BuyHistoryData = {\n  /**\n   * The list of buy transactions.\n   */\n  page: Array<\n    | {\n        buyWithFiatStatus: BuyWithFiatStatus;\n      }\n    | {\n        buyWithCryptoStatus: BuyWithCryptoStatus;\n      }\n  >;\n  /**\n   * Whether there are more pages of results.\n   */\n  hasNextPage: boolean;\n};\n\n/**\n * Get Buy transaction history for a given wallet address.\n *\n * This includes both \"Buy with Cryto\" and \"Buy with Fiat\" transactions\n *\n * @param params Object of type [`BuyHistoryParams`](https://portal.thirdweb.com/references/typescript/v5/BuyHistoryParams)\n * @example\n * ```ts\n * import { createThirdwebClient } from \"thirdweb\";\n * import { getBuyHistory } from \"thirdweb/pay\";\n *\n * const client = createThirdwebClient({ clientId: \"...\" });\n *\n * const history = await getBuyHistory({\n *  client,\n *  walletAddress: \"0x...\",\n * })\n * ```\n * @buyCrypto\n */\nexport async function getBuyHistory(\n  params: BuyHistoryParams,\n): Promise<BuyHistoryData> {\n  try {\n    const queryParams = new URLSearchParams();\n    queryParams.append(\"walletAddress\", params.walletAddress);\n    queryParams.append(\"start\", params.start.toString());\n    queryParams.append(\"count\", params.count.toString());\n\n    const queryString = queryParams.toString();\n    const url = `${getPayBuyHistoryEndpoint()}?${queryString}`;\n\n    const response = await getClientFetch(params.client)(url);\n\n    // Assuming the response directly matches the SwapResponse interface\n    if (!response.ok) {\n      const error = await response.text().catch(() => null);\n      throw new Error(\n        `HTTP error! status: ${response.status} - ${response.statusText}: ${error || \"unknown error\"}`,\n      );\n    }\n\n    const data: BuyHistoryData = (await response.json()).result;\n    return data;\n  } catch (error) {\n    throw new Error(`Fetch failed: ${error}`);\n  }\n}\n", "import {\n  type UseQueryOptions,\n  type UseQueryResult,\n  useQuery,\n} from \"@tanstack/react-query\";\nimport {\n  type BuyHistoryData,\n  type BuyHistoryParams,\n  getBuyHistory,\n} from \"../../../../pay/getBuyHistory.js\";\n\n/**\n * @internal\n */\nexport type BuyHistoryQueryOptions = Omit<\n  UseQueryOptions<BuyHistoryData>,\n  \"queryFn\" | \"queryKey\" | \"enabled\"\n>;\n\n/**\n * Hook to get the history of Buy transactions for a given wallet - This includes both \"buy with crypto\" and \"buy with fiat\" transactions.\n *\n * This hook is a React Query wrapper of the [`getBuyHistory`](https://portal.thirdweb.com/references/typescript/v5/getBuyHistory) function.\n * You can also use that function directly\n *\n * @param params - object of type [`BuyHistoryParams`](https://portal.thirdweb.com/references/typescript/v5/BuyHistoryParams)\n * @param queryParams - options to configure the react query\n * @returns A React Query object which contains the data of type [`BuyHistoryData`](https://portal.thirdweb.com/references/typescript/v5/BuyHistoryData)\n * @example\n * ```tsx\n * import { useBuyHistory } from \"thirdweb/react\";\n *\n * function Component() {\n *  const buyHistoryQuery = useBuyHistory(params);\n *  return <div> ... </div>\n * }\n * ```\n * @buyCrypto\n */\nexport function useBuyHistory(\n  params?: BuyHistoryParams,\n  queryParams?: BuyHistoryQueryOptions,\n): UseQueryResult<BuyHistoryData> {\n  return useQuery({\n    ...queryParams,\n    queryKey: [\"getBuyHistory\", params],\n    queryFn: () => {\n      if (!params) {\n        throw new Error(\"params are required\");\n      }\n      return getBuyHistory(params);\n    },\n    enabled: !!params,\n  });\n}\n", "import type { ThirdwebClient } from \"../../client/client.js\";\nimport {\n  type BuyWithCryptoQuote,\n  getBuyWithCryptoQuote,\n} from \"../buyWithCrypto/getQuote.js\";\nimport type { BuyWithFiatStatus } from \"./getStatus.js\";\n\n/**\n * The parameters for [`getPostOnRampQuote`](https://portal.thirdweb.com/references/typescript/v5/getPostOnRampQuote) function\n * @buyCrypto\n */\nexport type GetPostOnRampQuoteParams = {\n  /**\n   * A client is the entry point to the thirdweb SDK. It is required for all other actions.\n   *\n   * You can create a client using the `createThirdwebClient` function.\n   * Refer to the [Creating a Client](https://portal.thirdweb.com/typescript/v5/client) documentation for more information.\n   */\n  client: ThirdwebClient;\n  /**\n   * The \"Buy with fiat\" transaction status object returned by [`getBuyWithFiatStatus`](https://portal.thirdweb.com/typescript/v5/getBuyWithFiatStatus) function\n   */\n  buyWithFiatStatus: BuyWithFiatStatus;\n};\n\n/**\n * When buying a token with fiat currency - It only involes doing on-ramp if the on-ramp provider supports buying the given destination token directly.\n *\n * If the on-ramp provider does not support buying the destination token directly, user can be sent an intermediate token with fiat currency from the on-ramp provider which\n * can be swapped to destination token onchain.\n *\n * `getPostOnRampQuote` function is used to get the quote for swapping the on-ramp token to destination token.\n *\n * When you get a \"Buy with Fiat\" status of type \"CRYPTO_SWAP_REQUIRED\" from the [`getBuyWithFiatStatus`](https://portal.thirdweb.com/references/typescript/v5/getBuyWithFiatStatus) function,\n *  you can use `getPostOnRampQuote` function to get the quote of type [`BuyWithCryptoQuote`](https://portal.thirdweb.com/references/typescript/v5/BuyWithCryptoQuote) for swapping the on-ramp token to destination token\n *\n * Once you have the quote, you can start the Swap process by following the same steps as mentioned in the [`getBuyWithCryptoQuote`](https://portal.thirdweb.com/references/typescript/v5/getBuyWithCryptoQuote) documentation.\n *\n * @param params - object of type [`GetPostOnRampQuoteParams`](https://portal.thirdweb.com/references/typescript/v5/GetPostOnRampQuoteParams)\n * @returns Object of type [`BuyWithCryptoQuote`](https://portal.thirdweb.com/references/typescript/v5/BuyWithCryptoQuote) which contains the information about the quote such as processing fees, estimated time, converted token amounts, etc.\n * @example\n * ```ts\n * import { getPostOnRampQuote, getBuyWithFiatStatus } from \"thirdweb/pay\";\n *\n * // previous steps\n * const fiatQuote = await getBuyWithFiatQuote(fiatQuoteParams);\n * window.open(fiatQuote.onRampLink, \"_blank\");\n * const buyWithFiatStatus = await getBuyWithFiatStatus({ client, intentId }); // keep calling this until status is \"settled\" state\n *\n * // when a swap is required after onramp\n * if (buyWithFiatStatus.status === \"CRYPTO_SWAP_REQUIRED\") {\n *  const buyWithCryptoQuote = await getPostOnRampQuote({\n *    client,\n *    buyWithFiatStatus\n *  });\n * }\n * ```\n * @deprecated\n * @buyCrypto\n */\nexport async function getPostOnRampQuote({\n  client,\n  buyWithFiatStatus,\n}: GetPostOnRampQuoteParams): Promise<BuyWithCryptoQuote> {\n  if (buyWithFiatStatus.status === \"NOT_FOUND\") {\n    throw new Error(\"Invalid buyWithFiatStatus\");\n  }\n\n  return getBuyWithCryptoQuote({\n    client,\n    intentId: buyWithFiatStatus.intentId,\n    // onramp always happens to fromAddress, and then swap is done from - fromAddress to toAddress\n    fromAddress: buyWithFiatStatus.fromAddress,\n    toAddress: buyWithFiatStatus.toAddress,\n    fromChainId: buyWithFiatStatus.quote.onRampToken.chainId,\n    fromTokenAddress: buyWithFiatStatus.quote.onRampToken.tokenAddress,\n    toChainId: buyWithFiatStatus.quote.toToken.chainId,\n    toTokenAddress: buyWithFiatStatus.quote.toToken.tokenAddress,\n    toAmount: buyWithFiatStatus.quote.estimatedToTokenAmount,\n  });\n}\n", "import {\n  type UseQueryOptions,\n  type UseQueryResult,\n  useQuery,\n} from \"@tanstack/react-query\";\nimport type { BuyWithCryptoQuote } from \"../../../../pay/buyWithCrypto/getQuote.js\";\nimport {\n  type GetPostOnRampQuoteParams,\n  getPostOnRampQuote,\n} from \"../../../../pay/buyWithFiat/getPostOnRampQuote.js\";\n\n/**\n * @internal\n */\nexport type PostOnRampQuoteQueryOptions = Omit<\n  UseQueryOptions<BuyWithCryptoQuote>,\n  \"queryFn\" | \"queryKey\" | \"enabled\"\n>;\n\n/**\n * When buying a token with fiat currency - It only involes doing on-ramp if the on-ramp provider supports buying the given destination token directly.\n *\n * If the on-ramp provider does not support buying the destination token directly, user can be sent an intermediate token with fiat currency from the on-ramp provider which\n * can be swapped to destination token onchain.\n *\n * `usePostOnRampQuote` hook is used to get the quote for swapping the on-ramp token to destination token.\n *\n * When you get a \"Buy with Fiat\" status of type `\"CRYPTO_SWAP_REQUIRED\"` from the [`useBuyWithFiatStatus`](https://portal.thirdweb.com/references/typescript/v5/useBuyWithFiatStatus) hook,\n * you can use `usePostOnRampQuote` hook to get the quote of type [`BuyWithCryptoQuote`](https://portal.thirdweb.com/references/typescript/v5/BuyWithCryptoQuote) for swapping the on-ramp token to destination token to complete the step-2 of the process.\n *\n * Once you have the quote, you can start the Swap process by following the same steps as mentioned in the [`useBuyWithCryptoQuote`](https://portal.thirdweb.com/references/typescript/v5/useBuyWithCryptoQuote) documentation.\n *\n * @param params - object of type [`GetPostOnRampQuoteParams`](https://portal.thirdweb.com/references/typescript/v5/GetPostOnRampQuoteParams)\n * @returns Object of type [`BuyWithCryptoQuote`](https://portal.thirdweb.com/references/typescript/v5/BuyWithCryptoQuote) which contains the information about the quote such as processing fees, estimated time, converted token amounts, etc.\n * @buyCrypto\n */\nexport function usePostOnRampQuote(\n  params?: GetPostOnRampQuoteParams,\n  queryOptions?: PostOnRampQuoteQueryOptions,\n): UseQueryResult<BuyWithCryptoQuote> {\n  return useQuery({\n    ...queryOptions,\n    queryKey: [\"getPostOnRampQuote\", params],\n    queryFn: async () => {\n      if (!params) {\n        throw new Error(\"No params provided\");\n      }\n      return getPostOnRampQuote(params);\n    },\n    enabled: !!params,\n  });\n}\n", "\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport type { Chain } from \"../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport type { Wallet } from \"../../../wallets/interfaces/wallet.js\";\nimport type { SmartWalletOptions } from \"../../../wallets/smart/types.js\";\nimport type { AppMetadata } from \"../../../wallets/types.js\";\nimport type { WalletId } from \"../../../wallets/wallet-types.js\";\nimport { CustomThemeProvider } from \"../../core/design-system/CustomThemeProvider.js\";\nimport type { Theme } from \"../../core/design-system/index.js\";\nimport {\n  type SiweAuthOptions,\n  useSiweAuth,\n} from \"../../core/hooks/auth/useSiweAuth.js\";\nimport type {\n  ConnectButton_connectModalOptions,\n  PayUIOptions,\n} from \"../../core/hooks/connection/ConnectButtonProps.js\";\nimport { useActiveAccount } from \"../../core/hooks/wallets/useActiveAccount.js\";\nimport { useActiveWallet } from \"../../core/hooks/wallets/useActiveWallet.js\";\nimport { useConnectionManager } from \"../../core/providers/connection-manager.js\";\nimport type { SupportedTokens } from \"../../core/utils/defaultTokens.js\";\nimport { AutoConnect } from \"../../web/ui/AutoConnect/AutoConnect.js\";\nimport { EmbedContainer } from \"./ConnectWallet/Modal/ConnectEmbed.js\";\nimport { useConnectLocale } from \"./ConnectWallet/locale/getConnectLocale.js\";\nimport BuyScreen from \"./ConnectWallet/screens/Buy/BuyScreen.js\";\nimport { ExecutingTxScreen } from \"./TransactionButton/ExecutingScreen.js\";\nimport { DynamicHeight } from \"./components/DynamicHeight.js\";\nimport { Spinner } from \"./components/Spinner.js\";\nimport type { LocaleId } from \"./types.js\";\n\n/**\n * Props of [`PayEmbed`](https://portal.thirdweb.com/references/typescript/v5/PayEmbed) component\n */\nexport type PayEmbedProps = {\n  /**\n   * Override the default tokens shown in PayEmbed UI\n   * By default, PayEmbed shows a few popular tokens for Pay supported chains\n   * @example\n   *\n   * `supportedTokens` prop allows you to override this list as shown below.\n   *\n   * ```tsx\n   * import { PayEmbed } from 'thirdweb/react';\n   * import { NATIVE_TOKEN_ADDRESS } from 'thirdweb';\n   *\n   * function Example() {\n   *   return (\n   * \t\t<PayEmbed\n   * \t\t\tsupportedTokens={{\n   *        // Override the tokens for Base Mainnet ( chaid id 84532 )\n   * \t\t\t\t84532: [\n   * \t\t\t\t\t{\n   * \t\t\t\t\t\taddress: NATIVE_TOKEN_ADDRESS, // use NATIVE_TOKEN_ADDRESS for native token\n   * \t\t\t\t\t\tname: 'Base ETH',\n   * \t\t\t\t\t\tsymbol: 'ETH',\n   * \t\t\t\t\t\ticon: 'https://...',\n   * \t\t\t\t\t},\n   *          {\n   * \t\t\t\t\t\taddress: '0x...', // token contract address\n   * \t\t\t\t\t\tname: 'Dai Stablecoin',\n   * \t\t\t\t\t\tsymbol: 'DAI',\n   * \t\t\t\t\t\ticon: 'https://...',\n   * \t\t\t\t\t},\n   * \t\t\t\t],\n   * \t\t\t}}\n   * \t\t/>\n   * \t);\n   * }\n   * ```\n   */\n  supportedTokens?: SupportedTokens;\n  /**\n   * A client is the entry point to the thirdweb SDK.\n   * It is required for all other actions.\n   * You can create a client using the `createThirdwebClient` function. Refer to the [Creating a Client](https://portal.thirdweb.com/typescript/v5/client) documentation for more information.\n   *\n   * You must provide a `clientId` or `secretKey` in order to initialize a client. Pass `clientId` if you want for client-side usage and `secretKey` for server-side usage.\n   *\n   * ```tsx\n   * import { createThirdwebClient } from \"thirdweb\";\n   *\n   * const client = createThirdwebClient({\n   *  clientId: \"<your_client_id>\",\n   * })\n   * ```\n   */\n  client: ThirdwebClient;\n  /**\n   * By default - ConnectButton UI uses the `en-US` locale for english language users.\n   *\n   * You can customize the language used in the ConnectButton UI by setting the `locale` prop.\n   *\n   * Refer to the [`LocaleId`](https://portal.thirdweb.com/references/typescript/v5/LocaleId) type for supported locales.\n   */\n  locale?: LocaleId;\n  /**\n   * Customize the Pay UI options. Refer to the [`PayUIOptions`](https://portal.thirdweb.com/references/typescript/v5/PayUIOptions) type for more details.\n   */\n  payOptions?: PayUIOptions;\n\n  /**\n   * Set the theme for the `PayEmbed` component. By default it is set to `\"dark\"`\n   *\n   * theme can be set to either `\"dark\"`, `\"light\"` or a custom theme object.\n   * You can also import [`lightTheme`](https://portal.thirdweb.com/references/typescript/v5/lightTheme)\n   * or [`darkTheme`](https://portal.thirdweb.com/references/typescript/v5/darkTheme)\n   * functions from `thirdweb/react` to use the default themes as base and overrides parts of it.\n   * @example\n   * ```ts\n   * import { lightTheme } from \"thirdweb/react\";\n   *\n   * const customTheme = lightTheme({\n   *  colors: {\n   *    modalBg: 'red'\n   *  }\n   * })\n   *\n   * function Example() {\n   *  return <PayEmbed client={client} theme={customTheme} />\n   * }\n   * ```\n   */\n  theme?: \"light\" | \"dark\" | Theme;\n\n  /**\n   * Customize the options for \"Connect\" Button showing in the PayEmbed UI when the user is not connected to a wallet.\n   *\n   * Refer to the [`PayEmbedConnectOptions`](https://portal.thirdweb.com/references/typescript/v5/PayEmbedConnectOptions) type for more details.\n   */\n  connectOptions?: PayEmbedConnectOptions;\n\n  /**\n   * All wallet IDs included in this array will be hidden from wallet selection when connected.\n   */\n  hiddenWallets?: WalletId[];\n\n  /**\n   * The wallet that should be pre-selected in the PayEmbed UI.\n   */\n  activeWallet?: Wallet;\n\n  style?: React.CSSProperties;\n\n  className?: string;\n};\n\n/**\n * Embed a prebuilt UI for funding wallets, purchases or transactions with crypto or fiat.\n *\n * @param props - Props of type [`PayEmbedProps`](https://portal.thirdweb.com/references/typescript/v5/PayEmbedProps) to configure the PayEmbed component.\n *\n * @example\n * ### Default configuration\n *\n * By default, the `PayEmbed` component will allows users to fund their wallets with crypto or fiat on any of the supported chains..\n *\n * ```tsx\n * <PayEmbed\n *   client={client}\n *  />\n * ```\n *\n * ### Top up wallets\n *\n * You can set the `mode` option to `\"fund_wallet\"` to allow users to top up their wallets with crypto or fiat.\n *\n * ```tsx\n * <PayEmbed\n *   client={client}\n *   payOptions={{\n *     mode: \"fund_wallet\",\n *     metadata: {\n *       name: \"Get funds\", // title of the payment modal\n *     },\n *     prefillBuy: {\n *       chain: base, // chain to prefill the buy screen with\n *       amount: \"0.01\", // amount to prefill the buy screen with\n *     },\n *   }}\n *  />\n * ```\n *\n * ### Direct Payments\n *\n * You can set the `mode` option to `\"direct_payment\"` to allow users to make a direct payment to a wallet address.\n *\n * ```tsx\n * <PayEmbed\n *   client={client}\n *   theme={\"light\"}\n *   payOptions={{\n *     mode: \"direct_payment\",\n *     paymentInfo: {\n *       amount: \"35\",\n *       chain: base,\n *       token: getDefaultToken(base, \"USDC\"),\n *       sellerAddress: \"0x...\", // the wallet address of the seller\n *     },\n *     metadata: {\n *       name: \"Black Hoodie (Size L)\",\n *       image: \"/drip-hoodie.png\",\n *     },\n *   }}\n *  />\n * ```\n *\n * ### Transactions\n *\n * You can set the `mode` option to `\"transaction\"` to allow users to execute a transaction with a different wallet, chain or token.\n *\n * ```tsx\n * <PayEmbed\n *   client={client}\n *   payOptions={{\n *     mode: \"transaction\",\n *     // can be any transaction\n *     transaction: claimTo({\n *       contract: nftContract,\n *       quantity: 1n,\n *       tokenId: 0n,\n *       to: \"0x...\",\n *     }),\n *     // this could be any metadata, including NFT metadata\n *     metadata: {\n *       name: \"VIP Ticket\",\n *       image: \"https://...\",\n *     },\n *   }}\n *  />\n * ```\n *\n * You can also handle ERC20 payments by passing `erc20value` to your transaction:\n *\n * ```tsx\n * <PayEmbed\n *   client={client}\n *   payOptions={{\n *     mode: \"transaction\",\n *     transaction: prepareContractCall({\n *       contract: yourContract,\n *       functionName: \"purchase\",\n *       args: [arg1, arg2, ...],\n *       erc20value: {\n *         token: USDC_TOKEN_ADDRESS, // the erc20 token required to purchase\n *         amount: toUnits(\"100\", 6), // the amount of erc20 token required to purchase\n *       },\n *     }),\n *   }}\n *  />\n * ```\n *\n * ### Enable/Disable payment methods\n *\n * You can disable the use of crypto or fiat by setting the `buyWithCrypto` or `buyWithFiat` options to `false`.\n *\n * ```tsx\n * <PayEmbed\n *   client={client}\n *   payOptions={{\n *     buyWithFiat: false,\n *   }}\n *  />\n * ```\n *\n * ### Customize the UI\n *\n * You can customize the UI of the `PayEmbed` component by passing a custom theme object to the `theme` prop.\n *\n * ```tsx\n * <PayEmbed\n *   client={client}\n *   theme={darkTheme({\n *     colors: {\n *       modalBg: \"red\",\n *     },\n *   })}\n * />\n * ```\n *\n * Refer to the [`Theme`](https://portal.thirdweb.com/references/typescript/v5/Theme) type for more details.\n *\n * ### Configure the wallet connection\n *\n * You can customize the wallet connection flow by passing a `connectOptions` object to the `PayEmbed` component.\n *\n * ```tsx\n * <PayEmbed\n *   client={client}\n *   connectOptions={{\n *     connectModal: {\n *       size: 'compact',\n *       title: \"Sign in\",\n *     }\n *   }}\n * />\n * ```\n *\n * Refer to the [`PayEmbedConnectOptions`](https://portal.thirdweb.com/references/typescript/v5/PayEmbedConnectOptions) type for more details.\n *\n * @buyCrypto\n */\nexport function PayEmbed(props: PayEmbedProps) {\n  const localeQuery = useConnectLocale(props.locale || \"en_US\");\n  const [screen, setScreen] = useState<\"buy\" | \"execute-tx\">(\"buy\");\n  const theme = props.theme || \"dark\";\n  const connectionManager = useConnectionManager();\n  const activeAccount = useActiveAccount();\n  const activeWallet = useActiveWallet();\n  const siweAuth = useSiweAuth(\n    activeWallet,\n    activeAccount,\n    props.connectOptions?.auth,\n  );\n\n  // Add props.chain and props.chains to defined chains store\n  useEffect(() => {\n    if (props.connectOptions?.chain) {\n      connectionManager.defineChains([props.connectOptions?.chain]);\n    }\n  }, [props.connectOptions?.chain, connectionManager]);\n\n  useEffect(() => {\n    if (props.connectOptions?.chains) {\n      connectionManager.defineChains(props.connectOptions?.chains);\n    }\n  }, [props.connectOptions?.chains, connectionManager]);\n\n  useEffect(() => {\n    if (props.activeWallet) {\n      connectionManager.setActiveWallet(props.activeWallet);\n    }\n  }, [props.activeWallet, connectionManager]);\n\n  let content = null;\n  const metadata =\n    props.payOptions && \"metadata\" in props.payOptions\n      ? props.payOptions.metadata\n      : null;\n\n  if (!localeQuery.data) {\n    content = (\n      <div\n        style={{\n          minHeight: \"350px\",\n          display: \"flex\",\n          justifyContent: \"center\",\n          alignItems: \"center\",\n        }}\n      >\n        <Spinner size=\"xl\" color=\"secondaryText\" />\n      </div>\n    );\n  } else {\n    content = (\n      <>\n        <AutoConnect client={props.client} siweAuth={siweAuth} />\n        {screen === \"buy\" && (\n          <BuyScreen\n            title={metadata?.name || \"Buy\"}\n            isEmbed={true}\n            supportedTokens={props.supportedTokens}\n            theme={theme}\n            client={props.client}\n            connectLocale={localeQuery.data}\n            hiddenWallets={props.hiddenWallets}\n            payOptions={\n              props.payOptions || {\n                mode: \"fund_wallet\",\n              }\n            }\n            onDone={() => {\n              if (props.payOptions?.mode === \"transaction\") {\n                setScreen(\"execute-tx\");\n              }\n            }}\n            connectOptions={props.connectOptions}\n            onBack={undefined}\n          />\n        )}\n\n        {screen === \"execute-tx\" &&\n          props.payOptions?.mode === \"transaction\" &&\n          props.payOptions.transaction && (\n            <ExecutingTxScreen\n              tx={props.payOptions.transaction}\n              closeModal={() => {\n                setScreen(\"buy\");\n              }}\n              onBack={() => {\n                setScreen(\"buy\");\n              }}\n              onTxSent={(data) => {\n                props.payOptions?.onPurchaseSuccess?.({\n                  type: \"transaction\",\n                  chainId: data.chain.id,\n                  transactionHash: data.transactionHash,\n                });\n              }}\n            />\n          )}\n      </>\n    );\n  }\n\n  return (\n    <CustomThemeProvider theme={theme}>\n      <EmbedContainer\n        modalSize=\"compact\"\n        style={props.style}\n        className={props.className}\n      >\n        <DynamicHeight>{content}</DynamicHeight>\n      </EmbedContainer>\n    </CustomThemeProvider>\n  );\n}\n\n/**\n * Connection options for the `PayEmbed` component\n *\n * @example\n * ```tsx\n * <PayEmbed client={client} connectOptions={{\n *    connectModal: {\n *      size: 'compact',\n *      title: \"Sign in\",\n *    }\n *  }}\n * />\n * ```\n */\nexport type PayEmbedConnectOptions = {\n  /**\n   * Configurations for the `ConnectButton`'s Modal that is shown for connecting a wallet\n   * Refer to the [`ConnectButton_connectModalOptions`](https://portal.thirdweb.com/references/typescript/v5/ConnectButton_connectModalOptions) type for more details\n   */\n  connectModal?: ConnectButton_connectModalOptions;\n\n  /**\n   * Configure options for WalletConnect\n   *\n   * By default WalletConnect uses the thirdweb's default project id.\n   * Setting your own project id is recommended.\n   *\n   * You can create a project id by signing up on [walletconnect.com](https://walletconnect.com/)\n   */\n  walletConnect?: {\n    projectId?: string;\n  };\n\n  /**\n   * Enable Account abstraction for all wallets. This will connect to the users's smart account based on the connected personal wallet and the given options.\n   *\n   * This allows to sponsor gas fees for your user's transaction using the thirdweb account abstraction infrastructure.\n   *\n   */\n  accountAbstraction?: SmartWalletOptions;\n\n  /**\n   * Array of wallets to show in Connect Modal. If not provided, default wallets will be used.\n   */\n  wallets?: Wallet[];\n  /**\n   * When the user has connected their wallet to your site, this configuration determines whether or not you want to automatically connect to the last connected wallet when user visits your site again in the future.\n   *\n   * By default it is set to `{ timeout: 15000 }` meaning that autoConnect is enabled and if the autoConnection does not succeed within 15 seconds, it will be cancelled.\n   *\n   * If you want to disable autoConnect, set this prop to `false`.\n   *\n   * If you want to customize the timeout, you can assign an object with a `timeout` key to this prop.\n   * ```\n   */\n  autoConnect?:\n    | {\n        timeout: number;\n      }\n    | boolean;\n\n  /**\n   * Metadata of the app that will be passed to connected wallet. Setting this is highly recommended.\n   */\n  appMetadata?: AppMetadata;\n\n  /**\n   * The [`Chain`](https://portal.thirdweb.com/references/typescript/v5/Chain) object of the blockchain you want the wallet to connect to\n   *\n   * If a `chain` is not specified, Wallet will be connected to whatever is the default set in the wallet.\n   *\n   * If a `chain` is specified, Wallet will be prompted to switch to given chain after connection if it is not already connected to it.\n   * This ensures that the wallet is connected to the correct blockchain before interacting with your app.\n   *\n   * The `ConnectButton` also shows a \"Switch Network\" button until the wallet is connected to the specified chain. Clicking on the \"Switch Network\" button triggers the wallet to switch to the specified chain.\n   *\n   * You can create a `Chain` object using the [`defineChain`](https://portal.thirdweb.com/references/typescript/v5/defineChain) function.\n   * At minimum, you need to pass the `id` of the blockchain to `defineChain` function to create a `Chain` object.\n   * ```\n   */\n  chain?: Chain;\n\n  /**\n   * Array of chains that your app supports.\n   *\n   * This is only relevant if your app is a multi-chain app and works across multiple blockchains.\n   * If your app only works on a single blockchain, you should only specify the `chain` prop.\n   *\n   * Given list of chains will used in various ways:\n   * - They will be displayed in the network selector in the `ConnectButton`'s details modal post connection\n   * - They will be sent to wallet at the time of connection if the wallet supports requesting multiple chains ( example: WalletConnect ) so that users can switch between the chains post connection easily\n   *\n   * You can create a `Chain` object using the [`defineChain`](https://portal.thirdweb.com/references/typescript/v5/defineChain) function.\n   * At minimum, you need to pass the `id` of the blockchain to `defineChain` function to create a `Chain` object.\n   *\n   * ```tsx\n   * import { defineChain } from \"thirdweb/react\";\n   *\n   * const polygon = defineChain({\n   *   id: 137,\n   * });\n   * ```\n   */\n  chains?: Chain[];\n\n  /**\n   * Wallets to show as recommended in the `ConnectButton`'s Modal\n   */\n  recommendedWallets?: Wallet[];\n\n  /**\n   * By default, ConnectButton modal shows a \"All Wallets\" button that shows a list of 500+ wallets.\n   *\n   * You can disable this button by setting `showAllWallets` prop to `false`\n   */\n  showAllWallets?: boolean;\n\n  /**\n   * Enable SIWE (Sign in with Ethererum) by passing an object of type `SiweAuthOptions` to\n   * enforce the users to sign a message after connecting their wallet to authenticate themselves.\n   *\n   * Refer to the [`SiweAuthOptions`](https://portal.thirdweb.com/references/typescript/v5/SiweAuthOptions) for more details\n   */\n  auth?: SiweAuthOptions;\n};\n", "import { use<PERSON><PERSON>back, use<PERSON>ontext, use<PERSON>emo, useState } from \"react\";\nimport type { Chain } from \"../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../client/client.js\";\nimport { getDefaultWallets } from \"../../../../wallets/defaultWallets.js\";\nimport type { Wallet } from \"../../../../wallets/interfaces/wallet.js\";\nimport type { SmartWalletOptions } from \"../../../../wallets/smart/types.js\";\nimport type { AppMetadata } from \"../../../../wallets/types.js\";\nimport type { Theme } from \"../../../core/design-system/index.js\";\nimport type { SiweAuthOptions } from \"../../../core/hooks/auth/useSiweAuth.js\";\nimport { SetRootElementContext } from \"../../../core/providers/RootElementContext.js\";\nimport { WalletUIStatesProvider } from \"../../providers/wallet-ui-states-provider.js\";\nimport { canFitWideModal } from \"../../utils/canFitWideModal.js\";\nimport type { LocaleId } from \"../types.js\";\nimport ConnectModal from \"./Modal/ConnectModal.js\";\nimport { getConnectLocale } from \"./locale/getConnectLocale.js\";\nimport type { ConnectLocale } from \"./locale/types.js\";\nimport type { WelcomeScreen } from \"./screens/types.js\";\n\n/**\n * hook that allows you to open the Connect UI in a Modal to prompt the user to connect wallet.\n * @example\n * ```tsx\n * import { createThirdwebClient } from \"thirdweb\";\n * import { useConnectModal } from \"thirdweb/react\";\n *\n * const client = createThirdwebClient({\n *  clientId: \"<your_client_id>\",\n * });\n *\n * function Example() {\n *   const { connect, isConnecting } = useConnectModal();\n *\n *   async function handleConnect() {\n *      const wallet = await connect({ client }); // opens the connect modal\n *      console.log('connected to', wallet);\n *   }\n *\n *   return <button onClick={handleConnect}> Connect </button>\n * }\n * ```\n *\n * The returned `connect` method takes an object of type [UseConnectModalOptions](https://portal.thirdweb.com/references/typescript/v5/ConnectButtonProps)\n * as an argument to customize the Connect Modal UI. Refer to [UseConnectModalOptions](https://portal.thirdweb.com/references/typescript/v5/ConnectButtonProps) to see the available options.\n *\n * @walletConnection\n */\nexport function useConnectModal() {\n  const setRootEl = useContext(SetRootElementContext);\n  const [isConnecting, setIsConnecting] = useState(false);\n\n  const connect = useCallback(\n    (props: UseConnectModalOptions) => {\n      function cleanup() {\n        setIsConnecting(false);\n        setRootEl(undefined);\n      }\n\n      return new Promise<Wallet>((resolve, reject) => {\n        setIsConnecting(true);\n        getConnectLocale(props.locale || \"en_US\")\n          .then((locale) => {\n            setRootEl(\n              <Modal\n                {...props}\n                onConnect={(w) => {\n                  if (props.auth) return;\n                  resolve(w);\n                  cleanup();\n                }}\n                onClose={() => {\n                  reject();\n                  cleanup();\n                }}\n                connectLocale={locale}\n              />,\n            );\n          })\n          .catch(() => {\n            reject();\n            cleanup();\n          });\n      });\n    },\n    [setRootEl],\n  );\n\n  return { connect, isConnecting };\n}\n\nfunction Modal(\n  props: UseConnectModalOptions & {\n    onConnect: (wallet: Wallet) => void;\n    onClose: () => void;\n    connectLocale: ConnectLocale;\n  },\n) {\n  const wallets = useMemo(\n    () =>\n      props.wallets ||\n      getDefaultWallets({\n        appMetadata: props.appMetadata,\n        chains: props.chains,\n      }),\n    [props.wallets, props.appMetadata, props.chains],\n  );\n\n  const size = useMemo(() => {\n    return !canFitWideModal() || wallets.length === 1\n      ? \"compact\"\n      : props.size || \"wide\";\n  }, [props.size, wallets.length]);\n  const meta = useMemo(() => {\n    return {\n      privacyPolicyUrl: props.privacyPolicyUrl,\n      showThirdwebBranding: props.showThirdwebBranding,\n      termsOfServiceUrl: props.termsOfServiceUrl,\n      title: props.title,\n      titleIconUrl: props.titleIcon,\n    };\n  }, [\n    props.privacyPolicyUrl,\n    props.showThirdwebBranding,\n    props.termsOfServiceUrl,\n    props.title,\n    props.titleIcon,\n  ]);\n\n  return (\n    <WalletUIStatesProvider theme={props.theme} isOpen={true}>\n      <ConnectModal\n        onClose={props.onClose}\n        shouldSetActive={props.setActive === undefined ? true : props.setActive}\n        accountAbstraction={props.accountAbstraction}\n        auth={props.auth}\n        chain={props.chain}\n        client={props.client}\n        connectLocale={props.connectLocale}\n        meta={meta}\n        size={size}\n        welcomeScreen={props.welcomeScreen}\n        localeId={props.locale || \"en_US\"}\n        onConnect={props.onConnect}\n        recommendedWallets={props.recommendedWallets}\n        showAllWallets={props.showAllWallets}\n        wallets={wallets}\n        chains={props.chains}\n        walletConnect={props.walletConnect}\n      />\n    </WalletUIStatesProvider>\n  );\n}\n\n/**\n * Options for configuring Connect Modal for [`useConnectModal`](https://portal.thirdweb.com/references/typescript/v5/useConnectModal) hook\n * @connectWallet\n */\nexport type UseConnectModalOptions = {\n  /**\n   * Whether to set the connected wallet as active wallet or not\n   *\n   * By default, It is set to `true`\n   *\n   * You can set it to `false` and use the retunred wallet from the `connect` method if you want to connect wallet without setting it as active wallet\n   *\n   * @example\n   * ```ts\n   * function Example() {\n   *  const { connect } = useConnectModal();\n   *  return <button> onClick={async () => {\n   *  const wallet = await connect({ setActive: false, client });\n   * }}>\n   *  Connect\n   * </button>\n   * }\n   * ```\n   */\n  setActive?: boolean;\n\n  /**\n   * A client is the entry point to the thirdweb SDK.\n   * It is required for all other actions.\n   * You can create a client using the `createThirdwebClient` function. Refer to the [Creating a Client](https://portal.thirdweb.com/typescript/v5/client) documentation for more information.\n   *\n   * You must provide a `clientId` or `secretKey` in order to initialize a client. Pass `clientId` if you want for client-side usage and `secretKey` for server-side usage.\n   *\n   * ```tsx\n   * import { createThirdwebClient } from \"thirdweb\";\n   *\n   * const client = createThirdwebClient({\n   *  clientId: \"<your_client_id>\",\n   * })\n   * ```\n   */\n  client: ThirdwebClient;\n\n  /**\n   * By default - Connect Modal UI uses the `en-US` locale for english language users.\n   *\n   * You can customize the language used in the Connect Modal UI by setting the `locale` prop.\n   *\n   * Refer to the [`LocaleId`](https://portal.thirdweb.com/references/typescript/v5/LocaleId) type for supported locales.\n   */\n  locale?: LocaleId;\n\n  /**\n   * Array of supported wallets. If not provided, default wallets will be used.\n   * @example\n   * ```tsx\n   * import { AutoConnect } from \"thirdweb/react\";\n   * import { createWallet, inAppWallet } from \"thirdweb/wallets\";\n   *\n   * const wallets = [\n   *   inAppWallet(),\n   *   createWallet(\"io.metamask\"),\n   *   createWallet(\"com.coinbase.wallet\"),\n   *   createWallet(\"me.rainbow\"),\n   * ];\n   *\n   * function Example() {\n   *  const { connect } = useConnectModal();\n   *  return <button> onClick={() => connect({ wallets, client })}> Connect </button>\n   * }\n   * ```\n   *\n   * If no wallets are specified. The component will show All the EIP-6963 compliant installed wallet extensions, as well as below default wallets:\n   *\n   * ```tsx\n   * const defaultWallets = [\n   *  inAppWallet(),\n   *  createWallet(\"io.metamask\"),\n   *  createWallet(\"com.coinbase.wallet\"),\n   *  createWallet(\"me.rainbow\"),\n   *  createWallet(\"io.zerion.wallet\"),\n   * ]\n   * ```\n   *\n   * Connect Modal also shows a \"All wallets\" button at the end of wallet list which allows user to connect to any of the 500+ wallets\n   */\n  wallets?: Wallet[];\n\n  /**\n   * Metadata of the app that will be passed to connected wallet. Setting this is highly recommended.\n   *\n   * Some wallets display this information to the user when they connect to your app.\n   * @example\n   * ```ts\n   * {\n   *   name: \"My App\",\n   *   url: \"https://my-app.com\",\n   *   description: \"some description about your app\",\n   *   logoUrl: \"https://path/to/my-app/logo.svg\",\n   * };\n   * ```\n   */\n  appMetadata?: AppMetadata;\n\n  /**\n   * The [`Chain`](https://portal.thirdweb.com/references/typescript/v5/Chain) object of the blockchain you want the wallet to connect to\n   *\n   * If a `chain` is not specified, Wallet will be connected to whatever is the default set in the wallet.\n   *\n   * If a `chain` is specified, Wallet will be prompted to switch to given chain after connection if it is not already connected to it.\n   * This ensures that the wallet is connected to the correct blockchain before interacting with your app.\n   *\n   * You can create a `Chain` object using the [`defineChain`](https://portal.thirdweb.com/references/typescript/v5/defineChain) function.\n   * At minimum, you need to pass the `id` of the blockchain to `defineChain` function to create a `Chain` object.\n   * ```\n   */\n  chain?: Chain;\n\n  /**\n   * Array of chains that your app supports.\n   *\n   * This is only relevant if your app is a multi-chain app and works across multiple blockchains.\n   * If your app only works on a single blockchain, you should only specify the `chain` prop.\n   *\n   * Given list of chains will be sent to wallet at the time of connection if the wallet supports requesting multiple chains ( example: WalletConnect ) so that users can switch between the chains post connection easily\n   *\n   * You can create a `Chain` object using the [`defineChain`](https://portal.thirdweb.com/references/typescript/v5/defineChain) function.\n   * At minimum, you need to pass the `id` of the blockchain to `defineChain` function to create a `Chain` object.\n   *\n   * ```tsx\n   * import { defineChain } from \"thirdweb/react\";\n   *\n   * const polygon = defineChain({\n   *   id: 137,\n   * });\n   * ```\n   */\n  chains?: Chain[];\n\n  /**\n   * Set the theme for the Connect Modal. By default it is set to `\"dark\"`\n   *\n   * theme can be set to either `\"dark\"`, `\"light\"` or a custom theme object.\n   * You can also import [`lightTheme`](https://portal.thirdweb.com/references/typescript/v5/lightTheme)\n   * or [`darkTheme`](https://portal.thirdweb.com/references/typescript/v5/darkTheme)\n   * functions from `thirdweb/react` to use the default themes as base and overrides parts of it.\n   * @example\n   * ```ts\n   * import { lightTheme } from \"thirdweb/react\";\n   *\n   * const customTheme = lightTheme({\n   *  colors: {\n   *    modalBg: 'red'\n   *  }\n   * })\n   * ```\n   */\n  theme?: \"dark\" | \"light\" | Theme;\n\n  /**\n   * Configure options for WalletConnect\n   *\n   * By default WalletConnect uses the thirdweb's default project id.\n   * Setting your own project id is recommended.\n   *\n   * You can create a project id by signing up on [walletconnect.com](https://walletconnect.com/)\n   */\n  walletConnect?: {\n    projectId?: string;\n  };\n\n  /**\n   * Enable Account abstraction for all wallets. This will connect to the users's smart account based on the connected personal wallet and the given options.\n   *\n   * This allows to sponsor gas fees for your user's transaction using the thirdweb account abstraction infrastructure.\n   *\n   * ```tsx\n   * * function Example() {\n   *  const { connect } = useConnectModal();\n   *\n   * async function handleConnect() {\n   *  await connect({\n   *    client,\n   *    accountAbstraction: {\n   *      factoryAddress: \"0x123...\",\n   *      chain: sepolia,\n   *      sponsorGas: true\n   *    }\n   *  })\n   * }\n   *\n   *  return (\n   *  <button> onClick={handleConnect}>\n   *    Connect\n   *  </button>\n   * )\n   *\n   * }\n   * ```\n   */\n  accountAbstraction?: SmartWalletOptions;\n\n  /**\n   * Wallets to show as recommended in the Connect Modal\n   */\n  recommendedWallets?: Wallet[];\n\n  /**\n   * By default, Connect modal shows a \"All Wallets\" button that shows a list of 500+ wallets.\n   *\n   * You can disable this button by setting `showAllWallets` prop to `false`\n   */\n  showAllWallets?: boolean;\n\n  /**\n   * Title to show in Connect Modal\n   *\n   * The default is `\"Connect\"`\n   */\n  title?: string;\n\n  /**\n   * Replace the default thirdweb icon next to Modal title with your own icon\n   *\n   * Set to empty string (`\"\"`) to hide the icon\n   */\n  titleIcon?: string;\n\n  /**\n   * Set the size of the connect modal on desktop - `\"compact\"` or `\"wide\"`\n   *\n   * Modal size is always `compact` on mobile\n   *\n   * By default it is `\"wide\"` for desktop.\n   */\n  size?: \"compact\" | \"wide\";\n\n  /**\n   * URL of the \"terms of service\" page\n   *\n   * If provided, Modal will show a Terms of Service message at the bottom with below link\n   */\n  termsOfServiceUrl?: string;\n\n  /**\n   * URL of the \"privacy policy\" page\n   *\n   * If provided, Modal will show a Privacy Policy message at the bottom with below link\n   */\n  privacyPolicyUrl?: string;\n\n  /**\n   * Customize the welcome screen. This prop is only applicable when modal size prop is set to \"wide\". On \"wide\" Modal size, a welcome screen is shown on the right side of the modal.\n   *\n   * This screen can be customized in two ways\n   *\n   * #### 1. Customize Metadata and Image\n   * ```tsx\n   * const welcomeScreen = {\n   *  title: \"your title\",\n   *  subtitle: \"your subtitle\",\n   *  img: {\n   *   src: \"https://your-image-url.png\",\n   *   width: 300,\n   *   height: 50,\n   *  },\n   * }\n   * ```\n   *\n   * #### 2. Render Custom Component\n   *\n   * ```tsx\n   * const welcomeScreen = () => <YourCustomComponent />\n   * ```\n   */\n  welcomeScreen?: WelcomeScreen;\n\n  /**\n   * By default Connect Modal shows \"Powered by Thirdweb\" branding at the bottom of the Modal.\n   *\n   * If you want to hide the branding, set this prop to `false`\n   */\n  showThirdwebBranding?: boolean;\n\n  /**\n   * Enable SIWE (Sign in with Ethererum) by passing an object of type `SiweAuthOptions` to\n   * enforce the users to sign a message after connecting their wallet to authenticate themselves.\n   *\n   * Refer to the [`SiweAuthOptions`](https://portal.thirdweb.com/references/typescript/v5/SiweAuthOptions) for more details\n   */\n  auth?: SiweAuthOptions;\n};\n\n// TODO: consilidate Button/Embed/Modal props into one type with extras\n", "\"use client\";\n\nimport {\n  type ThirdwebContract,\n  getContract,\n} from \"../../../../../../contract/contract.js\";\nimport { getContractMetadata } from \"../../../../../../extensions/common/read/getContractMetadata.js\";\nimport { getNFT } from \"../../../../../../extensions/erc1155/read/getNFT.js\";\nimport type { PreparedTransaction } from \"../../../../../../transaction/prepare-transaction.js\";\nimport type { BaseTransactionOptions } from \"../../../../../../transaction/types.js\";\nimport type { Account } from \"../../../../../../wallets/interfaces/wallet.js\";\nimport { useReadContract } from \"../../../../../core/hooks/contract/useReadContract.js\";\nimport { useSendAndConfirmTransaction } from \"../../../../../core/hooks/transaction/useSendAndConfirmTransaction.js\";\nimport { useActiveAccount } from \"../../../../../core/hooks/wallets/useActiveAccount.js\";\nimport { TransactionButton } from \"../../../TransactionButton/index.js\";\nimport type {\n  ClaimButtonProps,\n  ClaimParams,\n  Erc20ClaimParams,\n  Erc721ClaimParams,\n  Erc1155ClaimParams,\n} from \"./types.js\";\n\n/**\n * This button is used to claim tokens (NFT or ERC20) from a given thirdweb Drop contract.\n *\n * there are 3 type of Drop contract: NFT Drop (DropERC721), Edition Drop (DropERC1155) and Token Drop (DropERC20)\n *\n * Learn more: https://thirdweb.com/explore/drops\n *\n *\n * Note: This button only works with thirdweb Drop contracts.\n * For custom contract, please use [`TransactionButton`](https://portal.thirdweb.com/references/typescript/v5/TransactionButton)\n * @param props\n * @returns A wrapper for TransactionButton\n *\n * @component\n * @example\n *\n * Example for claiming NFT from an NFT Drop contract\n * ```tsx\n * import { ClaimButton } from \"thirdweb/react\";\n * import { ethereum } from \"thirdweb/chains\";\n *\n * <ClaimButton\n *   contractAddress=\"0x...\" // contract address of the NFT Drop\n *   chain={ethereum}\n *   client={client}\n *   claimParams={{\n *     type: \"ERC721\",\n *     quantity: 1n, // claim 1 token\n *   }}\n * >\n *   Claim now\n * </ClaimButton>\n * ```\n *\n * For Edition Drop (ERC1155)\n * ```tsx\n * <ClaimButton\n *   contractAddress=\"0x...\" // contract address of the Edition Drop\n *   chain={ethereum}\n *   client={client}\n *   claimParams={{\n *     type: \"ERC1155\",\n *     quantity: 1n,\n *     tokenId: 0n,\n *   }}\n * >\n *   Claim now\n * </ClaimButton>\n * ```\n *\n * For Token Drop (ERC20)\n * ```tsx\n * <ClaimButton\n *   contractAddress=\"0x...\" // contract address of the Token Drop\n *   chain={ethereum}\n *   client={client}\n *   claimParams={{\n *     type: \"ERC20\",\n *     quantity: \"100\", // claim 100 ERC20 tokens\n *     // instead of `quantity`, you can also use `quantityInWei` (bigint)\n *   }}\n * >\n *   Claim now\n * </ClaimButton>\n * ```\n *\n * Attach custom Pay metadata\n * ```tsx\n * <ClaimButton\n *   payModal={{\n *     metadata: {\n *       name: \"Van Gogh Starry Night\",\n *       image: \"https://unsplash.com/starry-night.png\"\n *     }\n *   }}\n * >...</ClaimButton>\n *\n * ```\n *\n * Since this button uses the `TransactionButton`, it can take in any props that can be passed\n * to the [`TransactionButton`](https://portal.thirdweb.com/references/typescript/v5/TransactionButton)\n *\n *\n * For error handling & callbacks on transaction-sent and transaction-confirmed,\n * please refer to the TransactionButton docs.\n * @transaction\n */\nexport function ClaimButton(props: ClaimButtonProps) {\n  const { children, contractAddress, client, chain, claimParams, payModal } =\n    props;\n  const defaultPayModalMetadata = payModal ? payModal.metadata : undefined;\n  const contract = getContract({\n    address: contractAddress,\n    client,\n    chain,\n  });\n\n  const { data: payMetadata } = useReadContract(getPayMetadata, {\n    contract,\n    tokenId: claimParams.type === \"ERC1155\" ? claimParams.tokenId : undefined,\n    queryOptions: {\n      enabled: !defaultPayModalMetadata,\n    },\n  });\n  const account = useActiveAccount();\n  const { mutateAsync } = useSendAndConfirmTransaction();\n  return (\n    <TransactionButton\n      payModal={{\n        metadata: defaultPayModalMetadata || payMetadata,\n        ...payModal,\n      }}\n      transaction={async () => {\n        if (!account) {\n          throw new Error(\"No account detected\");\n        }\n        const [claimTx, { getApprovalForTransaction }] = await Promise.all([\n          getClaimTransaction({\n            contract,\n            account,\n            claimParams,\n          }),\n          import(\n            \"../../../../../../extensions/erc20/write/getApprovalForTransaction.js\"\n          ),\n        ]);\n        const approveTx = await getApprovalForTransaction({\n          transaction: claimTx,\n          account,\n        });\n        if (approveTx) {\n          await mutateAsync(approveTx);\n        }\n        return claimTx;\n      }}\n      {...props}\n    >\n      {children}\n    </TransactionButton>\n  );\n}\n\n/**\n * We can only get the image and name for Edition Drop\n * For NFT Drop and Token Drop we fall back to the name & image of the contract\n * @internal\n */\nasync function getPayMetadata(\n  options: BaseTransactionOptions<{ tokenId?: bigint }>,\n): Promise<{ name?: string; image?: string }> {\n  const { contract, tokenId } = options;\n  const [contractMetadata, nft] = await Promise.all([\n    getContractMetadata(options),\n    tokenId ? getNFT({ contract, tokenId }) : undefined,\n  ]);\n  if (tokenId) {\n    return {\n      image: nft?.metadata?.image,\n      name: nft?.metadata?.name,\n    };\n  }\n  return {\n    image: contractMetadata?.image,\n    name: contractMetadata?.name,\n  };\n}\n\n/**\n * @internal Export for test\n */\nasync function getClaimTransaction({\n  contract,\n  account,\n  claimParams,\n}: {\n  contract: ThirdwebContract;\n  account: Account | undefined;\n  claimParams: ClaimParams;\n}): Promise<PreparedTransaction> {\n  switch (claimParams.type) {\n    case \"ERC721\":\n      return await getERC721ClaimTo({ contract, account, claimParams });\n    case \"ERC1155\":\n      return await getERC1155ClaimTo({ contract, account, claimParams });\n    case \"ERC20\": {\n      return await getERC20ClaimTo({ contract, account, claimParams });\n    }\n    default:\n      throw new Error(\n        \"Invalid contract type. Must be either NFT Drop (ERC721), Edition Drop (ERC1155) or Token Drop (ERC20)\",\n      );\n  }\n}\n\n/**\n * @internal\n */\nexport async function getERC721ClaimTo({\n  contract,\n  account,\n  claimParams,\n}: {\n  contract: ThirdwebContract;\n  account: Account | undefined;\n  claimParams: Erc721ClaimParams;\n}) {\n  const { claimTo } = await import(\n    \"../../../../../../extensions/erc721/drops/write/claimTo.js\"\n  );\n\n  return claimTo({\n    contract,\n    to: claimParams.to || account?.address || \"\",\n    quantity: claimParams.quantity,\n    from: claimParams.from,\n  });\n}\n\n/**\n * @internal\n */\nexport async function getERC1155ClaimTo({\n  contract,\n  account,\n  claimParams,\n}: {\n  contract: ThirdwebContract;\n  account: Account | undefined;\n  claimParams: Erc1155ClaimParams;\n}) {\n  const { claimTo } = await import(\n    \"../../../../../../extensions/erc1155/drops/write/claimTo.js\"\n  );\n\n  return claimTo({\n    contract,\n    to: claimParams.to || account?.address || \"\",\n    quantity: claimParams.quantity,\n    tokenId: claimParams.tokenId,\n    from: claimParams.from,\n  });\n}\n\n/**\n * @internal\n */\nexport async function getERC20ClaimTo({\n  contract,\n  account,\n  claimParams,\n}: {\n  contract: ThirdwebContract;\n  account: Account | undefined;\n  claimParams: Erc20ClaimParams;\n}) {\n  // Ideally we should check if the contract is ERC20 using `isERC20`\n  // however TokenDrop doesn't have `supportsInterface` so it doesn't work\n  const { claimTo } = await import(\n    \"../../../../../../extensions/erc20/drops/write/claimTo.js\"\n  );\n\n  if (\"quantity\" in claimParams) {\n    return claimTo({\n      contract,\n      to: claimParams.to || account?.address || \"\",\n      quantity: claimParams.quantity,\n      from: claimParams.from,\n    });\n  }\n  if (\"quantityInWei\" in claimParams) {\n    return claimTo({\n      contract,\n      to: claimParams.to || account?.address || \"\",\n      quantityInWei: claimParams.quantityInWei,\n      from: claimParams.from,\n    });\n  }\n  throw new Error(\"Missing quantity or quantityInWei\");\n}\n", "\"use client\";\n\nimport { useCallback } from \"react\";\nimport type { Chain } from \"../../../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../../../client/client.js\";\nimport { getContract } from \"../../../../../../contract/contract.js\";\nimport { getListing } from \"../../../../../../extensions/marketplace/direct-listings/read/getListing.js\";\nimport type { BaseTransactionOptions } from \"../../../../../../transaction/types.js\";\nimport { useReadContract } from \"../../../../../core/hooks/contract/useReadContract.js\";\nimport type { TransactionButtonProps } from \"../../../../../core/hooks/transaction/transaction-button-utils.js\";\nimport { useSendAndConfirmTransaction } from \"../../../../../core/hooks/transaction/useSendAndConfirmTransaction.js\";\nimport { useActiveAccount } from \"../../../../../core/hooks/wallets/useActiveAccount.js\";\nimport { TransactionButton } from \"../../../TransactionButton/index.js\";\n\nexport type BuyDirectListingButtonProps = Omit<\n  TransactionButtonProps,\n  \"transaction\"\n> & {\n  /**\n   * The contract address of the Marketplace v3 contract.\n   */\n  contractAddress: string;\n\n  /**\n   * The chain which the Drop contract is deployed on\n   */\n  chain: Chain;\n\n  /**\n   * thirdweb Client\n   */\n  client: ThirdwebClient;\n\n  /**\n   * ID of the marketplace's DirectListing\n   */\n  listingId: bigint;\n\n  /**\n   * Qty to buy (optional)\n   *\n   * - For ERC721 listing: the `quantity` is always hard-coded to 1n - passing this props doesn't do anything\n   *\n   * - For ERC1155 listing: the `quantity` defaults to the quantity of the listing if not specified.\n   *\n   * The component will also throw an error if  you pass a `quantity` and it's greater than the listing's quantity\n   */\n  quantity?: bigint;\n};\n\n/**\n * This button is used with thirdweb Marketplace v3 contract, for buying NFT(s) from a listing.\n *\n * Under the hood, it prepares a transaction using the [`buyFromListing` extension](https://portal.thirdweb.com/references/typescript/v5/marketplace/buyFromListing)\n * and then pass it to a <TransactionButton />\n *\n * Since it uses the TransactionButton, it can take in any props that can be passed\n * to the [`TransactionButton`](https://portal.thirdweb.com/references/typescript/v5/TransactionButton)\n *\n * @param props props of type [BuyDirectListingButtonProps](https://portal.thirdweb.com/references/typescript/v5/BuyDirectListingButtonProps)\n * @example\n * ```tsx\n * import { BuyDirectListingButton } from \"thirdweb/react\";\n *\n * <BuyDirectListingButton\n *   contractAddress=\"0x...\" // contract address of the marketplace v3\n *   chain={...} // the chain which the marketplace contract is deployed on\n *   client={...} // thirdweb client\n *   listingId={100n} // the listingId or the item you want to buy\n *   quantity={1n} // optional - see the docs to learn more\n * >\n *   Buy NFT\n * </BuyDirectListingButton>\n * ```\n *\n * For error handling & callbacks on transaction-sent and transaction-confirmed,\n * please refer to the TransactionButton docs.\n * @component\n * @transaction\n */\nexport function BuyDirectListingButton(props: BuyDirectListingButtonProps) {\n  const {\n    contractAddress,\n    listingId,\n    children,\n    chain,\n    client,\n    quantity,\n    payModal,\n  } = props;\n  const defaultPayModalMetadata = payModal ? payModal.metadata : undefined;\n  const account = useActiveAccount();\n  const contract = getContract({\n    address: contractAddress,\n    client,\n    chain,\n  });\n\n  const { data: payMetadata } = useReadContract(getPayMetadata, {\n    contract,\n    listingId,\n    queryOptions: {\n      enabled: !defaultPayModalMetadata,\n    },\n  });\n\n  const { mutateAsync } = useSendAndConfirmTransaction();\n\n  const prepareBuyTransaction = useCallback(async () => {\n    if (!account) {\n      throw new Error(\"No account detected\");\n    }\n    const [listing, { getApprovalForTransaction }, { buyFromListing }] =\n      await Promise.all([\n        getListing({\n          contract,\n          listingId,\n        }),\n        import(\n          \"../../../../../../extensions/erc20/write/getApprovalForTransaction.js\"\n        ),\n        import(\n          \"../../../../../../extensions/marketplace/direct-listings/write/buyFromListing.js\"\n        ),\n      ]);\n    if (!listing) {\n      throw new Error(`Could not retrieve listing with ID: ${listingId}`);\n    }\n\n    let _quantity = 1n;\n    // For ERC721 the quantity should always be 1n. We throw an error if user passes a different props\n    if (listing.asset.type === \"ERC721\") {\n      if (typeof quantity === \"bigint\" && (quantity !== 1n || quantity < 0n)) {\n        throw new Error(\n          \"Invalid quantity. This is an ERC721 listing & quantity is always `1n`\",\n        );\n      }\n    } else if (listing.asset.type === \"ERC1155\") {\n      if (typeof quantity === \"bigint\") {\n        if (quantity > listing.quantity) {\n          throw new Error(\n            `quantity exceeds available amount. Available: ${listing.quantity.toString()}`,\n          );\n        }\n        if (quantity < 0n) {\n          throw new Error(\"Invalid quantity. Should be at least 1n\");\n        }\n        _quantity = quantity;\n      }\n      _quantity = listing.quantity;\n    }\n\n    const buyTx = buyFromListing({\n      contract,\n      listingId,\n      quantity: _quantity,\n      recipient: account?.address || \"\",\n    });\n\n    const approveTx = await getApprovalForTransaction({\n      transaction: buyTx,\n      account,\n    });\n\n    if (approveTx) {\n      await mutateAsync(approveTx);\n    }\n\n    return buyTx;\n  }, [account, contract, quantity, listingId, mutateAsync]);\n\n  return (\n    <TransactionButton\n      payModal={{\n        metadata: defaultPayModalMetadata || payMetadata,\n        ...payModal,\n      }}\n      transaction={() => prepareBuyTransaction()}\n      {...props}\n    >\n      {children}\n    </TransactionButton>\n  );\n}\n\n/**\n * @internal\n */\nasync function getPayMetadata(\n  options: BaseTransactionOptions<{ listingId: bigint }>,\n): Promise<{ name?: string; image?: string }> {\n  const listing = await getListing(options);\n  if (!listing) {\n    return { name: undefined, image: undefined };\n  }\n  return {\n    name: listing.asset?.metadata?.name,\n    image: listing.asset?.metadata?.image,\n  };\n}\n", "\"use client\";\n\nimport { useCallback } from \"react\";\nimport type { Chain } from \"../../../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../../../client/client.js\";\nimport { getContract } from \"../../../../../../contract/contract.js\";\nimport { isERC721 } from \"../../../../../../extensions/erc721/read/isERC721.js\";\nimport { isERC1155 } from \"../../../../../../extensions/erc1155/read/isERC1155.js\";\nimport {\n  type CreateListingParams,\n  createListing,\n} from \"../../../../../../extensions/marketplace/direct-listings/write/createListing.js\";\nimport type { BaseTransactionOptions } from \"../../../../../../transaction/types.js\";\nimport { useReadContract } from \"../../../../../core/hooks/contract/useReadContract.js\";\nimport type { TransactionButtonProps } from \"../../../../../core/hooks/transaction/transaction-button-utils.js\";\nimport { useSendAndConfirmTransaction } from \"../../../../../core/hooks/transaction/useSendAndConfirmTransaction.js\";\nimport { useActiveAccount } from \"../../../../../core/hooks/wallets/useActiveAccount.js\";\nimport { TransactionButton } from \"../../../TransactionButton/index.js\";\n\nexport type CreateDirectListingButtonProps = Omit<\n  TransactionButtonProps,\n  \"transaction\"\n> &\n  CreateListingParams & {\n    contractAddress: string;\n    chain: Chain;\n    client: ThirdwebClient;\n  };\n\n/**\n * This button is used to create Direct listings for the thirdweb Marketplace v3 contract\n *\n * It uses the [`TransactionButton`](https://portal.thirdweb.com/references/typescript/v5/TransactionButton)\n * and the [`createListing` extension](https://portal.thirdweb.com/references/typescript/v5/marketplace/createListing) under the hood\n * which means it inherits all the props of those 2 components.\n *\n * @example\n * ```tsx\n * import { CreateDirectListingButton } from \"thirdweb/react\";\n *\n * <CreateDirectListingButton\n *   contractAddress=\"0x...\" // contract address for the marketplace-v3\n *   chain={...} // the chain which the marketplace contract is deployed on\n *\n *   // These props below are the same props for `createListing`\n *   // to get the full list, check the docs link above\n *   tokenId={0n}\n *   assetContractAddress=\"0x...\" // The NFT contract address whose NFT(s) you want to sell\n *   pricePerToken={\"0.1\"} // sell for 0.1 <native token>\n * >\n *   Sell NFT\n * </CreateDirectListingButton>\n * ```\n *\n * For error handling & callbacks on transaction-sent and transaction-confirmed,\n * please refer to the TransactionButton docs.\n * @component\n * @transaction\n */\nexport function CreateDirectListingButton(\n  props: CreateDirectListingButtonProps,\n) {\n  const {\n    contractAddress,\n    chain,\n    client,\n    children,\n    payModal,\n    assetContractAddress,\n    tokenId,\n  } = props;\n  const marketplaceContract = getContract({\n    address: contractAddress,\n    chain,\n    client,\n  });\n  const account = useActiveAccount();\n  const defaultPayModalMetadata = payModal ? payModal.metadata : undefined;\n  const nftContract = getContract({\n    address: assetContractAddress,\n    chain,\n    client,\n  });\n  const { data: payMetadata } = useReadContract(getPayMetadata, {\n    contract: nftContract,\n    tokenId,\n    queryOptions: {\n      enabled: !defaultPayModalMetadata,\n    },\n  });\n  const { mutateAsync } = useSendAndConfirmTransaction();\n\n  const prepareTransaction = useCallback(async () => {\n    if (!account) {\n      throw new Error(\"No account detected\");\n    }\n    const [is721, is1155] = await Promise.all([\n      isERC721({ contract: nftContract }),\n      isERC1155({ contract: nftContract }),\n    ]);\n    if (!is1155 && !is721) {\n      throw new Error(\"Asset must either be ERC721 or ERC1155\");\n    }\n    // Check for token approval\n    if (is1155) {\n      const [{ isApprovedForAll }, { setApprovalForAll }] = await Promise.all([\n        import(\n          \"../../../../../../extensions/erc1155/__generated__/IERC1155/read/isApprovedForAll.js\"\n        ),\n        import(\n          \"../../../../../../extensions/erc1155/__generated__/IERC1155/write/setApprovalForAll.js\"\n        ),\n      ]);\n      const isApproved = await isApprovedForAll({\n        contract: nftContract,\n        operator: marketplaceContract.address,\n        owner: account.address,\n      });\n      if (!isApproved) {\n        const transaction = setApprovalForAll({\n          contract: nftContract,\n          operator: marketplaceContract.address,\n          approved: true,\n        });\n        await mutateAsync(transaction);\n      }\n    } else {\n      const [{ isApprovedForAll }, { setApprovalForAll }, { getApproved }] =\n        await Promise.all([\n          import(\n            \"../../../../../../extensions/erc721/__generated__/IERC721A/read/isApprovedForAll.js\"\n          ),\n          import(\n            \"../../../../../../extensions/erc721/__generated__/IERC721A/write/setApprovalForAll.js\"\n          ),\n          import(\n            \"../../../../../../extensions/erc721/__generated__/IERC721A/read/getApproved.js\"\n          ),\n        ]);\n      const [isApproved, tokenApproved] = await Promise.all([\n        isApprovedForAll({\n          contract: nftContract,\n          operator: marketplaceContract.address,\n          owner: account.address,\n        }),\n        getApproved({ contract: nftContract, tokenId: props.tokenId }),\n      ]);\n\n      if (\n        !isApproved &&\n        tokenApproved.toLowerCase() !==\n          marketplaceContract.address.toLowerCase()\n      ) {\n        const transaction = setApprovalForAll({\n          contract: nftContract,\n          operator: marketplaceContract.address,\n          approved: true,\n        });\n        await mutateAsync(transaction);\n      }\n    }\n    const listingTx = createListing({\n      contract: marketplaceContract,\n      ...props,\n    });\n\n    return listingTx;\n  }, [marketplaceContract, props, account, mutateAsync, nftContract]);\n\n  return (\n    <TransactionButton\n      transaction={() => prepareTransaction()}\n      payModal={{\n        metadata: defaultPayModalMetadata || payMetadata,\n        ...payModal,\n      }}\n      {...props}\n    >\n      {children}\n    </TransactionButton>\n  );\n}\n\n/**\n * @internal\n */\nasync function getPayMetadata(\n  options: BaseTransactionOptions<{\n    tokenId: bigint;\n  }>,\n): Promise<{ name?: string; image?: string }> {\n  const [\n    { getContractMetadata },\n    { getNFT: getERC721 },\n    { getNFT: getERC1155 },\n  ] = await Promise.all([\n    import(\"../../../../../../extensions/common/read/getContractMetadata.js\"),\n    import(\"../../../../../../extensions/erc721/read/getNFT.js\"),\n    import(\"../../../../../../extensions/erc1155/read/getNFT.js\"),\n  ]);\n  const [is721, is1155, contractMetadata] = await Promise.all([\n    isERC721(options),\n    isERC1155(options),\n    getContractMetadata(options),\n  ]);\n  if (is721) {\n    const nft = await getERC721(options);\n    return {\n      image: nft?.metadata?.image,\n      name: nft?.metadata?.name,\n    };\n  }\n\n  if (is1155) {\n    const nft = await getERC1155(options);\n    return {\n      image: nft?.metadata?.image,\n      name: nft?.metadata?.name,\n    };\n  }\n\n  return {\n    image: contractMetadata?.image,\n    name: contractMetadata?.name,\n  };\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"createListing\" function.\n */\nexport type CreateListingParams = WithOverrides<{\n  params: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"_params\";\n    components: [\n      { type: \"address\"; name: \"assetContract\" },\n      { type: \"uint256\"; name: \"tokenId\" },\n      { type: \"uint256\"; name: \"quantity\" },\n      { type: \"address\"; name: \"currency\" },\n      { type: \"uint256\"; name: \"pricePerToken\" },\n      { type: \"uint128\"; name: \"startTimestamp\" },\n      { type: \"uint128\"; name: \"endTimestamp\" },\n      { type: \"bool\"; name: \"reserved\" },\n    ];\n  }>;\n}>;\n\nexport const FN_SELECTOR = \"0x746415b5\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"tuple\",\n    name: \"_params\",\n    components: [\n      {\n        type: \"address\",\n        name: \"assetContract\",\n      },\n      {\n        type: \"uint256\",\n        name: \"tokenId\",\n      },\n      {\n        type: \"uint256\",\n        name: \"quantity\",\n      },\n      {\n        type: \"address\",\n        name: \"currency\",\n      },\n      {\n        type: \"uint256\",\n        name: \"pricePerToken\",\n      },\n      {\n        type: \"uint128\",\n        name: \"startTimestamp\",\n      },\n      {\n        type: \"uint128\",\n        name: \"endTimestamp\",\n      },\n      {\n        type: \"bool\",\n        name: \"reserved\",\n      },\n    ],\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"uint256\",\n    name: \"listingId\",\n  },\n] as const;\n\n/**\n * Checks if the `createListing` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `createListing` method is supported.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { isCreateListingSupported } from \"thirdweb/extensions/marketplace\";\n *\n * const supported = isCreateListingSupported([\"0x...\"]);\n * ```\n */\nexport function isCreateListingSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"createListing\" function.\n * @param options - The options for the createListing function.\n * @returns The encoded ABI parameters.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { encodeCreateListingParams } from \"thirdweb/extensions/marketplace\";\n * const result = encodeCreateListingParams({\n *  params: ...,\n * });\n * ```\n */\nexport function encodeCreateListingParams(options: CreateListingParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.params]);\n}\n\n/**\n * Encodes the \"createListing\" function into a Hex string with its parameters.\n * @param options - The options for the createListing function.\n * @returns The encoded hexadecimal string.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { encodeCreateListing } from \"thirdweb/extensions/marketplace\";\n * const result = encodeCreateListing({\n *  params: ...,\n * });\n * ```\n */\nexport function encodeCreateListing(options: CreateListingParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeCreateListingParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"createListing\" function on the contract.\n * @param options - The options for the \"createListing\" function.\n * @returns A prepared transaction object.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { createListing } from \"thirdweb/extensions/marketplace\";\n *\n * const transaction = createListing({\n *  contract,\n *  params: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function createListing(\n  options: BaseTransactionOptions<\n    | CreateListingParams\n    | {\n        asyncParams: () => Promise<CreateListingParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.params] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { Address } from \"abitype\";\nimport {\n  NATIVE_TOKEN_ADDRESS,\n  isNativeTokenAddress,\n} from \"../../../../constants/addresses.js\";\nimport { getContract } from \"../../../../contract/contract.js\";\nimport { eth_getBlockByNumber } from \"../../../../rpc/actions/eth_getBlockByNumber.js\";\nimport { getRpcClient } from \"../../../../rpc/rpc.js\";\nimport type { BaseTransactionOptions } from \"../../../../transaction/types.js\";\nimport { toUnits } from \"../../../../utils/units.js\";\nimport { isERC721 } from \"../../../erc721/read/isERC721.js\";\nimport { isERC1155 } from \"../../../erc1155/read/isERC1155.js\";\nimport * as CreateListing from \"../../__generated__/IDirectListings/write/createListing.js\";\n\n/**\n * @extension MARKETPLACE\n */\nexport type CreateListingParams = {\n  /**\n   * The contract address of the asset being listed\n   */\n  assetContractAddress: Address;\n  /**\n   * The ID of the token being listed\n   */\n  tokenId: bigint;\n  /**\n   * The quantity of tokens to list\n   *\n   * For ERC721s, this value can be omitted.\n   * @default 1\n   */\n  quantity?: bigint;\n  /**\n   * The contract address of the currency to accept for the listing\n   * @default NATIVE_TOKEN_ADDRESS\n   */\n  currencyContractAddress?: Address;\n  /**\n   * The start time of the listing\n   * @default new Date()\n   */\n  startTimestamp?: Date;\n  /**\n   * The end time of the listing\n   * @default new Date() + 10 years\n   */\n  endTimestamp?: Date;\n  /**\n   * Whether the listing is reserved to be bought from a specific set of buyers\n   * @default false\n   */\n  isReservedListing?: boolean;\n} & (\n  | {\n      /**\n       * The price per token (in Ether)\n       */\n      pricePerToken: string;\n    }\n  | {\n      /**\n       * The price per token (in wei)\n       */\n      pricePerTokenWei: string;\n    }\n);\n\n/**\n * Creates a direct listing.\n * @param options The options for creating the direct listing.\n * @returns The result of creating the direct listing.\n * @extension MARKETPLACE\n * @example\n * ```typescript\n * import { createListing } from \"thirdweb/extensions/marketplace\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = createListing({\n *   assetContractAddress: \"0x...\", // the NFT contract address that you want to sell\n *   tokenId={0n}, // the token id you want to sell\n *   pricePerToken=\"0.1\" // sell for 0.1 <native token>\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function createListing(\n  options: BaseTransactionOptions<CreateListingParams>,\n) {\n  return CreateListing.createListing({\n    contract: options.contract,\n    asyncParams: async () => {\n      const assetContract = getContract({\n        ...options.contract,\n        address: options.assetContractAddress,\n      });\n\n      const rpcClient = getRpcClient(options.contract);\n\n      const [assetIsERC721, assetIsERC1155, lastestBlock] = await Promise.all([\n        isERC721({ contract: assetContract }),\n        isERC1155({ contract: assetContract }),\n        eth_getBlockByNumber(rpcClient, { blockTag: \"latest\" }),\n      ]);\n\n      // validate valid asset\n      if (!assetIsERC721 && !assetIsERC1155) {\n        throw new Error(\"AssetContract must implement ERC 1155 or ERC 721.\");\n      }\n\n      // validate the timestamps\n      let startTimestamp = BigInt(\n        Math.floor((options.startTimestamp ?? new Date()).getTime() / 1000),\n      );\n      const endTimestamp = BigInt(\n        Math.floor(\n          (\n            options.endTimestamp ??\n            new Date(Date.now() + 10 * 365 * 24 * 60 * 60 * 1000)\n          ).getTime() / 1000,\n        ),\n      );\n\n      if (startTimestamp <= lastestBlock.timestamp) {\n        // set the start time to the next block if it is in the past\n        startTimestamp = lastestBlock.timestamp + 1n;\n      }\n      if (startTimestamp >= endTimestamp) {\n        throw new Error(\"Start time must be before end time.\");\n      }\n\n      // valdiate quantity\n      let quantity: bigint;\n      if (assetIsERC721) {\n        // force quantity to 1 for ERC721s\n        quantity = 1n;\n      } else {\n        // otherwise use the provided quantity or default to 1\n        quantity = options.quantity ?? 1n;\n      }\n\n      // validate price\n      const currencyAddress =\n        options.currencyContractAddress ?? NATIVE_TOKEN_ADDRESS;\n      let pricePerToken: bigint;\n      if (\"pricePerToken\" in options) {\n        // for native token, we know decimals are 18\n        if (isNativeTokenAddress(currencyAddress)) {\n          pricePerToken = toUnits(options.pricePerToken, 18);\n        } else {\n          // otherwise get the decimals of the currency\n          const currencyContract = getContract({\n            ...options.contract,\n            address: currencyAddress,\n          });\n          const { decimals } = await import(\"../../../erc20/read/decimals.js\");\n          const currencyDecimals = await decimals({\n            contract: currencyContract,\n          });\n          pricePerToken = toUnits(options.pricePerToken, currencyDecimals);\n        }\n      } else {\n        pricePerToken = BigInt(options.pricePerTokenWei);\n      }\n\n      return {\n        params: {\n          assetContract: options.assetContractAddress,\n          tokenId: options.tokenId,\n          currency: options.currencyContractAddress ?? NATIVE_TOKEN_ADDRESS,\n          quantity,\n          pricePerToken,\n          startTimestamp,\n          endTimestamp,\n          reserved: options.isReservedListing ?? false,\n        },\n        overrides: {\n          extraGas: 50_000n, // add extra gas to account for router call\n        },\n      } as const;\n    },\n  });\n}\n\n/**\n * Checks if the `createListing` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `createListing` method is supported.\n * @extension MARKETPLACE\n * @example\n * ```ts\n * import { isCreateAuctionSupported } from \"thirdweb/extensions/marketplace\";\n *\n * const supported = isCreateAuctionSupported([\"0x...\"]);\n * ```\n */\nexport function isCreateListingSupported(availableSelectors: string[]) {\n  return CreateListing.isCreateListingSupported(availableSelectors);\n}\n", "\"use client\";\n\nimport { createContext, useContext } from \"react\";\nimport type { ThirdwebContract } from \"../../../../../contract/contract.js\";\n\n/**\n * Props for the <NFT> component\n * @component\n */\nexport type NFTProviderProps = {\n  /**\n   * The NFT contract address. Accepts both ERC721 and ERC1155 contracts\n   */\n  contract: ThirdwebContract;\n  /**\n   * The tokenId whose info you want to display\n   */\n  tokenId: bigint;\n};\n\n/**\n * @internal\n */\nexport const NFTProviderContext = /* @__PURE__ */ createContext<\n  NFTProviderProps | undefined\n>(undefined);\n\n/**\n * @internal\n */\nexport function useNFTContext() {\n  const ctx = useContext(NFTProviderContext);\n  if (!ctx) {\n    throw new Error(\n      \"NFTProviderContext not found. Make sure you are using NFTMedia, NFTDescription, etc. inside a <NFTProvider /> component\",\n    );\n  }\n  return ctx;\n}\n\n/**\n * A React context provider component that supplies NFT-related data to its child components.\n *\n * This component serves as a wrapper around the `NFTProviderContext.Provider` and passes\n * the provided NFT data down to all of its child components through the context API.\n *\n *\n * @param {React.PropsWithChildren<NFTProviderProps>} props - The props for the NFT provider\n *\n * @example\n * ```tsx\n * import { getContract } from \"thirdweb\";\n * import { NFTProvider, NFTMedia, NFTDescription, NFTName } from \"thirdweb/react\";\n *\n * const contract = getContract({\n *   address: \"0x...\",\n *   chain: ethereum,\n *   client: yourThirdwebClient,\n * });\n *\n * <NFTProvider contract={contract} tokenId={0n}>\n *    <NFTMedia />\n *    <NFTDescription />\n *    <NFTName />\n * </NFTProvider>\n * ```\n * @component\n * @nft\n * @beta\n */\nexport function NFTProvider(props: React.PropsWithChildren<NFTProviderProps>) {\n  return (\n    <NFTProviderContext.Provider value={props}>\n      {props.children}\n    </NFTProviderContext.Provider>\n  );\n}\n", "import { getNFT as getNFT721 } from \"../../../../../extensions/erc721/read/getNFT.js\";\nimport { getNFT as getNFT1155 } from \"../../../../../extensions/erc1155/read/getNFT.js\";\nimport type { NFT } from \"../../../../../utils/nft/parseNft.js\";\nimport { withCache } from \"../../../../../utils/promise/withCache.js\";\nimport type { NFTProviderProps } from \"./provider.js\";\n\n/**\n * @internal\n */\nexport async function getNFTInfo(options: NFTProviderProps): Promise<NFT> {\n  return withCache(\n    async () => {\n      const nft = await Promise.allSettled([\n        getNFT721({\n          ...options,\n          useIndexer: false, // TODO (insight): switch this call to only call insight once\n        }),\n        getNFT1155({\n          ...options,\n          useIndexer: false, // TODO (insight): switch this call to only call insight once\n        }),\n      ]).then(([possibleNFT721, possibleNFT1155]) => {\n        // getNFT extension always return an NFT object\n        // so we need to check if the tokenURI exists\n        if (\n          possibleNFT721.status === \"fulfilled\" &&\n          possibleNFT721.value.tokenURI\n        ) {\n          return possibleNFT721.value;\n        }\n        if (\n          possibleNFT1155.status === \"fulfilled\" &&\n          possibleNFT1155.value.tokenURI\n        ) {\n          return possibleNFT1155.value;\n        }\n        throw new Error(\"Failed to load NFT metadata\");\n      });\n      return nft;\n    },\n    {\n      cacheKey: `nft_info:${options.contract.chain.id}:${options.contract.address}:${options.tokenId.toString()}`,\n      cacheTime: 15 * 60 * 1000,\n    },\n  );\n}\n", "\"use client\";\n\nimport { type UseQueryOptions, useQuery } from \"@tanstack/react-query\";\nimport type { JSX } from \"react\";\nimport type { ThirdwebContract } from \"../../../../../contract/contract.js\";\nimport { getFunctionId } from \"../../../../../utils/function-id.js\";\nimport { useNFTContext } from \"./provider.js\";\nimport { getNFTInfo } from \"./utils.js\";\n\nexport interface NFTNameProps\n  extends Omit<React.HTMLAttributes<HTMLSpanElement>, \"children\"> {\n  loadingComponent?: JSX.Element;\n  fallbackComponent?: JSX.Element;\n  /**\n   * Optional `useQuery` params\n   */\n  queryOptions?: Omit<UseQueryOptions<string>, \"queryFn\" | \"queryKey\">;\n  /**\n   * This prop can be a string or a (async) function that resolves to a string, representing the name of the NFT\n   * This is particularly useful if you already have a way to fetch the name of the NFT.\n   */\n  nameResolver?: string | (() => string) | (() => Promise<string>);\n}\n\n/**\n * This component fetches and displays an NFT's name. It takes in a `className` and `style` props\n * so you can style it just like how you would style a <span> element.\n * @returns A <span> element containing the name of the NFT\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { NFTProvider, NFTName } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTName />\n * </NFTProvider>\n * ```\n *\n * ### Show a loading sign while the name is being fetched\n * ```tsx\n * import { NFTProvider, NFTName } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTName loadingComponent={<YourLoadingSign />} />\n * </NFTProvider>\n * ```\n *\n * ### Show something in case the name failed to resolve\n * ```tsx\n * import { NFTProvider, NFTName } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTName fallbackComponent={<span>Failed to load name</span>} />\n * </NFTProvider>\n * ```\n *\n * ### Custom query options for useQuery (tanstack-query)\n * ```tsx\n * import { NFTProvider, NFTName } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTName queryOptions={{ retry: 3, enabled: false, }} />\n * </NFTProvider>\n * ```\n *\n * ### Override the name with the `nameResolver` prop\n * If you already have the name, you can skip the network requests and pass it directly to the NFTName\n * ```tsx\n * <NFTName nameResolver=\"Doodles #1\" />\n * ```\n *\n * You can also pass in your own custom (async) function that retrieves the name\n * ```tsx\n * const getName = async () => {\n *   // ...\n *   return name;\n * };\n *\n * <NFTName nameResolver={getName} />\n * ```\n *\n * @nft\n * @component\n * @beta\n */\nexport function NFTName({\n  loadingComponent,\n  fallbackComponent,\n  queryOptions,\n  nameResolver,\n  ...restProps\n}: NFTNameProps) {\n  const { contract, tokenId } = useNFTContext();\n\n  const nameQuery = useQuery({\n    queryKey: getQueryKey({\n      contractAddress: contract.address,\n      chainId: contract.chain.id,\n      tokenId,\n      nameResolver,\n    }),\n    queryFn: async (): Promise<string> =>\n      fetchNftName({ nameResolver, contract, tokenId }),\n    ...queryOptions,\n  });\n\n  if (nameQuery.isLoading) {\n    return loadingComponent || null;\n  }\n\n  if (!nameQuery.data) {\n    return fallbackComponent || null;\n  }\n  return <span {...restProps}>{nameQuery.data}</span>;\n}\n\n/**\n * @internal\n */\nexport function getQueryKey(props: {\n  contractAddress: string;\n  chainId: number;\n  tokenId: bigint;\n  nameResolver?: string | (() => string) | (() => Promise<string>);\n}) {\n  const { chainId, tokenId, nameResolver, contractAddress } = props;\n  return [\n    \"_internal_nft_name_\",\n    chainId,\n    contractAddress,\n    tokenId.toString(),\n    {\n      resolver:\n        typeof nameResolver === \"string\"\n          ? nameResolver\n          : typeof nameResolver === \"function\"\n            ? getFunctionId(nameResolver)\n            : undefined,\n    },\n  ] as const;\n}\n\n/**\n * @internal Exported for tests\n */\nexport async function fetchNftName(props: {\n  nameResolver?: string | (() => string) | (() => Promise<string>);\n  contract: ThirdwebContract;\n  tokenId: bigint;\n}): Promise<string> {\n  const { nameResolver, contract, tokenId } = props;\n  if (typeof nameResolver === \"string\") {\n    return nameResolver;\n  }\n  if (typeof nameResolver === \"function\") {\n    return nameResolver();\n  }\n  const nft = await getNFTInfo({ contract, tokenId }).catch(() => undefined);\n  if (!nft) {\n    throw new Error(\"Failed to resolve NFT info\");\n  }\n  if (typeof nft.metadata.name !== \"string\") {\n    throw new Error(\"Failed to resolve NFT name\");\n  }\n  return nft.metadata.name;\n}\n", "\"use client\";\n\nimport { type UseQueryOptions, useQuery } from \"@tanstack/react-query\";\nimport type { JSX } from \"react\";\nimport type { ThirdwebContract } from \"../../../../../contract/contract.js\";\nimport { getFunctionId } from \"../../../../../utils/function-id.js\";\nimport { useNFTContext } from \"./provider.js\";\nimport { getNFTInfo } from \"./utils.js\";\n\nexport interface NFTDescriptionProps\n  extends Omit<React.HTMLAttributes<HTMLSpanElement>, \"children\"> {\n  loadingComponent?: JSX.Element;\n  fallbackComponent?: JSX.Element;\n  /**\n   * Optional `useQuery` params\n   */\n  queryOptions?: Omit<UseQueryOptions<string>, \"queryFn\" | \"queryKey\">;\n  /**\n   * This prop can be a string or a (async) function that resolves to a string, representing the description of the NFT\n   * This is particularly useful if you already have a way to fetch the data.\n   */\n  descriptionResolver?: string | (() => string) | (() => Promise<string>);\n}\n\n/**\n * This component fetches and displays an NFT's description. It inherits all the attributes of a <span>\n * so you can style it just like how you would style a <span> element.\n * @returns A <span> element containing the description of the NFT\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { NFTProvider, NFTDescription } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTDescription />\n * </NFTProvider>\n * ```\n *\n * ### Show a loading sign while the description is being fetched\n * ```tsx\n * import { NFTProvider, NFTDescription } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTDescription loadingComponent={<YourLoadingSign />} />\n * </NFTProvider>\n * ```\n *\n * ### Show something in case the description failed to resolve\n * ```tsx\n * import { NFTProvider, NFTDescription } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTDescription fallbackComponent={<span>Failed to load description</span>} />\n * </NFTProvider>\n * ```\n *\n * ### Custom query options for useQuery (tanstack-query)\n * ```tsx\n * import { NFTProvider, NFTDescription } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTDescription queryOptions={{ retry: 3, enabled: false, }} />\n * </NFTProvider>\n * ```\n *\n * ### Override the description with the `descriptionResolver` prop\n * If you already have the url, you can skip the network requests and pass it directly to the NFTDescription\n * ```tsx\n * <NFTDescription descriptionResolver=\"The desc of the NFT\" />\n * ```\n *\n * You can also pass in your own custom (async) function that retrieves the description\n * ```tsx\n * const getDescription = async () => {\n *   // ...\n *   return description;\n * };\n *\n * <NFTDescription descriptionResolver={getDescription} />\n * ```\n * @component\n * @nft\n * @beta\n */\nexport function NFTDescription({\n  loadingComponent,\n  fallbackComponent,\n  queryOptions,\n  descriptionResolver,\n  ...restProps\n}: NFTDescriptionProps) {\n  const { contract, tokenId } = useNFTContext();\n  const descQuery = useQuery({\n    queryKey: [\n      \"_internal_nft_description_\",\n      contract.chain.id,\n      contract.address,\n      tokenId.toString(),\n      {\n        resolver:\n          typeof descriptionResolver === \"string\"\n            ? descriptionResolver\n            : typeof descriptionResolver === \"function\"\n              ? getFunctionId(descriptionResolver)\n              : undefined,\n      },\n    ],\n    queryFn: async (): Promise<string> =>\n      fetchNftDescription({ descriptionResolver, contract, tokenId }),\n    ...queryOptions,\n  });\n\n  if (descQuery.isLoading) {\n    return loadingComponent || null;\n  }\n\n  if (!descQuery.data) {\n    return fallbackComponent || null;\n  }\n\n  return <span {...restProps}>{descQuery.data}</span>;\n}\n\n/**\n * @internal Exported for tests\n */\nexport async function fetchNftDescription(props: {\n  descriptionResolver?: string | (() => string) | (() => Promise<string>);\n  contract: ThirdwebContract;\n  tokenId: bigint;\n}): Promise<string> {\n  const { descriptionResolver, contract, tokenId } = props;\n  if (typeof descriptionResolver === \"string\") {\n    return descriptionResolver;\n  }\n  if (typeof descriptionResolver === \"function\") {\n    return descriptionResolver();\n  }\n  const nft = await getNFTInfo({ contract, tokenId }).catch(() => undefined);\n  if (!nft) {\n    throw new Error(\"Failed to resolve NFT info\");\n  }\n  if (typeof nft.metadata.description !== \"string\") {\n    throw new Error(\"Failed to resolve NFT description\");\n  }\n  return nft.metadata.description;\n}\n", "\"use client\";\n\nimport { type UseQueryOptions, useQuery } from \"@tanstack/react-query\";\nimport type { JSX } from \"react\";\nimport type { ThirdwebContract } from \"../../../../../contract/contract.js\";\nimport { getFunctionId } from \"../../../../../utils/function-id.js\";\nimport { MediaRenderer } from \"../../MediaRenderer/MediaRenderer.js\";\nimport type { MediaRendererProps } from \"../../MediaRenderer/types.js\";\nimport { useNFTContext } from \"./provider.js\";\nimport { getNFTInfo } from \"./utils.js\";\n\n/**\n * @component\n * @beta\n * @wallet\n */\nexport type NFTMediaInfo = {\n  src: string;\n  poster: string | undefined;\n};\n\n/**\n * @component\n * @beta\n * @wallet\n * The props for the <NFTMedia /> component\n * It is similar to the [`MediaRendererProps`](https://portal.thirdweb.com/references/typescript/v5/MediaRendererProps)\n * (excluding `src`, `poster` and `client`) that you can\n * use to style the NFTMedia\n */\nexport type NFTMediaProps = Omit<\n  MediaRendererProps,\n  \"src\" | \"poster\" | \"client\"\n> & {\n  loadingComponent?: JSX.Element;\n  fallbackComponent?: JSX.Element;\n  /**\n   * Optional `useQuery` params\n   */\n  queryOptions?: Omit<UseQueryOptions<NFTMediaInfo>, \"queryFn\" | \"queryKey\">;\n  /**\n   * This prop can be a string or a (async) function that resolves to a string, representing the media url of the NFT\n   * This is particularly useful if you already have a way to fetch the image.\n   * In case of function, the function must resolve to an object of type `NFTMediaInfo`\n   */\n  mediaResolver?:\n    | NFTMediaInfo\n    | (() => NFTMediaInfo)\n    | (() => Promise<NFTMediaInfo>);\n};\n\n/**\n * This component fetches and displays an NFT's media. It uses thirdweb [`MediaRenderer`](https://portal.thirdweb.com/refernces/typescript/v5/MediaRenderer) under the hood\n * so you can style it just like how you would style a MediaRenderer.\n * @returns A MediaRenderer component\n *\n * @component\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { NFTProvider, NFTMedia } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTMedia />\n * </NFTProvider>\n * ```\n *\n * ### Show a loading sign while the media is being fetched\n * ```tsx\n * import { NFTProvider, NFTMedia } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTMedia loadingComponent={<YourLoadingSign />} />\n * </NFTProvider>\n * ```\n *\n * ### Show something in case the media failed to resolve\n * ```tsx\n * import { NFTProvider, NFTMedia } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTMedia fallbackComponent={<span>Failed to load media</span>} />\n * </NFTProvider>\n * ```\n *\n * ### Custom query options for useQuery (tanstack-query)\n * ```tsx\n * import { NFTProvider, NFTMedia } from \"thirdweb/react\";\n *\n * <NFTProvider>\n *   <NFTMedia queryOptions={{ retry: 3, enabled: false, }} />\n * </NFTProvider>\n * ```\n *\n * ### Basic stylings\n *\n * You can style NFTMedia with the `style` and `className` props.\n *\n * ```tsx\n * <NFTMedia style={{ borderRadius: \"8px\" }} className=\"mx-auto\" />\n * ```\n *\n * ### Override the media with the `mediaResolver` prop\n * If you already have the url, you can skip the network requests and pass it directly to the NFTMedia\n * ```tsx\n * <NFTMedia mediaResolver={{\n *   src: \"/cat_video.mp4\",\n *   // Poster is applicable to medias that are videos and audios\n *   poster: \"/cat-image.png\",\n * }} />\n * ```\n *\n * You can also pass in your own custom (async) function that retrieves the media url\n * ```tsx\n * const getMedia = async () => {\n *   const url = getNFTMedia(props);\n *   return url;\n * };\n *\n * <NFTMedia mediaResolver={getMedia} />\n * ```\n * @nft\n * @beta\n */\nexport function NFTMedia({\n  loadingComponent,\n  fallbackComponent,\n  queryOptions,\n  mediaResolver,\n  ...mediaRendererProps\n}: NFTMediaProps) {\n  const { contract, tokenId } = useNFTContext();\n  const mediaQuery = useQuery({\n    queryKey: getQueryKey({\n      contractAddress: contract.address,\n      chainId: contract.chain.id,\n      tokenId,\n      mediaResolver,\n    }),\n    queryFn: async (): Promise<NFTMediaInfo> =>\n      fetchNftMedia({ mediaResolver, contract, tokenId }),\n    ...queryOptions,\n  });\n\n  if (mediaQuery.isLoading) {\n    return loadingComponent || null;\n  }\n\n  if (!mediaQuery.data) {\n    return fallbackComponent || null;\n  }\n\n  return (\n    <MediaRenderer\n      client={contract.client}\n      src={mediaQuery.data.src}\n      poster={mediaQuery.data.poster}\n      {...mediaRendererProps}\n    />\n  );\n}\n\n/**\n * @internal\n */\nexport function getQueryKey(props: {\n  contractAddress: string;\n  chainId: number;\n  tokenId: bigint;\n  mediaResolver?:\n    | NFTMediaInfo\n    | (() => NFTMediaInfo)\n    | (() => Promise<NFTMediaInfo>);\n}) {\n  const { chainId, tokenId, mediaResolver, contractAddress } = props;\n  return [\n    \"_internal_nft_media_\",\n    chainId,\n    contractAddress,\n    tokenId.toString(),\n    {\n      resolver:\n        typeof mediaResolver === \"object\"\n          ? mediaResolver\n          : typeof mediaResolver === \"function\"\n            ? getFunctionId(mediaResolver)\n            : undefined,\n    },\n  ] as const;\n}\n\n/**\n * @internal Exported for tests only\n */\nexport async function fetchNftMedia(props: {\n  mediaResolver?:\n    | NFTMediaInfo\n    | (() => NFTMediaInfo)\n    | (() => Promise<NFTMediaInfo>);\n  contract: ThirdwebContract;\n  tokenId: bigint;\n}): Promise<{ src: string; poster: string | undefined }> {\n  const { mediaResolver, contract, tokenId } = props;\n  if (typeof mediaResolver === \"object\") {\n    return mediaResolver;\n  }\n  if (typeof mediaResolver === \"function\") {\n    return mediaResolver();\n  }\n  const nft = await getNFTInfo({ contract, tokenId }).catch(() => undefined);\n  if (!nft) {\n    throw new Error(\"Failed to resolve NFT info\");\n  }\n  const animation_url = nft.metadata.animation_url;\n  const image = nft.metadata.image || nft.metadata.image_url;\n  if (animation_url) {\n    return {\n      src: animation_url,\n      poster: image || undefined,\n    };\n  }\n  if (image) {\n    return {\n      src: image,\n      poster: undefined,\n    };\n  }\n  throw new Error(\"Failed to resolve NFT media\");\n}\n", "\"use client\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport { getLastAuthProvider } from \"../../../react/core/utils/storage.js\";\nimport { webLocalStorage } from \"../../../utils/storage/webStorage.js\";\nimport { isEcosystemWallet } from \"../../../wallets/ecosystem/is-ecosystem-wallet.js\";\nimport { ClientScopedStorage } from \"../../../wallets/in-app/core/authentication/client-scoped-storage.js\";\nimport type { Ecosystem } from \"../../../wallets/in-app/core/wallet/types.js\";\nimport { useActiveWallet } from \"../../core/hooks/wallets/useActiveWallet.js\";\n\n/**\n * Embeds another thirdweb-supported site for seamless in-app and ecosystem wallet connection.\n *\n *  Make sure the embedded site includes <AutoConnect /> and supports frame ancestors, see [here](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/frame-ancestors) for more information.\n *\n *  The embedded site must support the connected wallet (ecosystem or in-app).\n *\n * @param {Object} props - The props to pass to the iframe\n * @param {String} props.src - The URL of the site to embed\n * @param {ThirdwebClient} props.client - The current site's thirdweb client\n * @param {Ecosystem} [props.ecosystem] - The ecosystem to use for the wallet connection in the embedded site\n *\n * @example\n * ```tsx\n * import { SiteEmbed } from \"thirdweb/react\";\n *\n * <SiteEmbed src=\"https://thirdweb.com\" client={thirdwebClient} ecosystem={{ id: \"ecosystem.thirdweb\" }} />\n * ```\n * @walletConnection\n */\nexport function SiteEmbed({\n  src,\n  client,\n  ecosystem,\n  ...props\n}: {\n  src: string;\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n} & React.DetailedHTMLProps<\n  React.IframeHTMLAttributes<HTMLIFrameElement>,\n  HTMLIFrameElement\n>) {\n  if (!client.clientId) {\n    throw new Error(\"The SiteEmbed client must have a clientId\");\n  }\n\n  const activeWallet = useActiveWallet();\n  const walletId = activeWallet?.id;\n\n  const {\n    data: { authProvider, authCookie } = {},\n  } = useQuery({\n    queryKey: [\"site-embed\", walletId, src, client.clientId, ecosystem],\n    enabled:\n      activeWallet &&\n      (isEcosystemWallet(activeWallet) ||\n        walletId === \"inApp\" ||\n        walletId === \"smart\"),\n    queryFn: async () => {\n      const storage = new ClientScopedStorage({\n        storage: webLocalStorage,\n        clientId: client.clientId,\n        ecosystem,\n      });\n\n      const authProvider = await getLastAuthProvider(webLocalStorage);\n      const authCookie = await storage.getAuthCookie();\n\n      return { authProvider, authCookie };\n    },\n  });\n\n  const url = new URL(src);\n  if (walletId) {\n    url.searchParams.set(\"walletId\", walletId === \"smart\" ? \"inApp\" : walletId);\n  }\n  if (authProvider) {\n    url.searchParams.set(\"authProvider\", authProvider);\n  }\n  if (authCookie) {\n    url.searchParams.set(\"authCookie\", authCookie);\n  }\n\n  return (\n    <iframe\n      src={encodeURI(url.toString())}\n      width=\"100%\"\n      height=\"100%\"\n      allowFullScreen\n      {...props}\n    />\n  );\n}\n", "\"use client\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport { getLastAuthProvider } from \"../../../react/core/utils/storage.js\";\nimport { webLocalStorage } from \"../../../utils/storage/webStorage.js\";\nimport { isEcosystemWallet } from \"../../../wallets/ecosystem/is-ecosystem-wallet.js\";\nimport { ClientScopedStorage } from \"../../../wallets/in-app/core/authentication/client-scoped-storage.js\";\nimport type { Ecosystem } from \"../../../wallets/in-app/core/wallet/types.js\";\nimport { useActiveWallet } from \"../../core/hooks/wallets/useActiveWallet.js\";\n\n/**\n * Creates a link to another thirdweb-supported site with wallet connection parameters.\n *\n *  The target site must support the connected wallet (ecosystem or in-app).\n *\n * @param {Object} props - The props to pass to the anchor tag\n * @param {String} props.href - The URL of the site to link to\n * @param {ThirdwebClient} props.client - The current site's thirdweb client\n * @param {Ecosystem} [props.ecosystem] - The ecosystem to use for the wallet connection in the target site\n * @param {React.ReactNode} props.children - The content to render inside the link\n *\n * @example\n * ```tsx\n * import { SiteLink } from \"thirdweb/react\";\n *\n * <SiteLink href=\"https://thirdweb.com\" client={thirdwebClient} ecosystem={{ id: \"ecosystem.thirdweb\" }}>\n *   Visit Site\n * </SiteLink>\n * ```\n * @walletConnection\n */\nexport function SiteLink({\n  href,\n  client,\n  ecosystem,\n  children,\n  ...props\n}: {\n  href: string;\n  client: ThirdwebClient;\n  ecosystem?: Ecosystem;\n  children: React.ReactNode;\n} & Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\">) {\n  if (!client.clientId) {\n    throw new Error(\"The SiteLink client must have a clientId\");\n  }\n\n  const activeWallet = useActiveWallet();\n  const walletId = activeWallet?.id;\n\n  const {\n    data: { authProvider, authCookie } = {},\n  } = useQuery({\n    queryKey: [\"site-link\", walletId, href, client.clientId, ecosystem],\n    enabled:\n      activeWallet &&\n      (isEcosystemWallet(activeWallet) ||\n        walletId === \"inApp\" ||\n        walletId === \"smart\"),\n    queryFn: async () => {\n      const storage = new ClientScopedStorage({\n        storage: webLocalStorage,\n        clientId: client.clientId,\n        ecosystem,\n      });\n\n      const authProvider = await getLastAuthProvider(webLocalStorage);\n      const authCookie = await storage.getAuthCookie();\n\n      return { authProvider, authCookie };\n    },\n  });\n\n  const url = new URL(href);\n  if (walletId) {\n    url.searchParams.set(\"walletId\", walletId === \"smart\" ? \"inApp\" : walletId);\n  }\n  if (authProvider) {\n    url.searchParams.set(\"authProvider\", authProvider);\n  }\n  if (authCookie) {\n    url.searchParams.set(\"authCookie\", authCookie);\n  }\n\n  return (\n    <a href={encodeURI(url.toString())} {...props}>\n      {children}\n    </a>\n  );\n}\n", "\"use client\";\n\nimport type { Address } from \"abitype\";\nimport type React from \"react\";\nimport { createContext, useContext } from \"react\";\nimport type { Chain } from \"../../../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../../../client/client.js\";\n\n/**\n * Props for the <TokenProvider /> component\n * @component\n * @token\n */\nexport type TokenProviderProps = {\n  /**\n   * The token (ERC20) contract address\n   */\n  address: Address;\n  /**\n   * thirdweb Client\n   */\n  client: ThirdwebClient;\n  /**\n   * The chain (network) that the token is on\n   */\n  chain: Chain;\n};\n\nconst TokenProviderContext = /* @__PURE__ */ createContext<\n  TokenProviderProps | undefined\n>(undefined);\n\n/**\n * A React context provider component that supplies Token-related data to its child components.\n *\n * This component serves as a wrapper around the `TokenProviderContext.Provider` and passes\n * the provided token data down to all of its child components through the context API.\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { TokenProvider, TokenIcon, TokenName  } from \"thirdweb/react\";\n * import { ethereum } from \"thirdweb/chains\";\n *\n * <TokenProvider address=\"0x...\" client={...} chain={ethereum}>\n *   <TokenIcon />\n *   <TokenName />\n * </TokenProvider>\n * ```\n *\n * ### This component also works with native token!\n * ```tsx\n * import { NATIVE_TOKEN_ADDRESS} from \"thirdweb\";\n * import { ethereum } from \"thirdweb/chains\";\n *\n * <TokenProvider address={NATIVE_TOKEN_ADDRESS} chain={ethereum} client={client}>\n *   <TokenSymbol /> // \"ETH\"\n * </TokenProvider>\n * ```\n *\n * @component\n * @token\n * @beta\n */\nexport function TokenProvider(\n  props: React.PropsWithChildren<TokenProviderProps>,\n) {\n  return (\n    <TokenProviderContext.Provider value={props}>\n      {props.children}\n    </TokenProviderContext.Provider>\n  );\n}\n\n/**\n * @internal\n */\nexport function useTokenContext() {\n  const ctx = useContext(TokenProviderContext);\n  if (!ctx) {\n    throw new Error(\n      \"TokenProviderContext not found. Make sure you are using TokenName, TokenIcon, TokenSymbol etc. inside a <TokenProvider /> component\",\n    );\n  }\n  return ctx;\n}\n", "\"use client\";\n\nimport { type UseQueryOptions, useQuery } from \"@tanstack/react-query\";\nimport type React from \"react\";\nimport type { JSX } from \"react\";\nimport type { Chain } from \"../../../../../chains/types.js\";\nimport { getChainMetadata } from \"../../../../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../../../../client/client.js\";\nimport { NATIVE_TOKEN_ADDRESS } from \"../../../../../constants/addresses.js\";\nimport { getContract } from \"../../../../../contract/contract.js\";\nimport { getContractMetadata } from \"../../../../../extensions/common/read/getContractMetadata.js\";\nimport { name } from \"../../../../../extensions/common/read/name.js\";\nimport { getFunctionId } from \"../../../../../utils/function-id.js\";\nimport { useTokenContext } from \"./provider.js\";\n\n/**\n * Props for the TokenName component\n * @component\n * @token\n */\nexport interface TokenNameProps\n  extends Omit<React.HTMLAttributes<HTMLSpanElement>, \"children\"> {\n  /**\n   * This prop can be a string or a (async) function that resolves to a string, representing the name of the token\n   * This is particularly useful if you already have a way to fetch the token name.\n   */\n  nameResolver?: string | (() => string) | (() => Promise<string>);\n  /**\n   * A function to format the name's display value\n   * Particularly useful to avoid overflowing-UI issues\n   *\n   * ```tsx\n   * <TokenName formatFn={(str: string) => doSomething()} />\n   * ```\n   */\n  formatFn?: (str: string) => string;\n  /**\n   * This component will be shown while the name of the token is being fetched\n   * If not passed, the component will return `null`.\n   *\n   * You can/should pass a loading sign or spinner to this prop.\n   * @example\n   * ```tsx\n   * <TokenName loadingComponent={<Spinner />} />\n   * ```\n   */\n  loadingComponent?: JSX.Element;\n  /**\n   * This component will be shown if the name fails to be retreived\n   * If not passed, the component will return `null`.\n   *\n   * You can/should pass a descriptive text/component to this prop, indicating that the\n   * name was not fetched successfully\n   * @example\n   * ```tsx\n   * <TokenName fallbackComponent={\"Failed to load\"}\n   * />\n   * ```\n   */\n  fallbackComponent?: JSX.Element;\n  /**\n   * Optional `useQuery` params\n   */\n  queryOptions?: Omit<UseQueryOptions<string>, \"queryFn\" | \"queryKey\">;\n}\n\n/**\n * This component fetches then shows the name of a token. For ERC20 tokens, it calls the `name` function in the ERC20 contract.\n * It inherits all the attributes of a HTML <span> component, hence you can style it just like how you would style a normal <span>\n *\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { TokenProvider, TokenName } from \"thirdweb/react\";\n * import { ethereum } from \"thirdweb/chains\";\n *\n * <TokenProvider {...props}>\n *   <TokenName  />\n * </TokenProvider>\n * ```\n * Result:\n * ```html\n * <span>Ether</span>\n * ```\n *\n * ### Custom name resolver\n * By default TokenName will call the `name` method of the token contract.\n * However if you have a different way to fetch the name, you can pass the function to the `nameResolver` prop.\n * Note: nameResolver should either be a string or a function (async) that returns a string.\n * ```tsx\n * async function fetchNameMethod() {\n *   // your own fetching logic\n *   return \"the token name\";\n * }\n *\n * <TokenName nameResolver={fetchNameMethod} />\n * ```\n *\n * Alternatively you can also pass in a string directly:\n * ```tsx\n * <TokenName nameResolver=\"USD Coin Circle\" />\n * ```\n *\n *\n * ### Format the name (capitalize, truncate, etc.)\n * The TokenName component accepts a `formatFn` which takes in a string and outputs a string\n * The function is used to modify the name of the token\n *\n * ```tsx\n * const concatStr = (str: string):string => str + \"Token\"\n *\n * <TokenName formatFn={concatStr} />\n * ```\n *\n * Result:\n * ```html\n * <span>Ether Token</span>\n * ```\n *\n * ### Show a loading sign when the name is being fetched\n * ```tsx\n * import { TokenProvider, TokenName } from \"thirdweb/react\";\n *\n * <TokenProvider address=\"0x...\">\n *   <TokenName loadingComponent={<Spinner />} />\n * </TokenProvider>\n * ```\n *\n * ### Fallback to something when the name fails to resolve\n * ```tsx\n * <TokenProvider address=\"0x...\">\n *   <TokenName fallbackComponent={\"Failed to load\"} />\n * </TokenProvider>\n * ```\n *\n * ### Custom query options for useQuery\n * This component uses `@tanstack-query`'s useQuery internally.\n * You can use the `queryOptions` prop for more fine-grained control\n * ```tsx\n * <TokenName\n *   queryOptions={{\n *     enabled: isEnabled,\n *     retry: 4,\n *   }}\n * />\n * ```\n *\n * @component\n * @token\n * @beta\n */\nexport function TokenName({\n  nameResolver,\n  formatFn,\n  loadingComponent,\n  fallbackComponent,\n  queryOptions,\n  ...restProps\n}: TokenNameProps) {\n  const { address, client, chain } = useTokenContext();\n  const nameQuery = useQuery({\n    queryKey: getQueryKeys({ chainId: chain.id, nameResolver, address }),\n    queryFn: async () =>\n      fetchTokenName({ address, chain, client, nameResolver }),\n    ...queryOptions,\n  });\n\n  if (nameQuery.isLoading) {\n    return loadingComponent || null;\n  }\n\n  if (!nameQuery.data) {\n    return fallbackComponent || null;\n  }\n\n  if (formatFn && typeof formatFn === \"function\") {\n    return <span {...restProps}>{formatFn(nameQuery.data)}</span>;\n  }\n\n  return <span {...restProps}>{nameQuery.data}</span>;\n}\n\n/**\n * @internal Exported for tests only\n */\nexport async function fetchTokenName(props: {\n  address: string;\n  client: ThirdwebClient;\n  chain: Chain;\n  nameResolver?: string | (() => string) | (() => Promise<string>);\n}) {\n  const { nameResolver, address, client, chain } = props;\n  if (typeof nameResolver === \"string\") {\n    return nameResolver;\n  }\n  if (typeof nameResolver === \"function\") {\n    return nameResolver();\n  }\n  if (address.toLowerCase() === NATIVE_TOKEN_ADDRESS.toLowerCase()) {\n    // Don't wanna use `getChainName` because it has some side effect (it catches error and defaults to \"ETH\")\n    return getChainMetadata(chain).then((data) => data.nativeCurrency.name);\n  }\n\n  // Try to fetch the name from both the `name` function and the contract metadata\n  // then prioritize its result\n  const contract = getContract({ address, client, chain });\n  const [_name, contractMetadata] = await Promise.all([\n    name({ contract }).catch(() => undefined),\n    getContractMetadata({ contract }).catch(() => undefined),\n  ]);\n  if (typeof _name === \"string\") {\n    return _name;\n  }\n  if (typeof contractMetadata?.name === \"string\") {\n    return contractMetadata.name;\n  }\n  throw new Error(\n    \"Failed to resolve name from both name() and contract metadata\",\n  );\n}\n\n/**\n * @internal\n */\nexport function getQueryKeys(props: {\n  chainId: number;\n  address: string;\n  nameResolver?: string | (() => string) | (() => Promise<string>);\n}) {\n  const { chainId, address, nameResolver } = props;\n  return [\n    \"_internal_token_name_\",\n    chainId,\n    address,\n    {\n      resolver:\n        typeof nameResolver === \"string\"\n          ? nameResolver\n          : typeof nameResolver === \"function\"\n            ? getFunctionId(nameResolver)\n            : undefined,\n    },\n  ] as const;\n}\n", "\"use client\";\n\nimport { type UseQueryOptions, useQuery } from \"@tanstack/react-query\";\nimport type React from \"react\";\nimport type { JSX } from \"react\";\nimport type { Chain } from \"../../../../../chains/types.js\";\nimport { getChainMetadata } from \"../../../../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../../../../client/client.js\";\nimport { NATIVE_TOKEN_ADDRESS } from \"../../../../../constants/addresses.js\";\nimport { getContract } from \"../../../../../contract/contract.js\";\nimport { getContractMetadata } from \"../../../../../extensions/common/read/getContractMetadata.js\";\nimport { symbol } from \"../../../../../extensions/common/read/symbol.js\";\nimport { getFunctionId } from \"../../../../../utils/function-id.js\";\nimport { useTokenContext } from \"./provider.js\";\n\n/**\n * Props for the TokenSymbol component\n * @component\n * @token\n */\nexport interface TokenSymbolProps\n  extends Omit<React.HTMLAttributes<HTMLSpanElement>, \"children\"> {\n  /**\n   * This prop can be a string or a (async) function that resolves to a string, representing the symbol of the token\n   * This is particularly useful if you already have a way to fetch the token symbol.\n   */\n  symbolResolver?: string | (() => string) | (() => Promise<string>);\n  /**\n   * A function to format the symbol's value\n   * Particularly useful to avoid overflowing-UI issues\n   *\n   * ```tsx\n   * <TokenSymbol formatFn={(str: string) => doSomething()} />\n   * ```\n   */\n  formatFn?: (str: string) => string;\n  /**\n   * This component will be shown while the symbol of the token is being fetched\n   * If not passed, the component will return `null`.\n   *\n   * You can/should pass a loading sign or spinner to this prop.\n   * @example\n   * ```tsx\n   * <TokenSymbol loadingComponent={<Spinner />} />\n   * ```\n   */\n  loadingComponent?: JSX.Element;\n  /**\n   * This component will be shown if the symbol fails to be retreived\n   * If not passed, the component will return `null`.\n   *\n   * You can/should pass a descriptive text/component to this prop, indicating that the\n   * symbol was not fetched successfully\n   * @example\n   * ```tsx\n   * <TokenSymbol fallbackComponent={\"Failed to load\"}\n   * />\n   * ```\n   */\n  fallbackComponent?: JSX.Element;\n  /**\n   * Optional `useQuery` params\n   */\n  queryOptions?: Omit<UseQueryOptions<string>, \"queryFn\" | \"queryKey\">;\n}\n\n/**\n * This component fetches then shows the symbol of a token. For ERC20 tokens, it calls the `symbol` function in the ERC20 contract.\n * It inherits all the attributes of a HTML <span> component, hence you can style it just like how you would style a normal <span>\n *\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { TokenProvider, TokenSymbol } from \"thirdweb/react\";\n * import { ethereum } from \"thirdweb/chains\";\n *\n * <TokenProvider {...props}>\n *   <TokenSymbol  />\n * </TokenProvider>\n * ```\n * Result:\n * ```html\n * <span>ETH</span>\n * ```\n *\n * ### Custom symbol resolver\n * By default, TokenSymbol calls the `symbol` function of your contract,\n * however, if your token as an unconventional way to fetch the symbol, you can pass the custom logic to the `symbolResolver` prop.\n * It can either be a string or a function (async) that returns or resolves to a string.\n * ```tsx\n * async function getSymbol() {\n *   // your own fetching logic\n *   return \"the symbol\";\n * }\n *\n * <TokenSymbol symbolResolver={getSymbol} />\n * ```\n * Alternatively, you can pass in a string directly:\n * ```tsx\n * <TokenSymbol symbolResolver=\"USDC.e\" />\n * ```\n *\n * ### Format the symbol (capitalize, truncate, etc.)\n * The TokenSymbol component accepts a `formatFn` which takes in a string and outputs a string\n * The function is used to modify the symbol of the token\n *\n * ```tsx\n * const concatStr = (str: string):string => str + \"Token\"\n *\n * <TokenSymbol formatFn={concatStr} />\n * ```\n *\n * Result:\n * ```html\n * <span>Ether Token</span>\n * ```\n *\n * ### Show a loading sign when the symbol is being fetched\n * ```tsx\n * import { TokenProvider, TokenSymbol } from \"thirdweb/react\";\n *\n * <TokenProvider address=\"0x...\">\n *   <TokenSymbol loadingComponent={<Spinner />} />\n * </TokenProvider>\n * ```\n *\n * ### Fallback to something when the symbol fails to resolve\n * ```tsx\n * <TokenProvider address=\"0x...\">\n *   <TokenSymbol fallbackComponent={\"Failed to load\"} />\n * </TokenProvider>\n * ```\n *\n * ### Custom query options for useQuery\n * This component uses `@tanstack-query`'s useQuery internally.\n * You can use the `queryOptions` prop for more fine-grained control\n * ```tsx\n * <TokenSymbol queryOptions={{\n *     enabled: isEnabled,\n *     retry: 4,\n *   }}\n * />\n * ```\n *\n * @component\n * @token\n * @beta\n */\nexport function TokenSymbol({\n  symbolResolver,\n  formatFn,\n  loadingComponent,\n  fallbackComponent,\n  queryOptions,\n  ...restProps\n}: TokenSymbolProps) {\n  const { address, client, chain } = useTokenContext();\n  const symbolQuery = useQuery({\n    queryKey: getQueryKeys({ chainId: chain.id, address, symbolResolver }),\n    queryFn: async () =>\n      fetchTokenSymbol({ symbolResolver, address, chain, client }),\n    ...queryOptions,\n  });\n\n  if (symbolQuery.isLoading) {\n    return loadingComponent || null;\n  }\n\n  if (!symbolQuery.data) {\n    return fallbackComponent || null;\n  }\n\n  if (formatFn && typeof formatFn === \"function\") {\n    return <span {...restProps}>{formatFn(symbolQuery.data)}</span>;\n  }\n\n  return <span {...restProps}>{symbolQuery.data}</span>;\n}\n\n/**\n * @internal Exported for tests only\n */\nexport async function fetchTokenSymbol(props: {\n  address: string;\n  client: ThirdwebClient;\n  chain: Chain;\n  symbolResolver?: string | (() => string) | (() => Promise<string>);\n}): Promise<string> {\n  const { symbolResolver, address, client, chain } = props;\n  if (typeof symbolResolver === \"string\") {\n    return symbolResolver;\n  }\n  if (typeof symbolResolver === \"function\") {\n    return symbolResolver();\n  }\n  if (address.toLowerCase() === NATIVE_TOKEN_ADDRESS.toLowerCase()) {\n    // Don't wanna use `getChainSymbol` because it has some side effect (it catches error and defaults to \"ETH\")\n    return getChainMetadata(chain).then((data) => data.nativeCurrency.symbol);\n  }\n\n  // Try to fetch the symbol from both the `symbol` function and the contract metadata\n  // then prioritize its result\n  const contract = getContract({ address, client, chain });\n  const [_symbol, contractMetadata] = await Promise.all([\n    symbol({ contract }).catch(() => undefined),\n    getContractMetadata({ contract }).catch(() => undefined),\n  ]);\n  if (typeof _symbol === \"string\") {\n    return _symbol;\n  }\n  if (typeof contractMetadata?.symbol === \"string\") {\n    return contractMetadata.symbol;\n  }\n  throw new Error(\n    \"Failed to resolve symbol from both symbol() and contract metadata\",\n  );\n}\n\n/**\n * @internal\n */\nexport function getQueryKeys(props: {\n  chainId: number;\n  address: string;\n  symbolResolver?: string | (() => string) | (() => Promise<string>);\n}) {\n  const { chainId, address, symbolResolver } = props;\n  return [\n    \"_internal_token_symbol_\",\n    chainId,\n    address,\n    {\n      resolver:\n        typeof symbolResolver === \"string\"\n          ? symbolResolver\n          : typeof symbolResolver === \"function\"\n            ? getFunctionId(symbolResolver)\n            : undefined,\n    },\n  ] as const;\n}\n", "\"use client\";\n\nimport { type UseQueryOptions, useQuery } from \"@tanstack/react-query\";\nimport type { JSX } from \"react\";\nimport { getChainMetadata } from \"../../../../../chains/utils.js\";\nimport { NATIVE_TOKEN_ADDRESS } from \"../../../../../constants/addresses.js\";\nimport { getContract } from \"../../../../../contract/contract.js\";\nimport { getContractMetadata } from \"../../../../../extensions/common/read/getContractMetadata.js\";\nimport { getFunctionId } from \"../../../../../utils/function-id.js\";\nimport { resolveScheme } from \"../../../../../utils/ipfs.js\";\nimport { useTokenContext } from \"./provider.js\";\n\n/**\n * Props for the TokenIcon component\n * @component\n * @token\n */\nexport interface TokenIconProps\n  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, \"src\"> {\n  /**\n   * This prop can be a string or a (async) function that resolves to a string, representing the icon url of the token\n   * This is particularly useful if you already have a way to fetch the token icon.\n   */\n  iconResolver?: string | (() => string) | (() => Promise<string>);\n  /**\n   * This component will be shown while the avatar of the icon is being fetched\n   * If not passed, the component will return `null`.\n   *\n   * You can pass a loading sign or spinner to this prop.\n   * @example\n   * ```tsx\n   * <TokenIcon loadingComponent={<Spinner />} />\n   * ```\n   */\n  loadingComponent?: JSX.Element;\n  /**\n   * This component will be shown if the request for fetching the avatar is done\n   * but could not retreive any result.\n   * You can pass a dummy avatar/image to this prop.\n   *\n   * If not passed, the component will return `null`\n   *\n   * @example\n   * ```tsx\n   * <TokenIcon fallbackComponent={<DummyImage />} />\n   * ```\n   */\n  fallbackComponent?: JSX.Element;\n\n  /**\n   * Optional query options for `useQuery`\n   */\n  queryOptions?: Omit<UseQueryOptions<string>, \"queryFn\" | \"queryKey\">;\n}\n\n/**\n * This component tries to resolve the icon of a given token, then return an image.\n * @returns an <img /> with the src of the token icon\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { TokenProvider, TokenIcon } from \"thirdweb/react\";\n *\n * <TokenProvider address=\"0x-token-address\" chain={chain} client={client}>\n *   <TokenIcon />\n * </TokenProvider>\n * ```\n *\n * Result: An <img /> component with the src of the icon\n * ```html\n * <img src=\"token-icon.png\" />\n * ```\n *\n * ### Override the icon with the `iconResolver` prop\n * If you already have the icon url, you can skip the network requests and pass it directly to the TokenIcon\n * ```tsx\n * <TokenIcon iconResolver=\"/usdc.png\" />\n * ```\n *\n * You can also pass in your own custom (async) function that retrieves the icon url\n * ```tsx\n * const getIcon = async () => {\n *   const icon = getIconFromCoinMarketCap(tokenAddress, etc);\n *   return icon;\n * };\n *\n * <TokenIcon iconResolver={getIcon} />\n * ```\n *\n * ### Show a loading sign while the icon is being loaded\n * ```tsx\n * <TokenIcon loadingComponent={<Spinner />} />\n * ```\n *\n * ### Fallback to a dummy image if the token icon fails to resolve\n * ```tsx\n * <TokenIcon fallbackComponent={<img src=\"blank-image.png\" />} />\n * ```\n *\n * ### Usage with queryOptions\n * TokenIcon uses useQuery() from tanstack query internally.\n * It allows you to pass a custom queryOptions of your choice for more control of the internal fetching logic\n * ```tsx\n * <TokenIcon queryOptions={{ enabled: someLogic, retry: 3, }} />\n * ```\n *\n * @component\n * @token\n * @beta\n */\nexport function TokenIcon({\n  iconResolver,\n  loadingComponent,\n  fallbackComponent,\n  queryOptions,\n  ...restProps\n}: TokenIconProps) {\n  const { address, client, chain } = useTokenContext();\n  const iconQuery = useQuery({\n    queryKey: [\n      \"_internal_token_icon_\",\n      chain.id,\n      address,\n      {\n        resolver:\n          typeof iconResolver === \"string\"\n            ? iconResolver\n            : typeof iconResolver === \"function\"\n              ? getFunctionId(iconResolver)\n              : undefined,\n      },\n    ] as const,\n    queryFn: async () => {\n      if (typeof iconResolver === \"string\") {\n        return iconResolver;\n      }\n      if (typeof iconResolver === \"function\") {\n        return iconResolver();\n      }\n      if (address.toLowerCase() === NATIVE_TOKEN_ADDRESS.toLowerCase()) {\n        const possibleUrl = await getChainMetadata(chain).then(\n          (data) => data.icon?.url,\n        );\n        if (!possibleUrl) {\n          throw new Error(\"Failed to resolve icon for native token\");\n        }\n        return resolveScheme({ uri: possibleUrl, client });\n      }\n\n      // Try to get the icon from the contractURI\n      const contractMetadata = await getContractMetadata({\n        contract: getContract({\n          address,\n          chain,\n          client,\n        }),\n      });\n\n      if (\n        !contractMetadata.image ||\n        typeof contractMetadata.image !== \"string\"\n      ) {\n        throw new Error(\"Failed to resolve token icon from contract metadata\");\n      }\n\n      return resolveScheme({\n        uri: contractMetadata.image,\n        client,\n      });\n    },\n    ...queryOptions,\n  });\n\n  if (iconQuery.isLoading) {\n    return loadingComponent || null;\n  }\n\n  if (!iconQuery.data) {\n    return fallbackComponent || null;\n  }\n\n  return <img src={iconQuery.data} {...restProps} alt={restProps.alt} />;\n}\n", "import { webLocalStorage } from \"../../../utils/storage/webStorage.js\";\nimport { getLastAuthProvider as getLastAuthProviderCore } from \"../../core/utils/storage.js\";\n\n/**\n * Retrieves the last authentication provider used from local storage.\n *\n * This function is designed to work only in a browser environment.\n *\n * @returns {Promise<AuthArgsType[\"strategy\"] | null>} A promise that resolves to the last\n * authentication provider strategy used, or `null` if none is found.\n *\n * @example\n * ```typescript\n * import { getLastAuthProvider } from \"thirdweb/react\";\n *\n * const lastAuthProvider = await getLastAuthProvider();\n * ```\n *\n * @utils\n */\nexport async function getLastAuthProvider() {\n  return getLastAuthProviderCore(webLocalStorage);\n}\n", "\"use client\";\n\nimport type { UseQueryOptions } from \"@tanstack/react-query\";\nimport type { JSX } from \"react\";\nimport type { AuthOption } from \"../../../../../wallets/types.js\";\nimport {\n  getSocialIcon,\n  useWalletIcon,\n} from \"../../../../core/utils/walletIcon.js\";\n\nexport interface WalletIconProps\n  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, \"src\"> {\n  /**\n   * This component will be shown while the icon of the wallet is being fetched\n   * If not passed, the component will return `null`.\n   *\n   * You can/should pass a loading sign or spinner to this prop.\n   * @example\n   * ```tsx\n   * <WalletIcon loadingComponent={<Spinner />} />\n   * ```\n   */\n  loadingComponent?: JSX.Element;\n  /**\n   * This component will be shown if the icon fails to be retrieved\n   * If not passed, the component will return `null`.\n   *\n   * You can/should pass a descriptive text/component to this prop, indicating that the\n   * icon was not fetched successfully\n   * @example\n   * ```tsx\n   * <WalletIcon fallbackComponent={<span>Failed to load</span>}\n   * />\n   * ```\n   */\n  fallbackComponent?: JSX.Element;\n  /**\n   * Optional `useQuery` params\n   */\n  queryOptions?: Omit<UseQueryOptions<string>, \"queryFn\" | \"queryKey\">;\n}\n\n/**\n * This component tries to resolve the icon of a given wallet, then return an image.\n * @returns an <img /> with the src of the wallet icon\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { WalletProvider, WalletIcon } from \"thirdweb/react\";\n *\n * <WalletProvider id=\"io.metamask\">\n *   <WalletIcon />\n * </WalletProvider>\n * ```\n *\n * Result: An <img /> component with the src of the icon\n * ```html\n * <img src=\"metamask-icon.png\" />\n * ```\n *\n * ### Show a loading sign while the icon is being loaded\n * ```tsx\n * <WalletIcon loadingComponent={<Spinner />} />\n * ```\n *\n * ### Fallback to a dummy image if the wallet icon fails to resolve\n * ```tsx\n * <WalletIcon fallbackComponent={<img src=\"blank-image.png\" />} />\n * ```\n *\n * ### Usage with queryOptions\n * WalletIcon uses useQuery() from tanstack query internally.\n * It allows you to pass a custom queryOptions of your choice for more control of the internal fetching logic\n * ```tsx\n * <WalletIcon queryOptions={{ enabled: someLogic, retry: 3, }} />\n * ```\n *\n * @component\n * @wallet\n * @beta\n */\nexport function WalletIcon({\n  loadingComponent,\n  fallbackComponent,\n  queryOptions,\n  ...restProps\n}: WalletIconProps) {\n  const imageQuery = useWalletIcon({ queryOptions });\n  if (imageQuery.isLoading) {\n    return loadingComponent || null;\n  }\n  if (!imageQuery.data) {\n    return fallbackComponent || null;\n  }\n  return <img src={imageQuery.data} {...restProps} alt={restProps.alt} />;\n}\n\nexport interface SocialIconProps\n  extends Omit<React.ImgHTMLAttributes<HTMLImageElement>, \"src\"> {\n  provider: AuthOption | string;\n}\n\n/**\n * Social auth provider icon\n * @returns an <img /> component with the src set to the svg\n *\n * @example\n * ```tsx\n * import { SocialIcon } from \"thirdweb/react\";\n *\n * <SocialIcon provider=\"google\" />\n * ```\n *\n * Result: An <img /> component with the src of the icon\n * ```html\n * <img src=\"google-icon-svg\" />\n * ```\n *\n * @component\n * @wallet\n * @beta\n */\nexport function SocialIcon({ provider, ...restProps }: SocialIconProps) {\n  const src = getSocialIcon(provider);\n  return <img src={src} {...restProps} alt={restProps.alt} />;\n}\n", "import { useQuery } from \"@tanstack/react-query\";\nimport type { UseQueryOptions } from \"@tanstack/react-query\";\nimport { getFunctionId } from \"../../../utils/function-id.js\";\nimport { getWalletInfo } from \"../../../wallets/__generated__/getWalletInfo.js\";\nimport type { WalletId } from \"../../../wallets/wallet-types.js\";\nimport { useWalletContext } from \"../wallet/provider.js\";\n\n/**\n * @internal\n */\nexport function useWalletName(props: {\n  formatFn?: (str: string) => string;\n  queryOptions?: Omit<UseQueryOptions<string>, \"queryFn\" | \"queryKey\">;\n}) {\n  const { id } = useWalletContext();\n  const nameQuery = useQuery({\n    queryKey: getQueryKeys({ id, formatFn: props.formatFn }),\n    queryFn: async () => fetchWalletName({ id, formatFn: props.formatFn }),\n    ...props.queryOptions,\n  });\n  return nameQuery;\n}\n\n/**\n * @internal Exported for tests only\n */\nexport function getQueryKeys(props: {\n  id: WalletId;\n  formatFn?: (str: string) => string;\n}) {\n  if (typeof props.formatFn === \"function\") {\n    return [\n      \"walletName\",\n      props.id,\n      { resolver: getFunctionId(props.formatFn) },\n    ] as const;\n  }\n  return [\"walletName\", props.id] as const;\n}\n\n/**\n * @internal Exported for tests only\n */\nexport async function fetchWalletName(props: {\n  id: WalletId;\n  formatFn?: (str: string) => string;\n}) {\n  const info = await getWalletInfo(props.id);\n  if (typeof props.formatFn === \"function\") {\n    return props.formatFn(info.name);\n  }\n  return info.name;\n}\n", "\"use client\";\n\nimport type { UseQueryOptions } from \"@tanstack/react-query\";\nimport type { JSX } from \"react\";\nimport { useWalletName } from \"../../../../core/utils/walletname.js\";\n\n/**\n * Props for the WalletName component\n * @component\n * @wallet\n */\nexport interface WalletNameProps\n  extends Omit<React.HTMLAttributes<HTMLSpanElement>, \"children\"> {\n  /**\n   * This component will be shown while the name of the wallet name is being fetched\n   * If not passed, the component will return `null`.\n   *\n   * You can/should pass a loading sign or spinner to this prop.\n   * @example\n   * ```tsx\n   * <WalletName loadingComponent={<Spinner />} />\n   * ```\n   */\n  loadingComponent?: JSX.Element;\n  /**\n   * This component will be shown if the name fails to be retreived\n   * If not passed, the component will return `null`.\n   *\n   * You can/should pass a descriptive text/component to this prop, indicating that the\n   * name was not fetched successfully\n   * @example\n   * ```tsx\n   * <WalletName fallbackComponent={<span>Failed to load</span>}\n   * />\n   * ```\n   */\n  fallbackComponent?: JSX.Element;\n  /**\n   * Optional `useQuery` params\n   */\n  queryOptions?: Omit<UseQueryOptions<string>, \"queryFn\" | \"queryKey\">;\n  /**\n   * A function to format the name's display value\n   * ```tsx\n   * <WalletName formatFn={(str: string) => doSomething()} />\n   * ```\n   */\n  formatFn?: (str: string) => string;\n}\n\n/**\n * This component fetches then shows the name of a wallet.\n * It inherits all the attributes of a HTML <span> component, hence you can style it just like how you would style a normal <span>\n *\n * @example\n * ### Basic usage\n * ```tsx\n * import { WalletProvider, WalletName } from \"thirdweb/react\";\n *\n * <WalletProvider id=\"io.metamask\">\n *   <WalletName  />\n * </WalletProvider>\n * ```\n * Result:\n * ```html\n * <span>MetaMask</span>\n * ```\n *\n * ### Show a loading sign when the name is being fetched\n * ```tsx\n * import { WalletProvider, WalletName } from \"thirdweb/react\";\n *\n * <WalletProvider {...props}>\n *   <WalletName loadingComponent={<Spinner />} />\n * </WalletProvider>\n * ```\n *\n * ### Fallback to something when the name fails to resolve\n * ```tsx\n * <WalletProvider {...props}>\n *   <WalletName fallbackComponent={<span>Failed to load</span>} />\n * </WalletProvider>\n * ```\n *\n * ### Custom query options for useQuery\n * This component uses `@tanstack-query`'s useQuery internally.\n * You can use the `queryOptions` prop for more fine-grained control\n * ```tsx\n * <WalletName\n *   queryOptions={{\n *     enabled: isEnabled,\n *     retry: 4,\n *   }}\n * />\n * @component\n * @beta\n * @wallet\n */\nexport function WalletName({\n  loadingComponent,\n  fallbackComponent,\n  queryOptions,\n  formatFn,\n  ...restProps\n}: WalletNameProps) {\n  const nameQuery = useWalletName({ queryOptions, formatFn });\n  if (nameQuery.isLoading) {\n    return loadingComponent || null;\n  }\n  if (!nameQuery.data) {\n    return fallbackComponent || null;\n  }\n  return <span {...restProps}>{nameQuery.data}</span>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,gBAAmC;;;ACCnC,mBAAqC;AAgB/B,SAAU,sBAAmB;AACjC,QAAM,UAAU,wBAAwB,qBAAqB;AAC7D,QAAM,QAAQ,QAAQ;AACtB,aAAO,mCAAqB,MAAM,WAAW,MAAM,UAAU,MAAM,QAAQ;AAC7E;;;ADgKM,SAAU,aAAa,OAAwB;AAtLrD;AAuLE,QAAM,eAAe,gBAAe;AACpC,QAAM,gBAAgB,iBAAgB;AACtC,QAAM,WAAW,YAAY,cAAc,eAAe,MAAM,IAAI;AACpE,QAAM,OACJ,CAAC,iBAAkB,SAAS,gBAAgB,CAAC,SAAS;AACxD,QAAM,oBAAoB,qBAAoB;AAG9C,+BAAU,MAAK;AACb,QAAI,MAAM,OAAO;AACf,wBAAkB,aAAa,CAAC,MAAM,KAAK,CAAC;IAC9C;EACF,GAAG,CAAC,MAAM,OAAO,iBAAiB,CAAC;AAEnC,+BAAU,MAAK;AACb,QAAI,MAAM,QAAQ;AAChB,wBAAkB,aAAa,MAAM,MAAM;IAC7C;EACF,GAAG,CAAC,MAAM,QAAQ,iBAAiB,CAAC;AAEpC,QAAM,cAAU,uBACd,MACE,MAAM,WACN,kBAAkB;IAChB,aAAa,MAAM;IACnB,QAAQ,MAAM;GACf,GACH,CAAC,MAAM,SAAS,MAAM,aAAa,MAAM,MAAM,CAAC;AAElD,QAAM,WAAW,MAAM,UAAU;AACjC,QAAM,cAAc,iBAAiB,QAAQ;AAE7C,4BAA0B;IACxB;GACD;AAED,QAAM,gBAAY,uBAAQ,MAAK;AAC7B,WAAO,CAAC,gBAAe,KAAM,QAAQ,WAAW,IAC5C,YACA,MAAM,aAAc;EAC1B,GAAG,CAAC,QAAQ,QAAQ,MAAM,SAAS,CAAC;AAEpC,QAAM,WAAO,uBAAQ,MAAK;AACxB,WAAO;MACL,kBAAkB,MAAM;MACxB,sBAAsB,MAAM,yBAAyB;MACrD,mBAAmB,MAAM;MACzB,OAAO;MACP,cAAc;MACd,iBAAiB,MAAM;;EAE3B,GAAG;IACD,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;GACP;AAED,QAAM,mBACJ,WAAM,uBAAN,mBAA0B,UAAS,MAAM,WAAS,WAAM,WAAN,mBAAe;AAEnE,QAAM,kBAAkB,MAAM,gBAAgB,aAC5C,mBAAAC,KAAC,aAAW,EACV,OAAO,gBACP,aAAa,MAAM,aACnB,QAAQ,MAAM,QACd,UACA,SACA,oBAAoB,MAAM,oBAC1B,SACE,OAAO,MAAM,gBAAgB,YACzB,UACA,WAAM,gBAAN,mBAAmB,SAEzB,WAAW,MAAM,UAAS,CAAA;AAI9B,MAAI,MAAM;AACR,QAAI,CAAC,YAAY,MAAM;AACrB,iBACE,mBAAAC,MAAA,mBAAAC,UAAA,EAAA,UAAA,CACG,qBACD,mBAAAF,KAAC,qBAAmB,EAAC,OAAO,MAAM,OAAK,cACrC,mBAAAA,KAAC,gBAAc,EAAC,WAAoB,cAClC,mBAAAA,KAAC,eAAa,CAAA,CAAA,EAAG,CAAA,EACF,CAAA,CACG,EAAA,CAAA;IAG5B;AAEA,eACE,mBAAAC,MAAC,wBAAsB,EAAC,OAAO,MAAM,OAAO,QAAQ,MAAI,UAAA,KACtD,mBAAAD,KAAC,qBAAmB,EAClB,MAAM,MAAM,MACZ,oBAAoB,MAAM,oBAC1B,OAAO,gBACP,QAAQ,MAAM,QACd,QAAQ,MAAM,QACd,eAAe,YAAY,MAC3B,MAAM,WACN,MACA,QAAQ,MAAM,QACd,UAAU,MAAM,UAAU,SAC1B,WAAW,MAAM,WACjB,oBAAoB,MAAM,oBAC1B,gBAAgB,MAAM,gBACtB,eAAe,MAAM,eACrB,SACA,WAAW,MAAM,WACjB,WACA,OAAO,MAAM,OACb,eAAe,MAAM,cAAa,CAAA,GAEnC,eAAe,EAAA,CAAA;EAGtB;AAEA,aAAO,mBAAAA,KAAA,OAAA,EAAA,UAAM,gBAAe,CAAA;AAC9B;AAKA,IAAM,sBAAsB,CAAC,UAqCxB;AAEH,QAAM,cAAc,eAAe;IACjC,MAAM,MAAM;IACZ,eAAe;IACf,SAAS,MAAM;GAChB;AACD,QAAM,EAAE,WAAW,eAAe,OAAM,IAAK;AAC7C,QAAM,eAAe,gBAAe;AACpC,QAAM,gBAAgB,iBAAgB;AACtC,QAAM,WAAW,YAAY,cAAc,eAAe,MAAM,IAAI;AAEpE,QAAM,mBAAmB,oBAAmB;AAE5C,MAAI,UAAU;AAGd,+BAAU,MAAK;AACb,QACE,SAAS,gBACT,CAAC,SAAS,cACV,iBACA,WAAW,eACX;AACA,gBAAU,gBAAgB,MAAM;IAClC;EACF,GAAG,CAAC,UAAU,WAAW,eAAe,QAAQ,aAAa,CAAC;AAE9D,QAAM,YAAY,CAAC,gBAAe,IAC9B,YACA,MAAM,aAAc;AAGxB,MAAI,kBAAkB;AACpB,kBAAU,mBAAAA,KAAC,eAAa,CAAA,CAAA;EAC1B,OAAO;AACL,kBACE,mBAAAA,KAAC,qBAAmB,EAClB,iBAAiB,MACjB,aACA,QAAQ,MACR,SAAS,MAAK;AACZ,gBAAU,aAAa;IACzB,GACA,oBAAoB,MAAK;IAEzB,GACA,oBAAoB,MAAM,oBAC1B,MAAM,MAAM,MACZ,OAAO,MAAM,OACb,QAAQ,MAAM,QACd,QAAQ,MAAM,QACd,eAAe,MAAM,eACrB,MAAM;MACJ,GAAG,MAAM;MACT,OACE,OAAO,MAAM,WAAW,WAAW,MAAM,OAAO,QAAQ;MAC1D,cACE,OAAO,MAAM,WAAW,WACpB,MAAM,OAAO,YACb;OAER,MAAM,MAAM,MACZ,eAAe,MAAM,eACrB,YAAY,CAAC,MAAM,QACnB,WAAW,MAAM,WACjB,oBAAoB,MAAM,oBAC1B,gBAAgB,MAAM,gBACtB,eAAe,MAAM,eACrB,SAAS,MAAM,SACf,aAAa,QACb,iBAAiB,OAAS,CAAA;EAGhC;AAEA,aACE,mBAAAA,KAAC,gBAAc,EACb,WACA,WAAW,MAAM,WACjB,OAAO,MAAM,OAAK,UAEjB,cAAc,SACb,cAEA,mBAAAC,MAAC,eAAa,EAAA,UAAA,CAAA,KAAG,SAAO,GAAA,EAAA,CAAA,EACzB,CAAA;AAGP;AAEO,IAAM,iBAAiC,UAE3C,CAAC,UAAS;AACX,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,QAAQ,eAAc;AAC5B,SAAO;IACL,OAAO,MAAM,OAAO;IACpB,YAAY,MAAM,OAAO;IACzB,QAAQ,cAAc,YAAY,SAAS;IAC3C,OAAO,cAAc,YAAY,uBAAuB;IACxD,WAAW;IACX,UAAU;IACV,YAAY;IACZ,cAAc,OAAO;IACrB,QAAQ,aAAa,MAAM,OAAO,WAAW;IAC7C,UAAU;IACV,YAAY,MAAM;IAClB,kBAAkB;MAChB,iBAAiB,MAAM,OAAO;MAC9B,OAAO,MAAM,OAAO;;IAEtB,OAAO;MACL,WAAW;;;AAGjB,CAAC;;;;;;AE7VM,IAAM,+BAA+B,CAC1C,OACA,sBACE;AACF,QAAM,EACJ,aACA,mBACA,wBACA,SACA,QAAO,IACL;AACJ,SAAO,YAAY;IACjB,YAAY,YAAW;AACrB,UAAI,SAAS;AACX,gBAAO;MACT;AACA,UAAI;AACF,cAAM,aAAa,MAAM,YAAW;AACpC,cAAM,SAAS,MAAM,kBAAkB,UAAU;AAEjD,YAAI,mBAAmB;AACrB,4BAAkB,MAAM;QAC1B;AAEA,YAAI,wBAAwB;AAC1B,gBAAM,UAAU,MAAM,eAAe,MAAM;AAC3C,cAAI,QAAQ,WAAW,YAAY;AACjC,kBAAM,IAAI,MACR,uBAAuB,UAAU,SAAS,MAAM,CAAC,CAAC,EAAE;UAExD;AACA,iCAAuB,OAAO;QAChC;MACF,SAAS,OAAO;AACd,YAAI,SAAS;AACX,kBAAQ,KAAc;QACxB;MACF;MACA;IACF;GACD;AACH;;;;AC1JA,IAAAE,gBAA2B;;;AC8HrB,SAAU,uBAAuB,MAKtC;AACC,QAAM,EAAE,cAAc,SAAS,QAAQ,YAAW,IAAK;AACvD,MAAI,WAAW,iCAAQ;AAEvB,SAAO,YAAY;IACjB,YAAY,OAAO,OAAM;AAxI7B;AA0IM,UAAI,UAAU,GAAG,MAAM,SAAO,YAAO,SAAQ,MAAf,mBAAmB,KAAI;AACnD,cAAM,YAAY,GAAG,KAAK;AAE1B,mBAAW,OAAO,WAAU;MAC9B;AAEA,YAAM,UAAU;AAEhB,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mBAAmB;MACrC;AAEA,UAAI,CAAC,cAAc;AACjB,sBAAc;UACZ,QAAQ,GAAG;UACX,eAAe,QAAQ;UACvB,YAAY,iCAAQ;UACpB,SAAS,GAAG,MAAM;UAClB,OAAO;SACR;AACD,eAAO,gBAAgB;UACrB,aAAa;UACb;UACA;SACD;MACH;AAEA,aAAO,IAAI,QAA+B,CAAC,SAAS,WAAU;AAC5D,cAAM,SAAS,YAAW;AACxB,cAAI;AACF,kBAAM,MAAM,MAAM,gBAAgB;cAChC,aAAa;cACb;cACA;aACD;AAED,oBAAQ,GAAG;UACb,SAAS,GAAG;AACV,mBAAO,CAAC;UACV;QACF;AAEA,SAAC,YAAW;AACV,cAAI;AACF,kBAAM,CAAC,cAAc,WAAW,IAAI,MAAM,QAAQ,IAAI;cACpD,qBAAqB,GAAG,KAAK;cAC7B,qBAAqB,GAAG,UAAU;aACnC;AAED,kBAAM,cAAc,gBAAgB;AACpC,kBAAM,cAAa,2CAAa,cAAa;AAE7C,kBAAM,CAAC,eAAe,cAAc,OAAO,IAAI,MAAM,QAAQ,IAAI;cAC/D,iBAAiB;gBACf,QAAQ,GAAG;gBACX,SAAS,QAAQ;gBACjB,OAAO,GAAG;eACX;eACD,2CAAa,gBACT,gBAAgB;gBACd,QAAQ,GAAG;gBACX;gBACA,OAAO,GAAG;gBACV,cAAc,YAAY;eAC3B,IACD;cACJ,sBAAsB,IAAI,QAAQ,OAAO;aAC1C;AAED,kBAAM,eAAe,gCAAgC,MAAM;AAC3D,kBAAM,YAAY,eAAe,KAAK;AACtC,kBAAM,aAAa,cAAc;AAEjC,kBAAM,kBACH,aAAa,MACZ,gBACA,aAAa,QAAQ,cACtB,aAAa,MAAM,cAAc,QAAQ;AAE5C,gBAAI,iBAAiB;AACnB,oBAAM,wBAAwB,MAAa,OAAO;gBAChD,QAAQ,GAAG;gBACX,oBAAoB,GAAG,MAAM;gBAC7B,yBAAyB,2CAAa;eACvC,EAAE,MAAM,CAAC,QAAO;AACf,8BAAc;kBACZ,QAAQ,GAAG;kBACX,eAAe,QAAQ;kBACvB,YAAY,iCAAQ;kBACpB,WAAW,GAAG,MAAM;kBACpB,OAAO;kBACP,OAAO,2BAAK;iBACb;AACD,uBAAO;cACT,CAAC;AAED,kBACE,CAAC,yBACD,sBAAsB,WAAW,GACjC;AAEA,8BAAc;kBACZ,QAAQ,GAAG;kBACX,eAAe,QAAQ;kBACvB,YAAY,iCAAQ;kBACpB,WAAW,GAAG,MAAM;kBACpB,UAAS,2CAAa,iBAAgB;kBACtC,OAAO;kBACP,OAAO,KAAK,UAAU;oBACpB,OAAO,GAAG,MAAM;oBAChB,OAAO,2CAAa;oBACpB,SAAS;mBACV;iBACF;AAED,6BAAa;kBACX,MAAM;kBACN;kBACA;kBACA,UAAU;kBACV,WAAW;iBACZ;AACD;cACF;AAGA,2BAAa;gBACX,MAAM;gBACN;gBACA;gBACA,UAAU;gBACV,WAAW;eACZ;YACH,OAAO;AACL,4BAAc;gBACZ,QAAQ,GAAG;gBACX,eAAe,QAAQ;gBACvB,YAAY,iCAAQ;gBACpB,WAAW,GAAG,MAAM;gBACpB,UAAS,2CAAa,iBAAgB;gBACtC,OAAO;eACR;AACD,qBAAM;YACR;UACF,SAAS,GAAG;AACV,oBAAQ,MAAM,2BAA2B,CAAC;AAE1C,mBAAM;UACR;QACF,GAAE;MACJ,CAAC;IACH;GACD;AACH;;;;AClSA,IAAAC,gBAAyB;;;;ACmCzB,IAAM,iBAAiB;;;;;;;;;;AAWvB,IAAM,eAA+B,UAAU,MAAK;AAClD,QAAM,QAAQ,eAAc;AAC5B,SAAO;IACL,SAAS;IACT,YAAY;IACZ,KAAK,QAAQ;IACb,iBAAiB,MAAM,OAAO;IAC9B,QAAQ,aAAa,MAAM,OAAO,WAAW;IAC7C,SAAS,GAAG,QAAQ,EAAE,IAAI,QAAQ,EAAE;IACpC,cAAc,OAAO;IACrB,OAAO,MAAM,OAAO;IACpB,UAAU,SAAS;IACnB,YAAY;IACZ,UAAU;IACV,aAAa;MACX,SAAS;MACT,OAAO;MACP,QAAQ;MACR,cAAc;MACd,iBAAiB,MAAM,OAAO;MAC9B,WAAW,GAAG,cAAc;;;AAGlC,CAAC;AAMK,SAAU,cAAc,OAM7B;AACC,QAAM,eAAe,gBAAe;AACpC,QAAM,gBAAgB,iBAAgB;AACtC,QAAM,UAAU,+CAAe;AAC/B,QAAM,EAAE,WAAW,OAAM,IAAK,aAAa,WAAW,EAAE;AACxD,QAAM,EAAE,eAAe,OAAM,IAAK;AAClC,QAAM,SAAS,cAAc;AAC7B,QAAM,YAAY,MAAM,GAAG,MAAM,YAAY;AAC7C,QAAM,EACJ,MAAM,wBACN,OAAO,6BACP,YAAY,gCACZ,SAAS,8BAA6B,IACpC,0BAA0B;IAC5B,aAAa,MAAM;IACnB,SAAS;IACT,uBAAuB,CAAA;IACvB,mBAAmB;GACpB;AACD,QAAM,QAAQ,eAAc;AAC5B,QAAM,+BACJ,gCAAgC,YAAY;AAE9C,MAAI,6BAA6B;AAC/B,eACE,oBAAAC,KAAC,WAAS,EACR,OAAO;MACL,WAAW;OAEb,YAAU,MACV,MAAK,OACL,QAAO,QAAM,cAEb,oBAAAA,KAAC,YAAU,EACT,QAAO,2EAA6B,YAAW,wBAC/C,YAAY,8BAA6B,CAAA,EACzC,CAAA;EAGR;AAEA,MAAI,CAAC,wBAAwB;AAC3B,eAAO,oBAAAA,KAAC,eAAa,CAAA,CAAA;EACvB;AAEA,QAAM,YACJ,cAAc,uBAAuB,KAAK,KAAK,CAAC,+BAC5C,uBAAuB,sBACvB,uBAAuB,aACvB,uBAAuB;AAC7B,QAAM,oBACJ,uBAAuB,cAAc,QAAQ;AAC/C,QAAM,gBAAgB,uBAAuB,cAAc,QACvD,YAAY,uBAAuB,cAAc,QACjD;AAEJ,QAAM,iBAAiB,MAAK;AAC1B,WAAO,KACL,wBAAwB,MAAM,GAAG,MAAM,EAAE,wBAAwB;EAErE;AAEA,aACE,oBAAAC,MAAC,WAAS,EAAC,GAAE,MAAI,UAAA,KACf,oBAAAD,KAAC,aAAW,EAAC,OAAO,sBAAsB,QAAQ,MAAM,OAAM,CAAA,OAE9D,oBAAAA,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,OAEd,oBAAAC,MAAC,WAAS,EAAC,MAAK,UAAS,KAAI,MAAI,UAAA,CAC9B,yBACC,oBAAAD,KAAA,OAAA,EAAA,cACE,oBAAAC,MAAC,MAAI,EAAC,MAAK,MAAK,QAAM,MAAC,OAAM,UAAS,WAAS,MAAA,UAAA,CAAA,YACpC,KACR,aACC,OAAO,WACL,SAAS,eAAe,uBAAuB,QAAQ,CAAC,GAE1D,CAAC,GACA,KACF,uBAAuB,MAAM,QAAM,cAAA,EAAA,CAAA,EAC/B,CAAA,OAGX,oBAAAA,MAAC,WAAS,EACR,MAAK,OACL,OAAO;IACL,gBAAgB;IAChB,SAAS,QAAQ;IACjB,cAAc,QAAQ;IACtB,cAAc,QAAQ;IACtB,iBAAiB,MAAM,OAAO;IAC9B,QAAQ,aAAa,MAAM,OAAO,WAAW;KAC9C,UAAA,CAEA,qBACC,oBAAAD,KAAC,WAAS,EACR,SAAS,+CAAe,SACxB,UAAS,MACT,OAAc,CAAA,GAGjB,uBAAuB,cAAc,UAAU,UAChD,CAAC,qCACC,oBAAAC,MAAC,WAAS,EAAC,MAAK,OAAM,KAAI,OAAM,QAAO,KAAG,UAAA,KACxC,oBAAAD,KAAC,MAAI,EAAC,MAAK,MAAK,OAAM,iBAAgB,QAAQ,KAAG,UAC9C,mBACC,uBAAuB,eACvB,KAAK,EACN,CAAA,OAEH,oBAAAA,KAAC,aAAW,EACV,OAAO,uBAAuB,OAC9B,OAAO,MAAM,GAAG,OAChB,MAAK,MACL,OAAM,gBAAe,CAAA,CACrB,EAAA,CAAA,QAGJ,oBAAAA,KAAC,WAAS,EAAC,MAAK,OAAM,KAAI,OAAM,QAAO,KAAG,cACxC,oBAAAA,KAAC,UAAQ,EAAC,OAAM,QAAO,QAAQ,SAAS,GAAE,CAAA,EAAI,CAAA,CAEjD,EAAA,CAAA,CACS,EAAA,CAAA,OAGd,oBAAAA,KAAC,wBAAsB,EAAC,SAAS,QAAM,cACrC,oBAAAC,MAAC,WAAS,EAAC,MAAK,UAAS,KAAI,MAAK,QAAO,QAAO,QAAM,MAAA,UAAA,KACpD,oBAAAD,KAAC,WAAS,EAAC,MAAK,OAAM,QAAO,KAAG,cAC9B,oBAAAA,KAAC,QAAM,EACL,WAAW,SACX,MAAM,KACN,QACE,oBACE,oBAAAA,KAAC,aAAW,EACV,IAAI,aAAa,IACjB,MAAM,SAAS,IACf,OAAc,CAAA,EAEjB,CAAA,EAEH,CAAA,OAEJ,oBAAAC,MAAC,WAAS,EAAC,MAAK,OAAM,QAAO,KAAI,KAAI,MAAI,UAAA,KACvC,oBAAAD,KAAC,MAAI,EAAC,OAAM,eAAc,MAAK,MAAI,UAChC,UAAU,eAAe,OAAO,IAAI,GAAE,CAAA,OAEzC,oBAAAA,KAAC,UAAQ,EACP,MAAM,WAAW,IACjB,KAAI,gBACJ,UAAoB,CAAA,CACpB,EAAA,CAAA,CACQ,EAAA,CAAA,EACF,CAAA,OAGd,oBAAAA,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,OAEd,oBAAAA,KAAC,MAAI,EACH,WAAS,MACT,QAAM,MACN,SAAO,MACP,MAAK,MACL,WAAU,mCAAiC,UAE1C,OAAO,YAAW,CAAA,OAGrB,oBAAAA,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,GAEb,wBACC,oBAAAC,MAAC,cAAY,EAAA,UAAA,CAAA,yBACW,uBAAuB,cAAc,MAAI,KAAA,EAAA,CAAA,QAGjE,oBAAAD,KAAC,QAAM,EAAC,SAAQ,UAAS,SAAS,MAAM,QAAQ,WAAS,MAAA,UAAA,WAAA,CAAA,GAI1D,qBAAqB,iBACpB,oBAAAC,MAAA,oBAAAC,UAAA,EAAA,UAAA,KACE,oBAAAF,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,OACd,oBAAAA,KAAC,QAAM,EAAC,SAAQ,QAAO,SAAS,gBAAgB,WAAS,MAAA,cACvD,oBAAAC,MAAC,WAAS,EAAC,MAAK,OAAM,QAAO,KAAI,KAAI,MAAK,OAAM,cAAY,UAAA,KAC1D,oBAAAD,KAAC,WAAS,EAAC,MAAM,SAAS,GAAE,CAAA,OAC5B,oBAAAA,KAAC,MAAI,EAAC,MAAK,MAAK,OAAM,cAAa,QAAQ,KAAK,QAAM,MAAA,UAAA,oBAAA,CAAA,CAE/C,EAAA,CAAA,EACG,CAAA,CACL,EAAA,CAAA,CAEZ,EAAA,CAAA;AAGP;AAEA,IAAM,yBAAyC,aAAa,CAAC,MAAK;AAChE,QAAM,QAAQ,eAAc;AAC5B,SAAO;IACL,KAAK;IACL,OAAO;IACP,WAAW;IACX,QAAQ;IACR,SAAS,QAAQ;IACjB,SAAS;IACT,gBAAgB;IAChB,QAAQ,aAAa,MAAM,OAAO,WAAW;IAC7C,cAAc,OAAO;IACrB,YAAY;IACZ,WAAW;MACT,aAAa,MAAM,OAAO;;;AAGhC,CAAC;;;;ACrSD,IAAAG,gBAAyD;AAenD,SAAU,kBAAkB,OAKjC;;AACC,QAAM,aAAa,mBAAmB;IACpC,UAAU;GACX;AACD,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAAQ;AACpC,QAAM,CAAC,SAAS,UAAU,QAAI,wBAAQ;AACtC,QAAM,iBAAiB,kBAAkB,MAAM,GAAG,KAAK;AACvD,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAC1B,SAAS;AAGX,QAAM,aAAS,2BAAY,YAAW;AACpC,cAAU,SAAS;AACnB,eAAW,MAAS;AACpB,QAAI;AACF,YAAM,SAAS,MAAM,WAAW,YAAY,MAAM,EAAE;AACpD,gBAAU,OAAO,eAAe;AAChC,YAAM,SAAS,MAAM;AACrB,gBAAU,MAAM;IAClB,SAAS,GAAG;AAGV,cAAQ,MAAM,CAAC;AACf,iBAAW,CAAU;AACrB,gBAAU,QAAQ;IACpB;EACF,GAAG,CAAC,YAAY,MAAM,IAAI,MAAM,QAAQ,CAAC;AAEzC,QAAM,WAAO,sBAAO,KAAK;AACzB,+BAAU,MAAK;AACb,QAAI,KAAK,SAAS;AAChB;IACF;AAEA,SAAK,UAAU;AACf,WAAM;EACR,GAAG,CAAC,MAAM,CAAC;AAEX,aACE,oBAAAC,MAAC,WAAS,EAAC,GAAE,MAAI,UAAA,KACf,oBAAAC,KAAC,aAAW,EAAC,OAAM,eAAc,QAAQ,MAAM,OAAM,CAAA,OAErD,oBAAAA,KAAC,QAAM,EAAC,GAAE,MAAK,CAAA,OAEf,oBAAAD,MAAC,WAAS,EAAC,MAAK,OAAM,QAAO,KAAG,UAAA,CAC7B,WAAW,iBAAa,oBAAAC,KAAC,SAAO,EAAC,MAAK,OAAM,OAAM,aAAY,CAAA,GAC9D,WAAW,gBAAY,oBAAAA,KAAC,gBAAc,EAAC,MAAM,SAAS,KAAK,EAAC,CAAA,GAC5D,WAAW,cACV,oBAAAA,KAAC,WAAS,EAAC,OAAM,WAAU,MAAK,OAAM,QAAO,QAAM,cACjD,oBAAAA,KAAC,kBAAgB,EACf,OAAO,SAAS,KAAK,GACrB,QAAQ,SAAS,KAAK,EAAC,CAAA,EACvB,CAAA,CAEL,EAAA,CAAA,OAEH,oBAAAA,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,OAEd,oBAAAD,MAAC,MAAI,EAAC,OAAM,eAAc,QAAM,MAAC,MAAK,MAAI,UAAA,CACvC,WAAW,aAAa,uBACxB,WAAW,YAAY,sBACvB,WAAW,UAAU,kBAAkB,EAAA,CAAA,OAE1C,oBAAAC,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,OACd,oBAAAA,KAAC,MAAI,EAAC,OAAM,UAAS,QAAM,MAAC,MAAK,MAAI,UAClC,WAAW,YAAY,UAAU,QAAQ,WAAW,KAAK,GAAE,CAAA,OAG9D,oBAAAA,KAAC,QAAM,EAAC,GAAE,MAAK,CAAA,GAEd,WAAW,gBACV,oBAAAA,KAAC,QAAM,EAAC,SAAQ,UAAS,WAAS,MAAC,SAAS,QAAM,UAAA,YAAA,CAAA,GAKnD,WAAW,cACV,oBAAAD,MAAA,oBAAAE,UAAA,EAAA,UAAA,KACE,oBAAAD,KAAC,QAAM,EAAC,SAAQ,UAAS,WAAS,MAAC,SAAS,MAAM,YAAU,UAAA,OAAA,CAAA,GAG3D,cACC,oBAAAD,MAAA,oBAAAE,UAAA,EAAA,UAAA,KACE,oBAAAD,KAAC,QAAM,EAAC,GAAE,KAAI,CAAA,OACd,oBAAAD,MAAC,YAAU,EACT,WAAS,MACT,SAAQ,WACR,MAAM,sBACJ,oBAAe,UAAU,CAAC,MAA1B,mBAA6B,QAAO,IACpC,MAAM,GAER,QAAO,UACP,IAAG,KACH,KAAI,MACJ,OAAO;IACL,gBAAgB;IAChB,OAAO;KACR,UAAA,CAAA,wBAGD,oBAAAC,KAAC,kBAAgB,EAAC,OAAO,SAAS,IAAI,QAAQ,SAAS,GAAE,CAAA,CAAI,EAAA,CAAA,CAClD,EAAA,CAAA,CAEhB,EAAA,CAAA,CAEJ,EAAA,CAAA;AAGP;;;AF7FM,SAAU,iBAAiB,OAAiB;AAChD,QAAM,UAAU,iBAAgB;AAChC,QAAM,SAAS,gBAAe;AAE9B,WAAS;IACP,UAAU,CAAC,2BAA2B,MAAM,IAAI;IAChD,SAAS,YAAW;;AAClB,UAAI,CAAC,WAAW,CAAC,QAAQ;AACvB,cAAM,IAAI,MAAK;MACjB;AACA,oBAAc;QACZ,QAAQ,MAAM;QACd,eAAe,QAAQ;QACvB,YAAY,OAAO;QACnB,WAAW,MAAM,GAAG,MAAM;QAC1B,SAAS,MAAM,GAAG,cACb,WAAM,qBAAqB,MAAM,GAAG,UAAU,MAA9C,mBAAkD,eACnD;QACJ,OACE,MAAM,cAAc,QAChB,+BACA;OACP;AAED,aAAO;IACT;IACA,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC;GACxB;AAED,aACE,oBAAAE,KAAC,qBAAmB,EAAC,OAAO,MAAM,OAAK,cACrC,oBAAAA,KAAC,OAAK,EACJ,MAAM,MACN,MAAK,WACL,SAAS,CAAC,UAAS;AACjB,QAAI,CAAC,OAAO;AACV,YAAM,QAAO;IACf;EACF,GAAC,cAED,oBAAAA,KAAC,yBAAuB,EAAA,GAAK,MAAK,CAAA,EAAI,CAAA,EAChC,CAAA;AAGd;AAEA,SAAS,wBAAwB,OAA2C;AAC1E,QAAM,cAAc,iBAAiB,MAAM,QAAQ;AACnD,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAA+B,KAAK;AAEhE,MAAI,CAAC,YAAY,MAAM;AACrB,eAAO,oBAAAA,KAAC,eAAa,CAAA,CAAA;EACvB;AAEA,MAAI,WAAW,cAAc;AAC3B,eACE,oBAAAA,KAAC,mBAAiB,EAChB,IAAI,MAAM,IACV,YAAY,MAAM,SAClB,UAAU,MAAM,SAAQ,CAAA;EAG9B;AAEA,MAAI,MAAM,cAAc,WAAW;AACjC,eACE,oBAAAA,KAAC,eAAa,EACZ,QAAQ,MAAM,QACd,QAAQ,MAAM,QACd,IAAI,MAAM,IACV,eAAe,YAAY,MAC3B,QAAQ,MAAK;AACX,gBAAU,YAAY;IACxB,EAAC,CAAA;EAGP;AAEA,aACE,oBAAAA,KAAC,eAAa,EACZ,OAAO,MAAM,OACb,SAAS,OACT,QAAQ,MAAM,QACd,QAAQ,MAAM,QACd,iBAAiB,MAAM,iBACvB,eAAe,YAAY,MAC3B,OAAO,OAAO,MAAM,UAAU,WAAW,MAAM,QAAQ,MAAM,MAAM,MACnE,YAAY,MAAM,YAClB,QAAQ,MAAK;AACX,cAAU,YAAY;EACxB,GACA,gBAAgB,OAAS,CAAA;AAG/B;;;AFtCM,SAAU,mBAAmB,SAAgC,CAAA,GAAE;AACnE,QAAM,cAAc,2BAA0B;AAC9C,QAAM,SAAS,gBAAe;AAC9B,QAAM,gBAAY,0BAAW,qBAAqB;AAClD,QAAM,WAAW,OAAO;AAExB,MAAI,kBAAkB;AAEtB,MAAI,aAAa,SAAS,OAAO,SAAS;AACxC,sBAAkB;EACpB;AAEA,QAAM,eAAe,CAAC,SAAuB;;AAC3C,QAAI,aAAa;AAAO;AACxB,kBACE,oBAAAC,KAAC,kBAAgB,EACf,SAAO,0CAAU,aAAV,mBAAoB,SAAQ,eACnC,MAAM,eAAc,GACpB,IAAI,KAAK,IACT,YAAY,KAAK,QACjB,SAAS,MAAK;AACZ,gBAAU,IAAI;AACd,WAAK,SACH,IAAI,MAAM,4CAA4C,CAAC;IAE3D,GACA,UAAU,KAAK,WACf,QAAQ,KAAK,GAAG,QAChB,WAAU,qCAAU,WAAU,SAC9B,iBAAiB,qCAAU,iBAC3B,QAAO,qCAAU,UAAS,QAC1B,WAAW,KAAK,MAChB,YAAY;MACV,eAAe,qCAAU;MACzB,aAAa,qCAAU;MACvB,cAAc,qCAAU;MACxB,MAAM;MACN,aAAa,KAAK;MAClB,UAAU,qCAAU;MACpB,mBAAmB,qCAAU;MAC7B,sBAAsB,qCAAU;MACjC,CAAA,CACD;EAEN;AAEA,SAAO,uBAAuB;IAC5B,cACE,CAAC,mBAAmB,aAAa,QAAQ,SAAY;IACvD,SAAS,OAAO;IAChB;IACA;GACD;AACH;;;AKZM,SAAU,kBAAkB,OAA6B;AAC7D,QAAM,EACJ,UACA,aACA,mBACA,wBACA,SACA,SACA,SACA,UACA,UACA,UACA,GAAG,YAAW,IACZ;AACJ,QAAM,UAAU,iBAAgB;AAChC,QAAMC,mBAAkB,mBAAmB,EAAE,SAAS,SAAQ,CAAE;AAChE,QAAM,EAAE,QAAQ,aAAa,UAAS,IAAK,6BACzC,OACAA,iBAAgB,WAAW;AAG7B,aACE,oBAAAC,KAAC,qBAAmB,EAAC,OAAO,MAAM,OAAK,cACrC,oBAAAC,MAAC,QAAM,EACL,KAAI,MACJ,UAAU,CAAC,WAAW,YAAY,WAClC,SAAQ,WACR,UAAkB,mBACD,WACjB,SAAS,MAAM,YAAW,GAAE,GACxB,aACJ,OACE,CAAC,WACG;IACE,SAAS,CAAC,WAAW,WAAW,MAAM;IACtC,UAAU;IACV,UAAU;IACV,GAAG,YAAY;MAEjB;IACE,UAAU;IACV,GAAG,YAAY;KAChB,UAAA,KAGP,oBAAAD,KAAA,QAAA,EAAM,OAAO,EAAE,YAAY,YAAY,WAAW,UAAS,GAAE,SAClD,CAAA,GAEV,iBACC,oBAAAA,KAAA,OAAA,EACE,OAAO;IACL,UAAU;IACV,SAAS;IACT,YAAY;IACZ,QAAQ;IACR,KAAK;IACL,QAAQ;IACR,QAAQ;KACT,cAED,oBAAAA,KAAC,SAAO,EAAC,MAAK,MAAK,OAAM,oBAAmB,CAAA,EAAG,CAAA,CAElD,EAAA,CAAA,EACM,CAAA;AAGf;;;;ACtMA,IAAAE,gBAAwB;;;;ACCxB,IAAAC,gBAAyB;;;ACEzB,SAAS,UAAU,GAAQ,GAAM;AAC/B,MAAI,MAAM;AAAG,WAAO;AAEpB,MAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,QAAI,EAAE,gBAAgB,EAAE;AAAa,aAAO;AAE5C,QAAI;AACJ,QAAI;AAEJ,QAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACxC,eAAS,EAAE;AACX,UAAI,WAAW,EAAE;AAAQ,eAAO;AAChC,WAAK,IAAI,QAAQ,QAAQ;AAAK,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,iBAAO;AACjE,aAAO;IACT;AAEA,QAAI,EAAE,YAAY,OAAO,UAAU;AACjC,aAAO,EAAE,QAAO,MAAO,EAAE,QAAO;AAClC,QAAI,EAAE,aAAa,OAAO,UAAU;AAClC,aAAO,EAAE,SAAQ,MAAO,EAAE,SAAQ;AAEpC,UAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE;AAAQ,aAAO;AAE7C,SAAK,IAAI,QAAQ,QAAQ;AAEvB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAE;AAAG,eAAO;AAEjE,SAAK,IAAI,QAAQ,QAAQ,KAAK;AAC5B,YAAM,MAAM,KAAK,CAAC;AAElB,UAAI,OAAO,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAG,eAAO;IAChD;AAEA,WAAO;EACT;AAIA,SAAO,MAAM,KAAK,MAAM;AAC1B;AAEM,SAAU,kBAAqB,SAAwB,SAAU;AACrE,MAAI,UAAU,SAAS,OAAO,GAAG;AAC/B,WAAO;EACT;AACA,SAAO,iBAAiB,SAAS,OAAO;AAC1C;;;ADpCM,SAAU,qBAAqB,OAGpC;AACC,QAAM,CAAC,IAAI,KAAK,QAAI,wBAA0B,IAAI;AAClD,QAAM,CAAC,WAAW,QAAI,wBACpB,MACE,IAAI,YAAY;IACd,gBAAgB;MACd,WAAW;QACT,WAAW,CAAC,MAAM,QAAQ,cAAa;AACrC,cAAI,yBAAyB,SAAS,GAAG;AACvC,gBACE,iBAAiB,MAAM,CAAC,iBAAiB,CAAC,KAC1C,iBAAiB,WAAW,CAAC,UAAU,OAAO,CAAC,GAC/C;AACA,6BAAe;gBACb,iBAAiB,KAAK;;gBACtB,QAAQ,UAAU;gBAClB,OAAO,UAAU;eAClB,EACE,MAAM,CAAC,MAAK;AAEX,wBAAQ,MAAM,uBAAuB,CAAC;cACxC,CAAC,EACA,KAAK,MAAK;AAzC/B;AA0CsB,uBAAO,QAAQ,IAAI;kBACjB,YAAY,kBAAkB;oBAC5B;;sBAEE;wBACE;0BACA,eAAU,eAAV,mBAAsB,MAAM,OAC1B,UAAU,MAAM;0BAClB,eAAU,eAAV,mBAAsB,YAAW,UAAU;;;mBAEhD;kBACD,wBACE,eACA,eAAU,eAAV,mBAAsB,MAAM,OAAM,UAAU,MAAM,EAAE;iBAEvD;cACH,CAAC;YACL;UACF;QACF;;MAEF,SAAS;;;QAGP,WAAW,KAAK;QAChB;;;GAGL,CAAC;AAGN,aACE,oBAAAC,KAAC,qBAAqB,UAAQ,EAAC,OAAO,MAAM,SAAO,cACjD,oBAAAC,MAAC,qBAAmB,EAAC,QAAQ,aAAW,UAAA,KACtC,oBAAAD,KAAC,sBAAsB,UAAQ,EAAC,OAAO,OAAK,UACzC,MAAM,SAAQ,CAAA,GAEhB,EAAE,EAAA,CAAA,EACiB,CAAA;AAG5B;;;ADxDM,SAAU,iBACd,OAEE;AAEF,QAAM,wBAAoB,uBACxB,MAAM,MAAM,qBAAqB,wBAAwB,eAAe,GACxE,CAAC,MAAM,iBAAiB,CAAC;AAG3B,aACE,oBAAAE,KAAC,sBAAoB,EAAC,SAAS,mBAAiB,UAC7C,MAAM,SAAQ,CAAA;AAGrB;;;AGmCM,SAAU,iBAAc;AAC5B,QAAM,UAAU,oBAAmB;AACnC,QAAM,cAAc,eAAc;AAClC,SAAO,YAAY;IACjB,aAAa,CAAC,UAAU;IACxB,YAAY,OAAO,YAAyB;AAlFhD;AAmFM,YAAM,kBAAkB,QAAQ,KAAK,CAAC,MAAM,kBAAkB,CAAC,CAAC;AAChE,YAAM,YAAmC,kBACrC;QACE,IAAI,gBAAgB;QACpB,YAAW,qBAAgB,UAAS,MAAzB,mBAA6B;UAE1C;AACJ,YAAM,uBAAuB,EAAE,GAAG,SAAS,UAAS;AACpD,aAAO,YAAY,oBAAoB;IACzC;IACA,YAAS;AACP,iBAAW,MAAK;AACd,oBAAY,kBAAkB,EAAE,UAAU,CAAC,UAAU,EAAC,CAAE;MAC1D,GAAG,GAAG;IACR;GACD;AACH;;;ACxDA,eAAsB,gBAAsD,EAC1E,QACA,QAAO,GACoB;AAC3B,QAAM,UAAU,OAAO,WAAU;AACjC,MAAI,CAAC,SAAS;AACZ,WAAO;MACL,SAAS,4DAA4D,OAAO,EAAE;;EAElF;AAEA,MAAI,OAAO,OAAO,SAAS;AACzB,UAAM,EAAE,2BAA0B,IAAK,MAAM,OAC3C,yCAA2C;AAE7C,WAAO,2BAA2B,EAAE,OAAM,CAAE;EAC9C;AAEA,MAAI,cAAc,MAAM,GAAG;AACzB,UAAM,EAAE,2BAA0B,IAAK,MAAM,OAC3C,0CAAsD;AAExD,WAAO,2BAA2B,EAAE,OAAM,CAAE;EAC9C;AAGA,MAAI,gBAAgB,MAAM,GAAG;AAC3B,WAAO;MACL,SAAS;;EAEb;AAEA,MAAI;AACJ,MAAI,oBAAoB,MAAM,GAAG;AAC/B,UAAM,EAAE,uBAAsB,IAAK,MAAM,OACvC,4BAA6B;AAE/B,UAAM,SAAS,OAAO,UAAS;AAC/B,eAAY,MAAM,uBAAuB,MAAM;EACjD,OAAO;AACL,eAAW,oBAAoB,OAAO,EAAE;EAC1C;AAEA,MAAI;AACF,UAAM,SAAS,MAAM,SAAS,QAAQ;MACpC,QAAQ;MACR,QAAQ,CAAC,WAAW,QAAQ,OAAO,CAAC;KACrC;AACD,UAAM,eAAe,CAAA;AAIrB,eAAW,CAACC,UAAS,aAAa,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC7D,mBAAa,OAAOA,QAAO,CAAC,IAAI,CAAA;AAChC,YAAM,mBAAmB,CAAA;AACzB,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,aAAa,GAAG;AACxD,yBAAiB,GAAG,IAAI;MAC1B;AACA,mBAAa,OAAOA,QAAO,CAAC,IAAI;IAClC;AACA,WACE,OAAO,YAAY,WAAW,aAAa,OAAO,IAAI;EAE1D,SAAS,OAAgB;AACvB,QAAI,uCAAuC,KAAM,MAAgB,OAAO,GAAG;AACzE,aAAO;QACL,SAAS,GAAG,OAAO,EAAE;;IAEzB;AACA,UAAM;EACR;AACF;;;AC5FM,SAAU,gBAAgB,SAM/B;AACC,QAAM,SAAS,gBAAe;AAC9B,SAAO,SAAS;IACd,UAAU,CAAC,mBAAmB,iCAAQ,IAAI,mCAAS,OAAO;IAC1D,SAAS,YAAW;AAClB,UAAI,CAAC,QAAQ;AACX,eAAO;UACL,SAAS;;MAEb;AACA,aAAO,gBAAgB;QACrB;QACA,SAAS,mCAAS;OACnB;IACH;IACA,OAAO;IACP,GAAG,mCAAS;GACb;AACH;;;ACkFA,eAAsB,UACpB,SAA6B;AAE7B,QAAM,EACJ,QACA,OACA,cACA,UAAU,SACV,QAAQ,OAAO,SAAQ,EAAE,IACvB;AAEJ,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MACR,wDAAwD,OAAO,EAAE,EAAE;EAEvE;AAEA,QAAM,UAAU,OAAO,WAAU;AACjC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MACR,uDAAuD,OAAO,EAAE,EAAE;EAEtE;AAEA,QAAM,YAAY,QAAQ,MAAM,CAAC;AACjC,MAAI,CAAC,WAAW;AACd,UAAM,IAAI,MAAM,kBAAkB;EACpC;AACA,QAAM,SAAS,UAAU;AAGzB,MAAI,cAAc,MAAM,KAAK,cAAc,MAAM,GAAG;AAClD,UAAM,EAAE,qBAAoB,IAAK,MAAM,OACrC,mCAA+C;AAEjD,UAAM,KAAK,MAAM,qBAAqB,EAAE,SAAS,MAAK,CAAE;AACxD,WAAO,EAAE,IAAI,QAAQ,OAAO,OAAM;EACpC;AAEA,QAAM,gBAA+B,MAAM,QAAQ,IACjD,MAAM,IAAI,OAAO,SAAQ;AACvB,UAAM,EAAE,IAAI,MAAK,IAAK;AACtB,QAAI,OAAO,UAAa,KAAK,SAAS,QAAW;AAC/C,YAAM,IAAI,MAAM,oDAAoD;IACtE;AAEA,UAAM,CAAC,KAAK,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;MAC7C,qBAAqB,EAAE;MACvB,OAAO,IAAI;MACX,qBAAqB,KAAK;KAC3B;AAED,WAAO;MACL,IAAI;MACJ,MAAM;MACN,OACE,OAAO,WAAW,YAAY,OAAO,WAAW,WAC5C,YAAY,MAAM,IAClB;;EAEV,CAAC,CAAC;AAGJ,QAAM,2BAAsD;IAC1D;MACE,MAAM,WAAW,QAAQ,OAAO;MAChC,OAAO;MACP;MACA;MACA,SAAS,YAAY,MAAM,EAAE;;MAE7B,gBAAgB,QAAQ,kBAAkB;;;AAI9C,MAAI,gBAAgB,MAAM,GAAG;AAC3B,UAAM,IAAI,MAAM,mDAAmD;EACrE;AAEA,MAAI;AACJ,MAAI,oBAAoB,MAAM,GAAG;AAC/B,UAAM,EAAE,uBAAsB,IAAK,MAAM,OACvC,4BAA6B;AAE/B,UAAM,SAAS,OAAO,UAAS;AAC/B,eAAY,MAAM,uBAAuB,MAAM;EACjD,OAAO;AACL,eAAW,oBAAoB,OAAO,EAAE;EAC1C;AAEA,MAAI;AACF,UAAM,SAAS,MAAM,SAAS,QAAQ;MACpC,QAAQ;MACR,QAAQ;;KACT;AACD,QAAI,OAAO,WAAW,YAAY,QAAQ,QAAQ;AAChD,aAAO,EAAE,IAAI,OAAO,IAAI,QAAQ,OAAO,OAAM;IAC/C;AACA,WAAO,EAAE,IAAI,QAAQ,QAAQ,OAAO,OAAM;EAC5C,SAAS,OAAO;AACd,QAAI,yBAAyB,KAAM,MAAgB,OAAO,GAAG;AAC3D,YAAM,IAAI,MACR,GAAG,OAAO,EAAE,kDAAkD,iBAAiB,QAAQ,MAAM,UAAU,UAAU,KAAK,CAAC,EAAE;IAE7H;AACA,UAAM;EACR;AACF;;;ACzLA,eAAsB,eAAe,EACnC,QACA,QACA,GAAE,GACoB;AACtB,QAAM,UAAU,OAAO,WAAU;AACjC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MACR,0DAA0D,OAAO,EAAE,EAAE;EAEzE;AAGA,MAAI,cAAc,MAAM,KAAK,cAAc,MAAM,GAAG;AAClD,UAAM,EAAE,0BAAyB,IAAK,MAAM,OAC1C,mCAA+C;AAEjD,WAAO,0BAA0B,EAAE,QAAQ,QAAQ,GAAE,CAAE;EACzD;AAEA,MAAI,gBAAgB,MAAM,GAAG;AAC3B,UAAM,IAAI,MAAM,wDAAwD;EAC1E;AAEA,MAAI;AACJ,MAAI,oBAAoB,MAAM,GAAG;AAC/B,UAAM,EAAE,uBAAsB,IAAK,MAAM,OACvC,4BAA6B;AAE/B,UAAM,SAAS,OAAO,UAAS;AAC/B,eAAY,MAAM,uBAAuB,MAAM;EACjD,OAAO;AACL,eAAW,oBAAoB,OAAO,EAAE;EAC1C;AAEA,MAAI;AACF,UAAM,EACJ,SAAS,OACT,SACA,UACA,UAAU,SACV,GAAG,SAAQ,IACR,MAAM,SAAS,QAAQ;MAC1B,QAAQ;MACR,QAAQ,CAAC,EAAE;KACZ;AACD,UAAM,CAAC,QAAQ,UAAU,KAAK,MAAK;AACjC,YAAMC,cAAa,SAAS;AAC5B,UAAIA,eAAc,OAAOA,cAAa;AACpC,eAAO,CAAC,WAAWA,WAAU;AAC/B,UAAIA,eAAc,OAAOA,cAAa;AACpC,eAAO,CAAC,WAAWA,WAAU;AAC/B,UAAIA,eAAc,OAAOA,cAAa;AACpC,eAAO,CAAC,WAAWA,WAAU;AAE/B,UAAIA,gBAAe;AAAa,eAAO,CAAC,WAAW,GAAG;AAEtD,UAAIA,gBAAe;AAAW,eAAO,CAAC,WAAW,GAAG;AACpD,aAAO,CAAC,QAAWA,WAAU;IAC/B,GAAE;AACF,WAAO;MACL,GAAG;MACH;;MAEA,SAAS,UAAU,YAAY,OAAO,IAAI;MAC1C,WACE,qCAAU,IAAI,CAAC,aAAa;QAC1B,GAAG;QACH,aAAa,YAAY,QAAQ,WAAW;QAC5C,SAAS,YAAY,QAAQ,OAAO;QACpC,QAAQ,gBAAgB,QAAQ,MAAuB;cAClD,CAAA;MACT;MACA;MACA;;EAEJ,SAAS,OAAO;AACd,QAAI,yBAAyB,KAAM,MAAgB,OAAO,GAAG;AAC3D,YAAM,IAAI,MACR,GAAG,OAAO,EAAE,kGAAkG;IAElH;AACA,UAAM;EACR;AACF;AAEA,IAAM,kBAAkB;EACtB,OAAO;EACP,OAAO;;;;ACnIT,IAAM,+BAA+B;AAErC,IAAM,MAAM,oBAAI,IAAG;AA2Cb,SAAU,oBACd,SAAmC;AAEnC,QAAM,EAAE,IAAI,OAAO,QAAQ,OAAM,IAAK;AAEtC,QAAM,UAAU,MAAM;AACtB,QAAM,MAAM,GAAG,OAAO,UAAU,EAAE;AAClC,QAAM,oBACJ,QAAQ,qBAAqB;AAE/B,MAAI,IAAI,IAAI,GAAG,GAAG;AAEhB,WAAO,IAAI,IAAI,GAAG;EACpB;AACA,QAAM,UAAU,IAAI,QAAgC,CAAC,SAAS,WAAU;AAEtE,QAAI,eAAe;AAEnB,UAAM,UAAU,iBAAiB;MAC/B;MACA;MACA,kBAAkB,YAAW;AAC3B;AACA,YAAI,gBAAgB,mBAAmB;AACrC,kBAAO;AACP,iBACE,IAAI,MAAM,8BAA8B,iBAAiB,SAAS,CAAC;AAErE;QACF;AACA,YAAI;AACF,gBAAM,SAAS,MAAM,eAAe;YAClC;YACA;YACA;WACD;AACD,cAAI,OAAO,WAAW,aAAa,OAAO,WAAW,WAAW;AAE9D,oBAAO;AAEP,oBAAQ,MAAM;AACd;UACF;QACF,QAAQ;QAER;MACF;KACD;EAEH,CAAC,EAAE,QAAQ,MAAK;AACd,QAAI,OAAO,GAAG;EAChB,CAAC;AAED,MAAI,IAAI,KAAK,OAAO;AACpB,SAAO;AACT;;;AC5BM,SAAU,eAAY;AAK1B,QAAM,eAAe,gBAAe;AACpC,QAAM,cAAc,eAAc;AAElC,SAAO,YAAY;IACjB,YAAY,OAAO,YAAW;AAC5B,YAAM,EAAE,SAAS,aAAY,IAAK;AAClC,YAAM,QAAQ,iCAAQ;AACtB,UAAI,CAAC,UAAU,CAAC,OAAO;AACrB,cAAM,IAAI,MACR,yDAAyD;MAE7D;AAEA,aAAO,UAAU,EAAE,GAAG,SAAS,OAAM,CAAE;IACzC;IACA,WAAW,OAAO,QAAQ,QAAQ,cAAa;AAnGnD;AAqGM,UAAI,CAAC,QAAQ;AACX;MACF;AACA,YAAM,OAAO,UAAU,MAAM,CAAC;AAC9B,UAAI,CAAC,MAAM;AACT;MACF;AACA,YAAM,UAAQ,UAAK,eAAL,mBAAiB,UAAS,KAAK;AAE7C,0BAAoB,MAAM,EACvB,KAAK,MAAK;AA/GnB,YAAAC,KAAA;AAgHU,mBAAWC,SAAQ,UAAU,OAAO;AAClC,sBAAY,kBAAkB;YAC5B,UAAU;cACR;gBACAD,MAAAC,MAAK,eAAL,gBAAAD,IAAiB,MAAM,OAAM,MAAM;gBACnC,KAAAC,MAAK,eAAL,mBAAiB,YAAWA,MAAK;;WAEpC;QACH;AACA,gCAAwB,aAAa,MAAM,EAAE;MAC/C,CAAC,EACA,MAAM,CAAC,UAAS;AACf,gBAAQ,MACN,wDACA,QACA,KAAK;AAEP,eAAO;MACT,CAAC;IACL;GACD;AACH;;;ACpGA,eAAsB,oBACpB,SAMC;AAED,QAAM,kBAAkB,MAAM,UAAU,OAAO;AAC/C,SAAO,oBAAoB;IACzB,GAAG;IACH,mBAAmB,QAAQ;GAC5B;AACH;;;ACqBM,SAAU,uBAAuB,MAEtC;AAKC,QAAM,eAAe,gBAAe;AACpC,QAAM,cAAc,eAAc;AAElC,SAAO,YAAY;IACjB,YAAY,OAAO,YAAW;AAC5B,YAAM,EAAE,SAAS,aAAY,IAAK;AAClC,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MACR,yDAAyD;MAE7D;AAEA,aAAO,oBAAoB;QACzB,GAAG;QACH;QACA,mBAAmB,6BAAM;OAC1B;IACH;IACA,WAAW,OAAO,SAAS,QAAQ,cAAa;AA7FpD;AA+FM,YAAM,OAAO,UAAU,MAAM,CAAC;AAC9B,UAAI,CAAC,MAAM;AACT;MACF;AACA,YAAM,UAAQ,UAAK,eAAL,mBAAiB,UAAS,KAAK;AAE7C,iBAAWC,SAAQ,UAAU,OAAO;AAClC,oBAAY,kBAAkB;UAC5B,UAAU;YACR;cACA,KAAAA,MAAK,eAAL,mBAAiB,MAAM,OAAMA,MAAK,MAAM;cACxC,KAAAA,MAAK,eAAL,mBAAiB,YAAWA,MAAK;;SAEpC;MACH;AACA,8BAAwB,aAAa,MAAM,EAAE;IAC/C;GACD;AACH;;;AC7FM,SAAU,uBACd,MAKa;AA1Bf;AA4BE,SAAO,SAAS;IACd,UAAU,CAAC,uBAAuB,6BAAM,EAAE;IAC1C,SAAS,YAAW;AAClB,UAAI,EAAC,6BAAM,KAAI;AACb,cAAM,IAAI,MAAM,yBAAyB;MAC3C;AACA,aAAO,oBAAoB;QACzB,GAAG;QACH,mBAAmB,KAAK;OACzB;IACH;IACA,SAAS,CAAC,EAAC,6BAAM,UAAO,kCAAM,iBAAN,mBAAoB,YAAW;IACvD,OAAO;GACR;AACH;;;ACoDM,SAAU,gBAQd,oBAGA,SAA2E;AAa3E,MAAI;AACJ,MAAI;AACJ,MAAI;AAGJ,MAAI,OAAO,uBAAuB,YAAY;AAC5C,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MACR,6EAA6E;IAEjF;AACA,UAAM,EAAE,cAAAC,eAAc,UAAU,GAAG,OAAM,IAAK;AAC9C,gBAAYA;AAEZ,eAAW;MACT;MACA,SAAS,MAAM;MACf,SAAS;MACT,cAAc,kBAAkB;MAChC,UAAU,MAAM;;AAGlB,cAAU,MACR,mBAAmB;MACjB,GAAI;MACJ;KACD;EACL;AAEA,MAAI,YAAY,oBAAoB;AAClC,UAAM,EAAE,cAAAA,eAAc,GAAG,GAAE,IAAK;AAChC,gBAAYA;AAEZ,eAAW;MACT;MACA,GAAG,SAAS,MAAM;MAClB,GAAG,SAAS;MACZ,GAAG;MACH,UAAU,GAAG,MAAM;;AAGrB,cAAU,MAAM,aAAa,kBAAkB;EACjD;AAEA,MAAI,CAAC,YAAY,CAAC,SAAS;AACzB,UAAM,IAAI,MACR,8FAA8F;EAElG;AAEA,SAAO,SACL,aAAY;IACV;IACA;IACA,GAAI,aAAa,CAAA;GAClB,CAAC;AAEN;;;ACzKA,IAAAC,gBAA2C;AAmErC,SAAU,kBAId,SAAiD;AAEjD,QAAM,EACJ,UACA,QACA,aAAa,KACb,UAAU,MACV,QAAQ,KAAI,IACV;AACJ,QAAM,wBAAoB,sBAAe,MAAS;AAElD,QAAM,cAAc,eAAc;AAElC,QAAM,gBAAY,uBAChB,OACE,iCAAQ,OAAO,CAAC,KAAK,SAAQ;AAE3B,WAAO,GAAG,GAAG,GAAG,KAAK,IAAI;EAC3B,GAAG,QAAO,WACZ,CAAC,MAAM,CAAC;AAGV,QAAM,eAAW,uBACf,MAAM,CAAC,SAAS,MAAM,IAAI,SAAS,SAAS,QAAQ,SAAS,GAC7D,CAAC,SAAS,SAAS,SAAS,OAAO,SAAS,CAAC;AAG/C,QAAM,QAAQ,SAAS;IACrB;IACA,SAAS,YAAW;AAClB,YAAM,aAAa,aAAa,QAAQ;AACxC,YAAM,qBAAqB,MAAM,gBAAgB,UAAU;AAC3D,wBAAkB,UAAU;AAC5B,YAAM,gBAAgB,MAAM,kBAAkB;QAC5C;QACA;QACA,WAAW,qBAAqB,OAAO,UAAU;OAClD;AACD,aAAO;IACT;IACA;GACD;AAED,+BAAU,MAAK;AACb,QAAI,CAAC,WAAW,CAAC,OAAO;AAEtB;IACF;AAGA,WAAO,oBAAoC;MACzC;MACA,UAAU,CAAC,cAAa;AACtB,YAAI,UAAU,SAAS,KAAK,UAAU,CAAC,GAAG;AACxC,4BAAkB,UAAU,UAAU,CAAC,EAAE;QAC3C;AAEA,oBAAY,aAAa,UAAU,CAAC,YAAiB,CAAA,MAAO;UAC1D,GAAG;UACH,GAAG;SACJ;MACH;MACA;MACA,mBAAmB,kBAAkB;KACtC;EACH,GAAG,CAAC,UAAU,SAAS,QAAQ,aAAa,UAAU,KAAK,CAAC;AAE5D,SAAO;AACT;;;AC7HM,SAAU,yBAAsB;AAQpC,SAAO,YAAY;IACjB,YAAY,CAAC,YAAY,oBAAoB,OAAO;GACrD;AACH;;;ACZM,SAAU,0BAAuB;AAKrC,QAAM,UAAU,iBAAgB;AAChC,SAAO,YAAY;IACjB,YAAY,OAAO,iBAAgB;AACjC,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mBAAmB;MACrC;AACA,aAAO,MAAM,qBAAqB;QAChC;QACA;OACD;IACH;GACD;AACH;;;ACoBM,SAAU,6BACd,SAA0C,CAAA,GAAE;AAE5C,QAAM,UAAU,iBAAgB;AAChC,QAAM,EAAE,QAAO,IAAK;AACpB,SAAO,YAAY;IACjB,YAAY,OAAO,gBAAe;AAChC,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,mBAAmB;MACrC;AACA,aAAO,MAAM,0BAA0B;QACrC;QACA;QACA;OACD;IACH;GACD;AACH;;;ACpDM,SAAU,iBAAc;AAK5B,QAAM,UAAU,iBAAgB;AAChC,SAAO,YAAY;IACjB,YAAY,CAAC,gBAAgB,YAAY,EAAE,aAAa,QAAO,CAAE;GAClE;AACH;;;ACRM,SAAU,qBAAkB;AAKhC,QAAM,UAAU,iBAAgB;AAChC,SAAO,YAAY;IACjB,YAAY,CAAC,gBAAgB,gBAAgB,EAAE,aAAa,QAAO,CAAE;GACtE;AACH;;;AC9BA,IAAAC,iBAAmC;AAyB7B,SAAU,eAAe,SAA8B;AAC3D,QAAM,EAAE,QAAQ,OAAO,UAAU,MAAM,QAAQ,KAAI,IAAK;AAExD,QAAM,cAAc,eAAc;AAElC,QAAM,eAAW,wBAAQ,MAAM,CAAC,MAAM,IAAI,aAAa,GAAY,CAAC,KAAK,CAAC;AAC1E,QAAM,QAAQ,SAAS;;IAGrB;IACA,SAAS,YAAW;AAClB,YAAM,aAAa,aAAa,EAAE,QAAQ,MAAK,CAAE;AACjD,aAAO,MAAM,gBAAgB,UAAU;IACzC;IACA;GACD;AAED,gCAAU,MAAK;AACb,QAAI,CAAC,WAAW,CAAC,OAAO;AAEtB;IACF;AACA,WAAO,iBAAiB;MACtB;MACA;MACA,kBAAkB,CAAC,mBAAkB;AACnC,oBAAY,aAAa,UAAU,cAAc;MACnD;KACD;EACH,GAAG,CAAC,QAAQ,OAAO,SAAS,aAAa,UAAU,KAAK,CAAC;AAEzD,SAAO,MAAM;AACf;;;ACpDA,IAAM,uBAAuB,oBAAI,QAAO;AAelC,SAAU,oBAKd,UAAyE;AAMzE,MAAI,qBAAqB,IAAI,QAAQ,GAAG;AACtC,WAAO,qBAAqB,IAAI,QAAQ;EAG1C;AACA,WAAS,QACP,SAEC;AAED,UAAM,EAAE,UAAU,cAAAC,eAAc,GAAG,OAAM,IAAK;AAE9C,WAAO,SAAS;MACd,UAAU;QACR;QACA,SAAS,MAAM;QACf,SAAS;QACT,cAAc,QAAQ;QACtB,UAAU,MAAM;;MAElB,SAAS,MAAM,SAAS,OAAO;MAC/B,GAAGA;KACJ;EACH;AACA,uBAAqB,IAAI,UAAU,OAAO;AAC1C,SAAO;AACT;;;ACrDM,SAAU,6BAA0B;AACxC,QAAM,cAAc,eAAc;AAElC,SAAO,CAAC,EACN,SACA,gBAAe,MAIZ;AACH,gBAAY,kBAAkB;MAC5B,UAAU,CAAC,gBAAgB,SAAS,eAAe;KACpD;EACH;AACF;;;ACoBM,SAAU,wBACd,QACA,aAA8C;AAE9C,SAAO,SAAS;IACd,GAAG;IACH,UAAU,CAAC,2BAA2B,MAAM;IAC5C,SAAS,MAAK;AACZ,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,0BAA0B;MAC5C;AACA,aAAO,wBAAwB,MAAM;IACvC;IACA,SAAS,CAAC,CAAC;GACZ;AACH;;;ACYA,eAAsB,sBACpB,QAAgC;AAEhC,MAAI;AACF,UAAM,cAAc,IAAI,gBAAe;AACvC,gBAAY,OAAO,iBAAiB,OAAO,aAAa;AACxD,gBAAY,OAAO,SAAS,OAAO,MAAM,SAAQ,CAAE;AACnD,gBAAY,OAAO,SAAS,OAAO,MAAM,SAAQ,CAAE;AAEnD,UAAM,cAAc,YAAY,SAAQ;AACxC,UAAM,MAAM,GAAG,iCAAgC,CAAE,IAAI,WAAW;AAEhE,UAAM,WAAW,MAAM,eAAe,OAAO,MAAM,EAAE,GAAG;AAGxD,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,QAAQ,MAAM,SAAS,KAAI,EAAG,MAAM,MAAM,IAAI;AACpD,YAAM,IAAI,MACR,uBAAuB,SAAS,MAAM,MAAM,SAAS,UAAU,KAAK,SAAS,eAAe,EAAE;IAElG;AAEA,UAAM,QAAgC,MAAM,SAAS,KAAI,GAAI;AAC7D,WAAO;EACT,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,iBAAiB,KAAK,EAAE;EAC1C;AACF;;;ACtDM,SAAU,sBACd,QACA,aAA4C;AAE5C,SAAO,SAAS;IACd,GAAG;IACH,UAAU,CAAC,sBAAsB,MAAM;IACvC,SAAS,MAAK;AACZ,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,qBAAqB;MACvC;AACA,aAAO,sBAAsB,MAAM;IACrC;IACA,SAAS,CAAC,CAAC;GACZ;AACH;;;ACyBA,eAAsB,cACpB,QAAwB;AAExB,MAAI;AACF,UAAM,cAAc,IAAI,gBAAe;AACvC,gBAAY,OAAO,iBAAiB,OAAO,aAAa;AACxD,gBAAY,OAAO,SAAS,OAAO,MAAM,SAAQ,CAAE;AACnD,gBAAY,OAAO,SAAS,OAAO,MAAM,SAAQ,CAAE;AAEnD,UAAM,cAAc,YAAY,SAAQ;AACxC,UAAM,MAAM,GAAG,yBAAwB,CAAE,IAAI,WAAW;AAExD,UAAM,WAAW,MAAM,eAAe,OAAO,MAAM,EAAE,GAAG;AAGxD,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,QAAQ,MAAM,SAAS,KAAI,EAAG,MAAM,MAAM,IAAI;AACpD,YAAM,IAAI,MACR,uBAAuB,SAAS,MAAM,MAAM,SAAS,UAAU,KAAK,SAAS,eAAe,EAAE;IAElG;AAEA,UAAM,QAAwB,MAAM,SAAS,KAAI,GAAI;AACrD,WAAO;EACT,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,iBAAiB,KAAK,EAAE;EAC1C;AACF;;;ACnEM,SAAU,cACd,QACA,aAAoC;AAEpC,SAAO,SAAS;IACd,GAAG;IACH,UAAU,CAAC,iBAAiB,MAAM;IAClC,SAAS,MAAK;AACZ,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,qBAAqB;MACvC;AACA,aAAO,cAAc,MAAM;IAC7B;IACA,SAAS,CAAC,CAAC;GACZ;AACH;;;ACMA,eAAsB,mBAAmB,EACvC,QACA,kBAAiB,GACQ;AACzB,MAAI,kBAAkB,WAAW,aAAa;AAC5C,UAAM,IAAI,MAAM,2BAA2B;EAC7C;AAEA,SAAO,sBAAsB;IAC3B;IACA,UAAU,kBAAkB;;IAE5B,aAAa,kBAAkB;IAC/B,WAAW,kBAAkB;IAC7B,aAAa,kBAAkB,MAAM,YAAY;IACjD,kBAAkB,kBAAkB,MAAM,YAAY;IACtD,WAAW,kBAAkB,MAAM,QAAQ;IAC3C,gBAAgB,kBAAkB,MAAM,QAAQ;IAChD,UAAU,kBAAkB,MAAM;GACnC;AACH;;;AC5CM,SAAU,mBACd,QACAC,eAA0C;AAE1C,SAAO,SAAS;IACd,GAAGA;IACH,UAAU,CAAC,sBAAsB,MAAM;IACvC,SAAS,YAAW;AAClB,UAAI,CAAC,QAAQ;AACX,cAAM,IAAI,MAAM,oBAAoB;MACtC;AACA,aAAO,mBAAmB,MAAM;IAClC;IACA,SAAS,CAAC,CAAC;GACZ;AACH;;;;ACjDA,IAAAC,iBAAoC;AA6S9B,SAAU,SAAS,OAAoB;AA/S7C;AAgTE,QAAM,cAAc,iBAAiB,MAAM,UAAU,OAAO;AAC5D,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAA+B,KAAK;AAChE,QAAM,QAAQ,MAAM,SAAS;AAC7B,QAAM,oBAAoB,qBAAoB;AAC9C,QAAM,gBAAgB,iBAAgB;AACtC,QAAM,eAAe,gBAAe;AACpC,QAAM,WAAW,YACf,cACA,gBACA,WAAM,mBAAN,mBAAsB,IAAI;AAI5B,gCAAU,MAAK;AA7TjB,QAAAC,KAAAC;AA8TI,SAAID,MAAA,MAAM,mBAAN,gBAAAA,IAAsB,OAAO;AAC/B,wBAAkB,aAAa,EAACC,MAAA,MAAM,mBAAN,gBAAAA,IAAsB,KAAK,CAAC;IAC9D;EACF,GAAG,EAAC,WAAM,mBAAN,mBAAsB,OAAO,iBAAiB,CAAC;AAEnD,gCAAU,MAAK;AAnUjB,QAAAD,KAAAC;AAoUI,SAAID,MAAA,MAAM,mBAAN,gBAAAA,IAAsB,QAAQ;AAChC,wBAAkB,cAAaC,MAAA,MAAM,mBAAN,gBAAAA,IAAsB,MAAM;IAC7D;EACF,GAAG,EAAC,WAAM,mBAAN,mBAAsB,QAAQ,iBAAiB,CAAC;AAEpD,gCAAU,MAAK;AACb,QAAI,MAAM,cAAc;AACtB,wBAAkB,gBAAgB,MAAM,YAAY;IACtD;EACF,GAAG,CAAC,MAAM,cAAc,iBAAiB,CAAC;AAE1C,MAAI,UAAU;AACd,QAAM,WACJ,MAAM,cAAc,cAAc,MAAM,aACpC,MAAM,WAAW,WACjB;AAEN,MAAI,CAAC,YAAY,MAAM;AACrB,kBACE,oBAAAC,KAAA,OAAA,EACE,OAAO;MACL,WAAW;MACX,SAAS;MACT,gBAAgB;MAChB,YAAY;OACb,cAED,oBAAAA,KAAC,SAAO,EAAC,MAAK,MAAK,OAAM,gBAAe,CAAA,EAAG,CAAA;EAGjD,OAAO;AACL,kBACE,oBAAAC,MAAA,oBAAAC,UAAA,EAAA,UAAA,KACE,oBAAAF,KAAC,aAAW,EAAC,QAAQ,MAAM,QAAQ,SAAkB,CAAA,GACpD,WAAW,aACV,oBAAAA,KAAC,WAAS,EACR,QAAO,qCAAU,SAAQ,OACzB,SAAS,MACT,iBAAiB,MAAM,iBACvB,OACA,QAAQ,MAAM,QACd,eAAe,YAAY,MAC3B,eAAe,MAAM,eACrB,YACE,MAAM,cAAc;MAClB,MAAM;OAGV,QAAQ,MAAK;AApXzB,UAAAF;AAqXc,YAAIA,MAAA,MAAM,eAAN,gBAAAA,IAAkB,UAAS,eAAe;AAC5C,kBAAU,YAAY;MACxB;IACF,GACA,gBAAgB,MAAM,gBACtB,QAAQ,OAAS,CAAA,GAIpB,WAAW,kBACV,WAAM,eAAN,mBAAkB,UAAS,iBAC3B,MAAM,WAAW,mBACf,oBAAAE,KAAC,mBAAiB,EAChB,IAAI,MAAM,WAAW,aACrB,YAAY,MAAK;AACf,gBAAU,KAAK;IACjB,GACA,QAAQ,MAAK;AACX,gBAAU,KAAK;IACjB,GACA,UAAU,CAAC,SAAQ;AAzYjC,UAAAF,KAAAC;AA0YgB,OAAAA,OAAAD,MAAA,MAAM,eAAN,gBAAAA,IAAkB,sBAAlB,gBAAAC,IAAA,KAAAD,KAAsC;QACpC,MAAM;QACN,SAAS,KAAK,MAAM;QACpB,iBAAiB,KAAK;;IAE1B,EAAC,CAAA,CAEJ,EAAA,CAAA;EAGT;AAEA,aACE,oBAAAE,KAAC,qBAAmB,EAAC,OAAY,cAC/B,oBAAAA,KAAC,gBAAc,EACb,WAAU,WACV,OAAO,MAAM,OACb,WAAW,MAAM,WAAS,cAE1B,oBAAAA,KAAC,eAAa,EAAA,UAAE,QAAO,CAAA,EAAiB,CAAA,EACzB,CAAA;AAGvB;;;;ACjaA,IAAAG,iBAA2D;AA8CrD,SAAU,kBAAe;AAC7B,QAAM,gBAAY,2BAAW,qBAAqB;AAClD,QAAM,CAAC,cAAc,eAAe,QAAI,yBAAS,KAAK;AAEtD,QAAM,cAAU,4BACd,CAAC,UAAiC;AAChC,aAAS,UAAO;AACd,sBAAgB,KAAK;AACrB,gBAAU,MAAS;IACrB;AAEA,WAAO,IAAI,QAAgB,CAAC,SAAS,WAAU;AAC7C,sBAAgB,IAAI;AACpB,uBAAiB,MAAM,UAAU,OAAO,EACrC,KAAK,CAAC,WAAU;AACf,sBACE,qBAAAC,KAACC,QAAK,EAAA,GACA,OACJ,WAAW,CAAC,MAAK;AACf,cAAI,MAAM;AAAM;AAChB,kBAAQ,CAAC;AACT,kBAAO;QACT,GACA,SAAS,MAAK;AACZ,iBAAM;AACN,kBAAO;QACT,GACA,eAAe,OAAM,CAAA,CACrB;MAEN,CAAC,EACA,MAAM,MAAK;AACV,eAAM;AACN,gBAAO;MACT,CAAC;IACL,CAAC;EACH,GACA,CAAC,SAAS,CAAC;AAGb,SAAO,EAAE,SAAS,aAAY;AAChC;AAEA,SAASA,OACP,OAIC;AAED,QAAM,cAAU,wBACd,MACE,MAAM,WACN,kBAAkB;IAChB,aAAa,MAAM;IACnB,QAAQ,MAAM;GACf,GACH,CAAC,MAAM,SAAS,MAAM,aAAa,MAAM,MAAM,CAAC;AAGlD,QAAM,WAAO,wBAAQ,MAAK;AACxB,WAAO,CAAC,gBAAe,KAAM,QAAQ,WAAW,IAC5C,YACA,MAAM,QAAQ;EACpB,GAAG,CAAC,MAAM,MAAM,QAAQ,MAAM,CAAC;AAC/B,QAAM,WAAO,wBAAQ,MAAK;AACxB,WAAO;MACL,kBAAkB,MAAM;MACxB,sBAAsB,MAAM;MAC5B,mBAAmB,MAAM;MACzB,OAAO,MAAM;MACb,cAAc,MAAM;;EAExB,GAAG;IACD,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;GACP;AAED,aACE,qBAAAD,KAAC,wBAAsB,EAAC,OAAO,MAAM,OAAO,QAAQ,MAAI,cACtD,qBAAAA,KAAC,sBAAY,EACX,SAAS,MAAM,SACf,iBAAiB,MAAM,cAAc,SAAY,OAAO,MAAM,WAC9D,oBAAoB,MAAM,oBAC1B,MAAM,MAAM,MACZ,OAAO,MAAM,OACb,QAAQ,MAAM,QACd,eAAe,MAAM,eACrB,MACA,MACA,eAAe,MAAM,eACrB,UAAU,MAAM,UAAU,SAC1B,WAAW,MAAM,WACjB,oBAAoB,MAAM,oBAC1B,gBAAgB,MAAM,gBACtB,SACA,QAAQ,MAAM,QACd,eAAe,MAAM,cAAa,CAAA,EAClC,CAAA;AAGR;;;;ACxCM,SAAU,YAAY,OAAuB;AACjD,QAAM,EAAE,UAAU,iBAAiB,QAAQ,OAAO,aAAa,SAAQ,IACrE;AACF,QAAM,0BAA0B,WAAW,SAAS,WAAW;AAC/D,QAAM,WAAW,YAAY;IAC3B,SAAS;IACT;IACA;GACD;AAED,QAAM,EAAE,MAAM,YAAW,IAAK,gBAAgB,gBAAgB;IAC5D;IACA,SAAS,YAAY,SAAS,YAAY,YAAY,UAAU;IAChE,cAAc;MACZ,SAAS,CAAC;;GAEb;AACD,QAAM,UAAU,iBAAgB;AAChC,QAAM,EAAE,YAAW,IAAK,6BAA4B;AACpD,aACE,qBAAAE,KAAC,mBAAiB,EAChB,UAAU;IACR,UAAU,2BAA2B;IACrC,GAAG;KAEL,aAAa,YAAW;AACtB,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,qBAAqB;IACvC;AACA,UAAM,CAAC,SAAS,EAAE,0BAAyB,CAAE,IAAI,MAAM,QAAQ,IAAI;MACjE,oBAAoB;QAClB;QACA;QACA;OACD;MACD,OACE,yCAAuE;KAE1E;AACD,UAAM,YAAY,MAAM,0BAA0B;MAChD,aAAa;MACb;KACD;AACD,QAAI,WAAW;AACb,YAAM,YAAY,SAAS;IAC7B;AACA,WAAO;EACT,GAAC,GACG,OAAK,SAEA,CAAA;AAGf;AAOA,eAAe,eACb,SAAqD;AA3KvD;AA6KE,QAAM,EAAE,UAAU,QAAO,IAAK;AAC9B,QAAM,CAAC,kBAAkB,GAAG,IAAI,MAAM,QAAQ,IAAI;IAChD,oBAAoB,OAAO;IAC3B,UAAUC,QAAO,EAAE,UAAU,QAAO,CAAE,IAAI;GAC3C;AACD,MAAI,SAAS;AACX,WAAO;MACL,QAAO,gCAAK,aAAL,mBAAe;MACtB,OAAM,gCAAK,aAAL,mBAAe;;EAEzB;AACA,SAAO;IACL,OAAO,qDAAkB;IACzB,MAAM,qDAAkB;;AAE5B;AAKA,eAAe,oBAAoB,EACjC,UACA,SACA,YAAW,GAKZ;AACC,UAAQ,YAAY,MAAM;IACxB,KAAK;AACH,aAAO,MAAM,iBAAiB,EAAE,UAAU,SAAS,YAAW,CAAE;IAClE,KAAK;AACH,aAAO,MAAM,kBAAkB,EAAE,UAAU,SAAS,YAAW,CAAE;IACnE,KAAK,SAAS;AACZ,aAAO,MAAM,gBAAgB,EAAE,UAAU,SAAS,YAAW,CAAE;IACjE;IACA;AACE,YAAM,IAAI,MACR,uGAAuG;EAE7G;AACF;AAKA,eAAsB,iBAAiB,EACrC,UACA,SACA,YAAW,GAKZ;AACC,QAAM,EAAE,QAAO,IAAK,MAAM,OACxB,uBAA4D;AAG9D,SAAO,QAAQ;IACb;IACA,IAAI,YAAY,OAAM,mCAAS,YAAW;IAC1C,UAAU,YAAY;IACtB,MAAM,YAAY;GACnB;AACH;AAKA,eAAsB,kBAAkB,EACtC,UACA,SACA,YAAW,GAKZ;AACC,QAAM,EAAE,QAAO,IAAK,MAAM,OACxB,uBAA6D;AAG/D,SAAO,QAAQ;IACb;IACA,IAAI,YAAY,OAAM,mCAAS,YAAW;IAC1C,UAAU,YAAY;IACtB,SAAS,YAAY;IACrB,MAAM,YAAY;GACnB;AACH;AAKA,eAAsB,gBAAgB,EACpC,UACA,SACA,YAAW,GAKZ;AAGC,QAAM,EAAE,QAAO,IAAK,MAAM,OACxB,uBAA2D;AAG7D,MAAI,cAAc,aAAa;AAC7B,WAAO,QAAQ;MACb;MACA,IAAI,YAAY,OAAM,mCAAS,YAAW;MAC1C,UAAU,YAAY;MACtB,MAAM,YAAY;KACnB;EACH;AACA,MAAI,mBAAmB,aAAa;AAClC,WAAO,QAAQ;MACb;MACA,IAAI,YAAY,OAAM,mCAAS,YAAW;MAC1C,eAAe,YAAY;MAC3B,MAAM,YAAY;KACnB;EACH;AACA,QAAM,IAAI,MAAM,mCAAmC;AACrD;;;;AC3SA,IAAAC,iBAA4B;AA8EtB,SAAU,uBAAuB,OAAkC;AACvE,QAAM,EACJ,iBACA,WACA,UACA,OACA,QACA,UACA,SAAQ,IACN;AACJ,QAAM,0BAA0B,WAAW,SAAS,WAAW;AAC/D,QAAM,UAAU,iBAAgB;AAChC,QAAM,WAAW,YAAY;IAC3B,SAAS;IACT;IACA;GACD;AAED,QAAM,EAAE,MAAM,YAAW,IAAK,gBAAgBC,iBAAgB;IAC5D;IACA;IACA,cAAc;MACZ,SAAS,CAAC;;GAEb;AAED,QAAM,EAAE,YAAW,IAAK,6BAA4B;AAEpD,QAAM,4BAAwB,4BAAY,YAAW;AACnD,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,qBAAqB;IACvC;AACA,UAAM,CAAC,SAAS,EAAE,0BAAyB,GAAI,EAAE,eAAc,CAAE,IAC/D,MAAM,QAAQ,IAAI;MAChB,WAAW;QACT;QACA;OACD;MACD,OACE,yCAAuE;MAEzE,OACE,8BAAkF;KAErF;AACH,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,uCAAuC,SAAS,EAAE;IACpE;AAEA,QAAI,YAAY;AAEhB,QAAI,QAAQ,MAAM,SAAS,UAAU;AACnC,UAAI,OAAO,aAAa,aAAa,aAAa,MAAM,WAAW,KAAK;AACtE,cAAM,IAAI,MACR,uEAAuE;MAE3E;IACF,WAAW,QAAQ,MAAM,SAAS,WAAW;AAC3C,UAAI,OAAO,aAAa,UAAU;AAChC,YAAI,WAAW,QAAQ,UAAU;AAC/B,gBAAM,IAAI,MACR,iDAAiD,QAAQ,SAAS,SAAQ,CAAE,EAAE;QAElF;AACA,YAAI,WAAW,IAAI;AACjB,gBAAM,IAAI,MAAM,yCAAyC;QAC3D;AACA,oBAAY;MACd;AACA,kBAAY,QAAQ;IACtB;AAEA,UAAM,QAAQ,eAAe;MAC3B;MACA;MACA,UAAU;MACV,YAAW,mCAAS,YAAW;KAChC;AAED,UAAM,YAAY,MAAM,0BAA0B;MAChD,aAAa;MACb;KACD;AAED,QAAI,WAAW;AACb,YAAM,YAAY,SAAS;IAC7B;AAEA,WAAO;EACT,GAAG,CAAC,SAAS,UAAU,UAAU,WAAW,WAAW,CAAC;AAExD,aACE,qBAAAC,KAAC,mBAAiB,EAChB,UAAU;IACR,UAAU,2BAA2B;IACrC,GAAG;KAEL,aAAa,MAAM,sBAAqB,GAAE,GACtC,OAAK,SAEA,CAAA;AAGf;AAKA,eAAeD,gBACb,SAAsD;AA7LxD;AA+LE,QAAM,UAAU,MAAM,WAAW,OAAO;AACxC,MAAI,CAAC,SAAS;AACZ,WAAO,EAAE,MAAM,QAAW,OAAO,OAAS;EAC5C;AACA,SAAO;IACL,OAAM,mBAAQ,UAAR,mBAAe,aAAf,mBAAyB;IAC/B,QAAO,mBAAQ,UAAR,mBAAe,aAAf,mBAAyB;;AAEpC;;;;ACrMA,IAAAE,iBAA4B;;;AC4BrB,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAKd,IAAM,aAAa;EACjB;IACE,MAAM;IACN,MAAM;;;AAoFJ,SAAU,cACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,MAAM;IAChC;IACA,OAAO,YAAS;AA5KpB;AA4KwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA7KzB;AA6K6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AA9KlB;AA8KsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AA/KvB;AA+K2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AAhL3B;AAgL+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AAjLnC;AAkLO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAnLpB;AAmLwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AApLvB;AAoL2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AArLzB;AAqL6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AAtLhC;AAuLO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;ACvGM,SAAUC,eACd,SAAoD;AAEpD,SAAqB,cAAc;IACjC,UAAU,QAAQ;IAClB,aAAa,YAAW;AACtB,YAAM,gBAAgB,YAAY;QAChC,GAAG,QAAQ;QACX,SAAS,QAAQ;OAClB;AAED,YAAM,YAAY,aAAa,QAAQ,QAAQ;AAE/C,YAAM,CAAC,eAAe,gBAAgB,YAAY,IAAI,MAAM,QAAQ,IAAI;QACtE,SAAS,EAAE,UAAU,cAAa,CAAE;QACpC,UAAU,EAAE,UAAU,cAAa,CAAE;QACrC,qBAAqB,WAAW,EAAE,UAAU,SAAQ,CAAE;OACvD;AAGD,UAAI,CAAC,iBAAiB,CAAC,gBAAgB;AACrC,cAAM,IAAI,MAAM,mDAAmD;MACrE;AAGA,UAAI,iBAAiB,OACnB,KAAK,OAAO,QAAQ,kBAAkB,oBAAI,KAAI,GAAI,QAAO,IAAK,GAAI,CAAC;AAErE,YAAM,eAAe,OACnB,KAAK,OAED,QAAQ,gBACR,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,MAAM,KAAK,KAAK,KAAK,GAAI,GACpD,QAAO,IAAK,GAAI,CACnB;AAGH,UAAI,kBAAkB,aAAa,WAAW;AAE5C,yBAAiB,aAAa,YAAY;MAC5C;AACA,UAAI,kBAAkB,cAAc;AAClC,cAAM,IAAI,MAAM,qCAAqC;MACvD;AAGA,UAAI;AACJ,UAAI,eAAe;AAEjB,mBAAW;MACb,OAAO;AAEL,mBAAW,QAAQ,YAAY;MACjC;AAGA,YAAM,kBACJ,QAAQ,2BAA2B;AACrC,UAAI;AACJ,UAAI,mBAAmB,SAAS;AAE9B,YAAI,qBAAqB,eAAe,GAAG;AACzC,0BAAgB,QAAQ,QAAQ,eAAe,EAAE;QACnD,OAAO;AAEL,gBAAM,mBAAmB,YAAY;YACnC,GAAG,QAAQ;YACX,SAAS;WACV;AACD,gBAAM,EAAE,SAAQ,IAAK,MAAM,OAAO,wBAAiC;AACnE,gBAAM,mBAAmB,MAAM,SAAS;YACtC,UAAU;WACX;AACD,0BAAgB,QAAQ,QAAQ,eAAe,gBAAgB;QACjE;MACF,OAAO;AACL,wBAAgB,OAAO,QAAQ,gBAAgB;MACjD;AAEA,aAAO;QACL,QAAQ;UACN,eAAe,QAAQ;UACvB,SAAS,QAAQ;UACjB,UAAU,QAAQ,2BAA2B;UAC7C;UACA;UACA;UACA;UACA,UAAU,QAAQ,qBAAqB;;QAEzC,WAAW;UACT,UAAU;;;;IAGhB;GACD;AACH;;;AF5HM,SAAU,0BACd,OAAqC;AAErC,QAAM,EACJ,iBACA,OACA,QACA,UACA,UACA,sBACA,QAAO,IACL;AACJ,QAAM,sBAAsB,YAAY;IACtC,SAAS;IACT;IACA;GACD;AACD,QAAM,UAAU,iBAAgB;AAChC,QAAM,0BAA0B,WAAW,SAAS,WAAW;AAC/D,QAAM,cAAc,YAAY;IAC9B,SAAS;IACT;IACA;GACD;AACD,QAAM,EAAE,MAAM,YAAW,IAAK,gBAAgBC,iBAAgB;IAC5D,UAAU;IACV;IACA,cAAc;MACZ,SAAS,CAAC;;GAEb;AACD,QAAM,EAAE,YAAW,IAAK,6BAA4B;AAEpD,QAAM,yBAAqB,4BAAY,YAAW;AAChD,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,qBAAqB;IACvC;AACA,UAAM,CAAC,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;MACxC,SAAS,EAAE,UAAU,YAAW,CAAE;MAClC,UAAU,EAAE,UAAU,YAAW,CAAE;KACpC;AACD,QAAI,CAAC,UAAU,CAAC,OAAO;AACrB,YAAM,IAAI,MAAM,wCAAwC;IAC1D;AAEA,QAAI,QAAQ;AACV,YAAM,CAAC,EAAE,iBAAgB,GAAI,EAAE,kBAAiB,CAAE,IAAI,MAAM,QAAQ,IAAI;QACtE,OACE,gCAAsF;QAExF,OACE,iCAAwF;OAE3F;AACD,YAAM,aAAa,MAAM,iBAAiB;QACxC,UAAU;QACV,UAAU,oBAAoB;QAC9B,OAAO,QAAQ;OAChB;AACD,UAAI,CAAC,YAAY;AACf,cAAM,cAAc,kBAAkB;UACpC,UAAU;UACV,UAAU,oBAAoB;UAC9B,UAAU;SACX;AACD,cAAM,YAAY,WAAW;MAC/B;IACF,OAAO;AACL,YAAM,CAAC,EAAE,iBAAgB,GAAI,EAAE,kBAAiB,GAAI,EAAE,YAAW,CAAE,IACjE,MAAM,QAAQ,IAAI;QAChB,OACE,gCAAqF;QAEvF,OACE,iCAAuF;QAEzF,OACE,2BAAgF;OAEnF;AACH,YAAM,CAAC,YAAY,aAAa,IAAI,MAAM,QAAQ,IAAI;QACpD,iBAAiB;UACf,UAAU;UACV,UAAU,oBAAoB;UAC9B,OAAO,QAAQ;SAChB;QACD,YAAY,EAAE,UAAU,aAAa,SAAS,MAAM,QAAO,CAAE;OAC9D;AAED,UACE,CAAC,cACD,cAAc,YAAW,MACvB,oBAAoB,QAAQ,YAAW,GACzC;AACA,cAAM,cAAc,kBAAkB;UACpC,UAAU;UACV,UAAU,oBAAoB;UAC9B,UAAU;SACX;AACD,cAAM,YAAY,WAAW;MAC/B;IACF;AACA,UAAM,YAAYC,eAAc;MAC9B,UAAU;MACV,GAAG;KACJ;AAED,WAAO;EACT,GAAG,CAAC,qBAAqB,OAAO,SAAS,aAAa,WAAW,CAAC;AAElE,aACE,qBAAAC,KAAC,mBAAiB,EAChB,aAAa,MAAM,mBAAkB,GACrC,UAAU;IACR,UAAU,2BAA2B;IACrC,GAAG;KACJ,GACG,OAAK,SAEA,CAAA;AAGf;AAKA,eAAeF,gBACb,SAEE;AA7LJ;AA+LE,QAAM,CACJ,EAAE,qBAAAG,qBAAmB,GACrB,EAAE,QAAQ,UAAS,GACnB,EAAE,QAAQ,WAAU,CAAE,IACpB,MAAM,QAAQ,IAAI;IACpB,OAAO,mCAAiE;IACxE,OAAO,sBAAoD;IAC3D,OAAO,sBAAqD;GAC7D;AACD,QAAM,CAAC,OAAO,QAAQ,gBAAgB,IAAI,MAAM,QAAQ,IAAI;IAC1D,SAAS,OAAO;IAChB,UAAU,OAAO;IACjBA,qBAAoB,OAAO;GAC5B;AACD,MAAI,OAAO;AACT,UAAM,MAAM,MAAM,UAAU,OAAO;AACnC,WAAO;MACL,QAAO,gCAAK,aAAL,mBAAe;MACtB,OAAM,gCAAK,aAAL,mBAAe;;EAEzB;AAEA,MAAI,QAAQ;AACV,UAAM,MAAM,MAAM,WAAW,OAAO;AACpC,WAAO;MACL,QAAO,gCAAK,aAAL,mBAAe;MACtB,OAAM,gCAAK,aAAL,mBAAe;;EAEzB;AAEA,SAAO;IACL,OAAO,qDAAkB;IACzB,MAAM,qDAAkB;;AAE5B;;;;AG/NA,IAAAC,iBAA0C;AAqBnC,IAAM,yBAAqC,8BAEhD,MAAS;AAKL,SAAU,gBAAa;AAC3B,QAAM,UAAM,2BAAW,kBAAkB;AACzC,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MACR,yHAAyH;EAE7H;AACA,SAAO;AACT;AAgCM,SAAU,YAAY,OAAgD;AAC1E,aACE,qBAAAC,KAAC,mBAAmB,UAAQ,EAAC,OAAO,OAAK,UACtC,MAAM,SAAQ,CAAA;AAGrB;;;;;;ACnEA,eAAsB,WAAW,SAAyB;AACxD,SAAO,UACL,YAAW;AACT,UAAM,MAAM,MAAM,QAAQ,WAAW;MACnC,OAAU;QACR,GAAG;QACH,YAAY;;OACb;MACDC,QAAW;QACT,GAAG;QACH,YAAY;;OACb;KACF,EAAE,KAAK,CAAC,CAAC,gBAAgB,eAAe,MAAK;AAG5C,UACE,eAAe,WAAW,eAC1B,eAAe,MAAM,UACrB;AACA,eAAO,eAAe;MACxB;AACA,UACE,gBAAgB,WAAW,eAC3B,gBAAgB,MAAM,UACtB;AACA,eAAO,gBAAgB;MACzB;AACA,YAAM,IAAI,MAAM,6BAA6B;IAC/C,CAAC;AACD,WAAO;EACT,GACA;IACE,UAAU,YAAY,QAAQ,SAAS,MAAM,EAAE,IAAI,QAAQ,SAAS,OAAO,IAAI,QAAQ,QAAQ,SAAQ,CAAE;IACzG,WAAW,KAAK,KAAK;GACtB;AAEL;;;ACyCM,SAAU,QAAQ,EACtB,kBACA,mBACA,cAAAC,eACA,cACA,GAAG,UAAS,GACC;AACb,QAAM,EAAE,UAAU,QAAO,IAAK,cAAa;AAE3C,QAAM,YAAY,SAAS;IACzB,UAAU,YAAY;MACpB,iBAAiB,SAAS;MAC1B,SAAS,SAAS,MAAM;MACxB;MACA;KACD;IACD,SAAS,YACP,aAAa,EAAE,cAAc,UAAU,QAAO,CAAE;IAClD,GAAGA;GACJ;AAED,MAAI,UAAU,WAAW;AACvB,WAAO,oBAAoB;EAC7B;AAEA,MAAI,CAAC,UAAU,MAAM;AACnB,WAAO,qBAAqB;EAC9B;AACA,aAAO,qBAAAC,KAAA,QAAA,EAAA,GAAU,WAAS,UAAG,UAAU,KAAI,CAAA;AAC7C;AAKM,SAAU,YAAY,OAK3B;AACC,QAAM,EAAE,SAAS,SAAS,cAAc,gBAAe,IAAK;AAC5D,SAAO;IACL;IACA;IACA;IACA,QAAQ,SAAQ;IAChB;MACE,UACE,OAAO,iBAAiB,WACpB,eACA,OAAO,iBAAiB,aACtB,cAAc,YAAY,IAC1B;;;AAGd;AAKA,eAAsB,aAAa,OAIlC;AACC,QAAM,EAAE,cAAc,UAAU,QAAO,IAAK;AAC5C,MAAI,OAAO,iBAAiB,UAAU;AACpC,WAAO;EACT;AACA,MAAI,OAAO,iBAAiB,YAAY;AACtC,WAAO,aAAY;EACrB;AACA,QAAM,MAAM,MAAM,WAAW,EAAE,UAAU,QAAO,CAAE,EAAE,MAAM,MAAM,MAAS;AACzE,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AACA,MAAI,OAAO,IAAI,SAAS,SAAS,UAAU;AACzC,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AACA,SAAO,IAAI,SAAS;AACtB;;;;ACjFM,SAAU,eAAe,EAC7B,kBACA,mBACA,cAAAC,eACA,qBACA,GAAG,UAAS,GACQ;AACpB,QAAM,EAAE,UAAU,QAAO,IAAK,cAAa;AAC3C,QAAM,YAAY,SAAS;IACzB,UAAU;MACR;MACA,SAAS,MAAM;MACf,SAAS;MACT,QAAQ,SAAQ;MAChB;QACE,UACE,OAAO,wBAAwB,WAC3B,sBACA,OAAO,wBAAwB,aAC7B,cAAc,mBAAmB,IACjC;;;IAGZ,SAAS,YACP,oBAAoB,EAAE,qBAAqB,UAAU,QAAO,CAAE;IAChE,GAAGA;GACJ;AAED,MAAI,UAAU,WAAW;AACvB,WAAO,oBAAoB;EAC7B;AAEA,MAAI,CAAC,UAAU,MAAM;AACnB,WAAO,qBAAqB;EAC9B;AAEA,aAAO,qBAAAC,KAAA,QAAA,EAAA,GAAU,WAAS,UAAG,UAAU,KAAI,CAAA;AAC7C;AAKA,eAAsB,oBAAoB,OAIzC;AACC,QAAM,EAAE,qBAAqB,UAAU,QAAO,IAAK;AACnD,MAAI,OAAO,wBAAwB,UAAU;AAC3C,WAAO;EACT;AACA,MAAI,OAAO,wBAAwB,YAAY;AAC7C,WAAO,oBAAmB;EAC5B;AACA,QAAM,MAAM,MAAM,WAAW,EAAE,UAAU,QAAO,CAAE,EAAE,MAAM,MAAM,MAAS;AACzE,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AACA,MAAI,OAAO,IAAI,SAAS,gBAAgB,UAAU;AAChD,UAAM,IAAI,MAAM,mCAAmC;EACrD;AACA,SAAO,IAAI,SAAS;AACtB;;;;ACtBM,SAAU,SAAS,EACvB,kBACA,mBACA,cAAAC,eACA,eACA,GAAG,mBAAkB,GACP;AACd,QAAM,EAAE,UAAU,QAAO,IAAK,cAAa;AAC3C,QAAM,aAAa,SAAS;IAC1B,UAAUC,aAAY;MACpB,iBAAiB,SAAS;MAC1B,SAAS,SAAS,MAAM;MACxB;MACA;KACD;IACD,SAAS,YACP,cAAc,EAAE,eAAe,UAAU,QAAO,CAAE;IACpD,GAAGD;GACJ;AAED,MAAI,WAAW,WAAW;AACxB,WAAO,oBAAoB;EAC7B;AAEA,MAAI,CAAC,WAAW,MAAM;AACpB,WAAO,qBAAqB;EAC9B;AAEA,aACE,qBAAAE,KAAC,eAAa,EACZ,QAAQ,SAAS,QACjB,KAAK,WAAW,KAAK,KACrB,QAAQ,WAAW,KAAK,QAAM,GAC1B,mBAAkB,CAAA;AAG5B;AAKM,SAAUD,aAAY,OAQ3B;AACC,QAAM,EAAE,SAAS,SAAS,eAAe,gBAAe,IAAK;AAC7D,SAAO;IACL;IACA;IACA;IACA,QAAQ,SAAQ;IAChB;MACE,UACE,OAAO,kBAAkB,WACrB,gBACA,OAAO,kBAAkB,aACvB,cAAc,aAAa,IAC3B;;;AAGd;AAKA,eAAsB,cAAc,OAOnC;AACC,QAAM,EAAE,eAAe,UAAU,QAAO,IAAK;AAC7C,MAAI,OAAO,kBAAkB,UAAU;AACrC,WAAO;EACT;AACA,MAAI,OAAO,kBAAkB,YAAY;AACvC,WAAO,cAAa;EACtB;AACA,QAAM,MAAM,MAAM,WAAW,EAAE,UAAU,QAAO,CAAE,EAAE,MAAM,MAAM,MAAS;AACzE,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AACA,QAAM,gBAAgB,IAAI,SAAS;AACnC,QAAM,QAAQ,IAAI,SAAS,SAAS,IAAI,SAAS;AACjD,MAAI,eAAe;AACjB,WAAO;MACL,KAAK;MACL,QAAQ,SAAS;;EAErB;AACA,MAAI,OAAO;AACT,WAAO;MACL,KAAK;MACL,QAAQ;;EAEZ;AACA,QAAM,IAAI,MAAM,6BAA6B;AAC/C;;;;ACvMM,SAAU,UAAU,EACxB,KACA,QACA,WACA,GAAG,MAAK,GAQT;AACC,MAAI,CAAC,OAAO,UAAU;AACpB,UAAM,IAAI,MAAM,2CAA2C;EAC7D;AAEA,QAAM,eAAe,gBAAe;AACpC,QAAM,WAAW,6CAAc;AAE/B,QAAM,EACJ,MAAM,EAAE,cAAc,WAAU,IAAK,CAAA,EAAE,IACrC,SAAS;IACX,UAAU,CAAC,cAAc,UAAU,KAAK,OAAO,UAAU,SAAS;IAClE,SACE,iBACC,kBAAkB,YAAY,KAC7B,aAAa,WACb,aAAa;IACjB,SAAS,YAAW;AAClB,YAAM,UAAU,IAAI,oBAAoB;QACtC,SAAS;QACT,UAAU,OAAO;QACjB;OACD;AAED,YAAME,gBAAe,MAAM,oBAAoB,eAAe;AAC9D,YAAMC,cAAa,MAAM,QAAQ,cAAa;AAE9C,aAAO,EAAE,cAAAD,eAAc,YAAAC,YAAU;IACnC;GACD;AAED,QAAM,MAAM,IAAI,IAAI,GAAG;AACvB,MAAI,UAAU;AACZ,QAAI,aAAa,IAAI,YAAY,aAAa,UAAU,UAAU,QAAQ;EAC5E;AACA,MAAI,cAAc;AAChB,QAAI,aAAa,IAAI,gBAAgB,YAAY;EACnD;AACA,MAAI,YAAY;AACd,QAAI,aAAa,IAAI,cAAc,UAAU;EAC/C;AAEA,aACE,qBAAAC,KAAA,UAAA,EACE,KAAK,UAAU,IAAI,SAAQ,CAAE,GAC7B,OAAM,QACN,QAAO,QACP,iBAAe,MAAA,GACX,MAAK,CAAA;AAGf;;;;AC9DM,SAAU,SAAS,EACvB,MACA,QACA,WACA,UACA,GAAG,MAAK,GAMqD;AAC7D,MAAI,CAAC,OAAO,UAAU;AACpB,UAAM,IAAI,MAAM,0CAA0C;EAC5D;AAEA,QAAM,eAAe,gBAAe;AACpC,QAAM,WAAW,6CAAc;AAE/B,QAAM,EACJ,MAAM,EAAE,cAAc,WAAU,IAAK,CAAA,EAAE,IACrC,SAAS;IACX,UAAU,CAAC,aAAa,UAAU,MAAM,OAAO,UAAU,SAAS;IAClE,SACE,iBACC,kBAAkB,YAAY,KAC7B,aAAa,WACb,aAAa;IACjB,SAAS,YAAW;AAClB,YAAM,UAAU,IAAI,oBAAoB;QACtC,SAAS;QACT,UAAU,OAAO;QACjB;OACD;AAED,YAAMC,gBAAe,MAAM,oBAAoB,eAAe;AAC9D,YAAMC,cAAa,MAAM,QAAQ,cAAa;AAE9C,aAAO,EAAE,cAAAD,eAAc,YAAAC,YAAU;IACnC;GACD;AAED,QAAM,MAAM,IAAI,IAAI,IAAI;AACxB,MAAI,UAAU;AACZ,QAAI,aAAa,IAAI,YAAY,aAAa,UAAU,UAAU,QAAQ;EAC5E;AACA,MAAI,cAAc;AAChB,QAAI,aAAa,IAAI,gBAAgB,YAAY;EACnD;AACA,MAAI,YAAY;AACd,QAAI,aAAa,IAAI,cAAc,UAAU;EAC/C;AAEA,aACE,qBAAAC,KAAA,KAAA,EAAG,MAAM,UAAU,IAAI,SAAQ,CAAE,GAAC,GAAM,OAAK,SAClC,CAAA;AAGf;;;;ACrFA,IAAAC,iBAA0C;AAwB1C,IAAM,2BAAuC,8BAE3C,MAAS;AAkCL,SAAU,cACd,OAAkD;AAElD,aACE,qBAAAC,KAAC,qBAAqB,UAAQ,EAAC,OAAO,OAAK,UACxC,MAAM,SAAQ,CAAA;AAGrB;AAKM,SAAU,kBAAe;AAC7B,QAAM,UAAM,2BAAW,oBAAoB;AAC3C,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MACR,qIAAqI;EAEzI;AACA,SAAO;AACT;;;;ACmEM,SAAU,UAAU,EACxB,cACA,UACA,kBACA,mBACA,cAAAC,eACA,GAAG,UAAS,GACG;AACf,QAAM,EAAE,SAAS,QAAQ,MAAK,IAAK,gBAAe;AAClD,QAAM,YAAY,SAAS;IACzB,UAAU,aAAa,EAAE,SAAS,MAAM,IAAI,cAAc,QAAO,CAAE;IACnE,SAAS,YACP,eAAe,EAAE,SAAS,OAAO,QAAQ,aAAY,CAAE;IACzD,GAAGA;GACJ;AAED,MAAI,UAAU,WAAW;AACvB,WAAO,oBAAoB;EAC7B;AAEA,MAAI,CAAC,UAAU,MAAM;AACnB,WAAO,qBAAqB;EAC9B;AAEA,MAAI,YAAY,OAAO,aAAa,YAAY;AAC9C,eAAO,qBAAAC,KAAA,QAAA,EAAA,GAAU,WAAS,UAAG,SAAS,UAAU,IAAI,EAAC,CAAA;EACvD;AAEA,aAAO,qBAAAA,KAAA,QAAA,EAAA,GAAU,WAAS,UAAG,UAAU,KAAI,CAAA;AAC7C;AAKA,eAAsB,eAAe,OAKpC;AACC,QAAM,EAAE,cAAc,SAAS,QAAQ,MAAK,IAAK;AACjD,MAAI,OAAO,iBAAiB,UAAU;AACpC,WAAO;EACT;AACA,MAAI,OAAO,iBAAiB,YAAY;AACtC,WAAO,aAAY;EACrB;AACA,MAAI,QAAQ,YAAW,MAAO,qBAAqB,YAAW,GAAI;AAEhE,WAAO,iBAAiB,KAAK,EAAE,KAAK,CAAC,SAAS,KAAK,eAAe,IAAI;EACxE;AAIA,QAAM,WAAW,YAAY,EAAE,SAAS,QAAQ,MAAK,CAAE;AACvD,QAAM,CAAC,OAAO,gBAAgB,IAAI,MAAM,QAAQ,IAAI;IAClD,KAAK,EAAE,SAAQ,CAAE,EAAE,MAAM,MAAM,MAAS;IACxC,oBAAoB,EAAE,SAAQ,CAAE,EAAE,MAAM,MAAM,MAAS;GACxD;AACD,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;EACT;AACA,MAAI,QAAO,qDAAkB,UAAS,UAAU;AAC9C,WAAO,iBAAiB;EAC1B;AACA,QAAM,IAAI,MACR,+DAA+D;AAEnE;AAKM,SAAU,aAAa,OAI5B;AACC,QAAM,EAAE,SAAS,SAAS,aAAY,IAAK;AAC3C,SAAO;IACL;IACA;IACA;IACA;MACE,UACE,OAAO,iBAAiB,WACpB,eACA,OAAO,iBAAiB,aACtB,cAAc,YAAY,IAC1B;;;AAGd;;;;AC/FM,SAAUC,aAAY,EAC1B,gBACA,UACA,kBACA,mBACA,cAAAC,eACA,GAAG,UAAS,GACK;AACjB,QAAM,EAAE,SAAS,QAAQ,MAAK,IAAK,gBAAe;AAClD,QAAM,cAAc,SAAS;IAC3B,UAAUC,cAAa,EAAE,SAAS,MAAM,IAAI,SAAS,eAAc,CAAE;IACrE,SAAS,YACP,iBAAiB,EAAE,gBAAgB,SAAS,OAAO,OAAM,CAAE;IAC7D,GAAGD;GACJ;AAED,MAAI,YAAY,WAAW;AACzB,WAAO,oBAAoB;EAC7B;AAEA,MAAI,CAAC,YAAY,MAAM;AACrB,WAAO,qBAAqB;EAC9B;AAEA,MAAI,YAAY,OAAO,aAAa,YAAY;AAC9C,eAAO,qBAAAE,KAAA,QAAA,EAAA,GAAU,WAAS,UAAG,SAAS,YAAY,IAAI,EAAC,CAAA;EACzD;AAEA,aAAO,qBAAAA,KAAA,QAAA,EAAA,GAAU,WAAS,UAAG,YAAY,KAAI,CAAA;AAC/C;AAKA,eAAsB,iBAAiB,OAKtC;AACC,QAAM,EAAE,gBAAgB,SAAS,QAAQ,MAAK,IAAK;AACnD,MAAI,OAAO,mBAAmB,UAAU;AACtC,WAAO;EACT;AACA,MAAI,OAAO,mBAAmB,YAAY;AACxC,WAAO,eAAc;EACvB;AACA,MAAI,QAAQ,YAAW,MAAO,qBAAqB,YAAW,GAAI;AAEhE,WAAO,iBAAiB,KAAK,EAAE,KAAK,CAAC,SAAS,KAAK,eAAe,MAAM;EAC1E;AAIA,QAAM,WAAW,YAAY,EAAE,SAAS,QAAQ,MAAK,CAAE;AACvD,QAAM,CAAC,SAAS,gBAAgB,IAAI,MAAM,QAAQ,IAAI;IACpD,OAAO,EAAE,SAAQ,CAAE,EAAE,MAAM,MAAM,MAAS;IAC1C,oBAAoB,EAAE,SAAQ,CAAE,EAAE,MAAM,MAAM,MAAS;GACxD;AACD,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO;EACT;AACA,MAAI,QAAO,qDAAkB,YAAW,UAAU;AAChD,WAAO,iBAAiB;EAC1B;AACA,QAAM,IAAI,MACR,mEAAmE;AAEvE;AAKM,SAAUD,cAAa,OAI5B;AACC,QAAM,EAAE,SAAS,SAAS,eAAc,IAAK;AAC7C,SAAO;IACL;IACA;IACA;IACA;MACE,UACE,OAAO,mBAAmB,WACtB,iBACA,OAAO,mBAAmB,aACxB,cAAc,cAAc,IAC5B;;;AAGd;;;;AClIM,SAAU,UAAU,EACxB,cACA,kBACA,mBACA,cAAAE,eACA,GAAG,UAAS,GACG;AACf,QAAM,EAAE,SAAS,QAAQ,MAAK,IAAK,gBAAe;AAClD,QAAM,YAAY,SAAS;IACzB,UAAU;MACR;MACA,MAAM;MACN;MACA;QACE,UACE,OAAO,iBAAiB,WACpB,eACA,OAAO,iBAAiB,aACtB,cAAc,YAAY,IAC1B;;;IAGZ,SAAS,YAAW;AAClB,UAAI,OAAO,iBAAiB,UAAU;AACpC,eAAO;MACT;AACA,UAAI,OAAO,iBAAiB,YAAY;AACtC,eAAO,aAAY;MACrB;AACA,UAAI,QAAQ,YAAW,MAAO,qBAAqB,YAAW,GAAI;AAChE,cAAM,cAAc,MAAM,iBAAiB,KAAK,EAAE,KAChD,CAAC,SAAM;AA9IjB;AA8IoB,4BAAK,SAAL,mBAAW;SAAG;AAE1B,YAAI,CAAC,aAAa;AAChB,gBAAM,IAAI,MAAM,yCAAyC;QAC3D;AACA,eAAO,cAAc,EAAE,KAAK,aAAa,OAAM,CAAE;MACnD;AAGA,YAAM,mBAAmB,MAAM,oBAAoB;QACjD,UAAU,YAAY;UACpB;UACA;UACA;SACD;OACF;AAED,UACE,CAAC,iBAAiB,SAClB,OAAO,iBAAiB,UAAU,UAClC;AACA,cAAM,IAAI,MAAM,qDAAqD;MACvE;AAEA,aAAO,cAAc;QACnB,KAAK,iBAAiB;QACtB;OACD;IACH;IACA,GAAGA;GACJ;AAED,MAAI,UAAU,WAAW;AACvB,WAAO,oBAAoB;EAC7B;AAEA,MAAI,CAAC,UAAU,MAAM;AACnB,WAAO,qBAAqB;EAC9B;AAEA,aAAO,qBAAAC,KAAA,OAAA,EAAK,KAAK,UAAU,MAAI,GAAM,WAAW,KAAK,UAAU,IAAG,CAAA;AACpE;;;ACnKA,eAAsBC,uBAAmB;AACvC,SAAO,oBAAwB,eAAe;AAChD;;;;AC4DM,SAAU,WAAW,EACzB,kBACA,mBACA,cAAAC,eACA,GAAG,UAAS,GACI;AAChB,QAAM,aAAa,cAAc,EAAE,cAAAA,cAAY,CAAE;AACjD,MAAI,WAAW,WAAW;AACxB,WAAO,oBAAoB;EAC7B;AACA,MAAI,CAAC,WAAW,MAAM;AACpB,WAAO,qBAAqB;EAC9B;AACA,aAAO,qBAAAC,KAAA,OAAA,EAAK,KAAK,WAAW,MAAI,GAAM,WAAW,KAAK,UAAU,IAAG,CAAA;AACrE;AA2BM,SAAU,WAAW,EAAE,UAAU,GAAG,UAAS,GAAmB;AACpE,QAAM,MAAM,cAAc,QAAQ;AAClC,aAAO,qBAAAA,KAAA,OAAA,EAAK,KAAQ,GAAM,WAAW,KAAK,UAAU,IAAG,CAAA;AACzD;;;;;;ACpHM,SAAU,cAAc,OAG7B;AACC,QAAM,EAAE,GAAE,IAAK,iBAAgB;AAC/B,QAAM,YAAY,SAAS;IACzB,UAAUC,cAAa,EAAE,IAAI,UAAU,MAAM,SAAQ,CAAE;IACvD,SAAS,YAAY,gBAAgB,EAAE,IAAI,UAAU,MAAM,SAAQ,CAAE;IACrE,GAAG,MAAM;GACV;AACD,SAAO;AACT;AAKM,SAAUA,cAAa,OAG5B;AACC,MAAI,OAAO,MAAM,aAAa,YAAY;AACxC,WAAO;MACL;MACA,MAAM;MACN,EAAE,UAAU,cAAc,MAAM,QAAQ,EAAC;;EAE7C;AACA,SAAO,CAAC,cAAc,MAAM,EAAE;AAChC;AAKA,eAAsB,gBAAgB,OAGrC;AACC,QAAM,OAAO,MAAM,cAAc,MAAM,EAAE;AACzC,MAAI,OAAO,MAAM,aAAa,YAAY;AACxC,WAAO,MAAM,SAAS,KAAK,IAAI;EACjC;AACA,SAAO,KAAK;AACd;;;AC8CM,SAAU,WAAW,EACzB,kBACA,mBACA,cAAAC,eACA,UACA,GAAG,UAAS,GACI;AAChB,QAAM,YAAY,cAAc,EAAE,cAAAA,eAAc,SAAQ,CAAE;AAC1D,MAAI,UAAU,WAAW;AACvB,WAAO,oBAAoB;EAC7B;AACA,MAAI,CAAC,UAAU,MAAM;AACnB,WAAO,qBAAqB;EAC9B;AACA,aAAO,qBAAAC,KAAA,QAAA,EAAA,GAAU,WAAS,UAAG,UAAU,KAAI,CAAA;AAC7C;", "names": ["import_react", "_jsx", "_jsxs", "_Fragment", "import_react", "import_react", "_jsx", "_jsxs", "_Fragment", "import_react", "_jsxs", "_jsx", "_Fragment", "_jsx", "_jsx", "sendTransaction", "_jsx", "_jsxs", "import_react", "import_react", "_jsx", "_jsxs", "_jsx", "chainId", "statusCode", "_a", "call", "call", "queryOptions", "import_react", "import_react", "queryOptions", "queryOptions", "import_react", "_a", "_b", "_jsx", "_jsxs", "_Fragment", "import_react", "_jsx", "Modal", "_jsx", "getNFT", "import_react", "getPayMetadata", "_jsx", "import_react", "createListing", "getPayMetadata", "createListing", "_jsx", "getContractMetadata", "import_react", "_jsx", "getNFT", "queryOptions", "_jsx", "queryOptions", "_jsx", "queryOptions", "get<PERSON><PERSON>y<PERSON>ey", "_jsx", "authProvider", "auth<PERSON><PERSON><PERSON>", "_jsx", "authProvider", "auth<PERSON><PERSON><PERSON>", "_jsx", "import_react", "_jsx", "queryOptions", "_jsx", "TokenSymbol", "queryOptions", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsx", "queryOptions", "_jsx", "getLastAuthProvider", "queryOptions", "_jsx", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "queryOptions", "_jsx"]}