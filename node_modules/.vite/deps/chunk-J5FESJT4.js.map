{"version": 3, "sources": ["../../thirdweb/src/extensions/erc20/write/approve.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/read/getPermissionsForSigner.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IAccountPermissions/write/setPermissionsForSigner.ts", "../../thirdweb/src/utils/date.ts", "../../thirdweb/src/extensions/erc4337/account/types.ts", "../../thirdweb/src/extensions/erc4337/account/common.ts", "../../thirdweb/src/extensions/erc4337/account/addSessionKey.ts", "../../thirdweb/src/utils/types.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IEntryPoint/events/UserOperationRevertReason.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IEntryPoint_v07/events/PostOpRevertReason.ts", "../../thirdweb/src/wallets/smart/types.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IEntryPoint/read/getNonce.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IEntryPoint/read/getUserOpHash.ts", "../../thirdweb/src/extensions/erc4337/__generated__/IEntryPoint_v07/read/getUserOpHash.ts", "../../thirdweb/src/wallets/smart/lib/packUserOp.ts", "../../thirdweb/src/wallets/smart/lib/utils.ts", "../../thirdweb/src/wallets/smart/lib/paymaster.ts", "../../thirdweb/src/wallets/smart/lib/userop.ts", "../../thirdweb/src/wallets/smart/lib/bundler.ts", "../../thirdweb/src/wallets/smart/is-smart-wallet.ts", "../../thirdweb/src/wallets/smart/index.ts"], "sourcesContent": ["import type { Address } from \"abitype\";\nimport type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport type { Prettify } from \"../../../utils/type-utils.js\";\nimport { toUnits } from \"../../../utils/units.js\";\nimport { approve as generatedApprove } from \"../__generated__/IERC20/write/approve.js\";\n\n/**\n * Represents the parameters for the `approve` function.\n * @extension ERC20\n */\nexport type ApproveParams = Prettify<\n  { spender: Address } & (\n    | {\n        amount: number | string;\n      }\n    | {\n        amountWei: bigint;\n      }\n  )\n>;\n\n/**\n * Approves the spending of tokens by a specific address.\n * @param options - The transaction options.\n * @returns A prepared transaction object.\n * @extension ERC20\n * @example\n * ```ts\n * import { approve } from \"thirdweb/extensions/erc20\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = await approve({\n *  contract,\n *  spender: \"0x...\",\n *  amount: 100,\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function approve(options: BaseTransactionOptions<ApproveParams>) {\n  return generatedApprove({\n    contract: options.contract,\n    asyncParams: async () => {\n      let amount: bigint;\n      if (\"amount\" in options) {\n        // if we need to parse the amount from ether to gwei then we pull in the decimals extension\n        const { decimals } = await import(\"../read/decimals.js\");\n        // if this fails we fall back to `18` decimals\n        const d = await decimals(options).catch(() => 18);\n        // turn ether into gwei\n        amount = toUnits(options.amount.toString(), d);\n      } else {\n        amount = options.amountWei;\n      }\n      return {\n        spender: options.spender,\n        value: amount,\n        overrides: {\n          erc20Value: {\n            amountWei: amount,\n            tokenAddress: options.contract.address,\n          },\n        },\n      } as const;\n    },\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"getPermissionsForSigner\" function.\n */\nexport type GetPermissionsForSignerParams = {\n  signer: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"signer\" }>;\n};\n\nexport const FN_SELECTOR = \"0xf15d424e\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"signer\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"tuple\",\n    name: \"permissions\",\n    components: [\n      {\n        type: \"address\",\n        name: \"signer\",\n      },\n      {\n        type: \"address[]\",\n        name: \"approvedTargets\",\n      },\n      {\n        type: \"uint256\",\n        name: \"nativeTokenLimitPerTransaction\",\n      },\n      {\n        type: \"uint128\",\n        name: \"startTimestamp\",\n      },\n      {\n        type: \"uint128\",\n        name: \"endTimestamp\",\n      },\n    ],\n  },\n] as const;\n\n/**\n * Checks if the `getPermissionsForSigner` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getPermissionsForSigner` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetPermissionsForSignerSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetPermissionsForSignerSupported([\"0x...\"]);\n * ```\n */\nexport function isGetPermissionsForSignerSupported(\n  availableSelectors: string[],\n) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"getPermissionsForSigner\" function.\n * @param options - The options for the getPermissionsForSigner function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetPermissionsForSignerParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetPermissionsForSignerParams({\n *  signer: ...,\n * });\n * ```\n */\nexport function encodeGetPermissionsForSignerParams(\n  options: GetPermissionsForSignerParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [options.signer]);\n}\n\n/**\n * Encodes the \"getPermissionsForSigner\" function into a Hex string with its parameters.\n * @param options - The options for the getPermissionsForSigner function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetPermissionsForSigner } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetPermissionsForSigner({\n *  signer: ...,\n * });\n * ```\n */\nexport function encodeGetPermissionsForSigner(\n  options: GetPermissionsForSignerParams,\n) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeGetPermissionsForSignerParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the getPermissionsForSigner function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetPermissionsForSignerResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetPermissionsForSignerResultResult(\"...\");\n * ```\n */\nexport function decodeGetPermissionsForSignerResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getPermissionsForSigner\" function on the contract.\n * @param options - The options for the getPermissionsForSigner function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getPermissionsForSigner } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getPermissionsForSigner({\n *  contract,\n *  signer: ...,\n * });\n *\n * ```\n */\nexport async function getPermissionsForSigner(\n  options: BaseTransactionOptions<GetPermissionsForSignerParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.signer],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"setPermissionsForSigner\" function.\n */\nexport type SetPermissionsForSignerParams = WithOverrides<{\n  req: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"req\";\n    components: [\n      { type: \"address\"; name: \"signer\" },\n      { type: \"uint8\"; name: \"isAdmin\" },\n      { type: \"address[]\"; name: \"approvedTargets\" },\n      { type: \"uint256\"; name: \"nativeTokenLimitPerTransaction\" },\n      { type: \"uint128\"; name: \"permissionStartTimestamp\" },\n      { type: \"uint128\"; name: \"permissionEndTimestamp\" },\n      { type: \"uint128\"; name: \"reqValidityStartTimestamp\" },\n      { type: \"uint128\"; name: \"reqValidityEndTimestamp\" },\n      { type: \"bytes32\"; name: \"uid\" },\n    ];\n  }>;\n  signature: AbiParameterToPrimitiveType<{ type: \"bytes\"; name: \"signature\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0x5892e236\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"tuple\",\n    name: \"req\",\n    components: [\n      {\n        type: \"address\",\n        name: \"signer\",\n      },\n      {\n        type: \"uint8\",\n        name: \"isAdmin\",\n      },\n      {\n        type: \"address[]\",\n        name: \"approvedTargets\",\n      },\n      {\n        type: \"uint256\",\n        name: \"nativeTokenLimitPerTransaction\",\n      },\n      {\n        type: \"uint128\",\n        name: \"permissionStartTimestamp\",\n      },\n      {\n        type: \"uint128\",\n        name: \"permissionEndTimestamp\",\n      },\n      {\n        type: \"uint128\",\n        name: \"reqValidityStartTimestamp\",\n      },\n      {\n        type: \"uint128\",\n        name: \"reqValidityEndTimestamp\",\n      },\n      {\n        type: \"bytes32\",\n        name: \"uid\",\n      },\n    ],\n  },\n  {\n    type: \"bytes\",\n    name: \"signature\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `setPermissionsForSigner` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `setPermissionsForSigner` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isSetPermissionsForSignerSupported } from \"thirdweb/extensions/erc4337\";\n *\n * const supported = isSetPermissionsForSignerSupported([\"0x...\"]);\n * ```\n */\nexport function isSetPermissionsForSignerSupported(\n  availableSelectors: string[],\n) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"setPermissionsForSigner\" function.\n * @param options - The options for the setPermissionsForSigner function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeSetPermissionsForSignerParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeSetPermissionsForSignerParams({\n *  req: ...,\n *  signature: ...,\n * });\n * ```\n */\nexport function encodeSetPermissionsForSignerParams(\n  options: SetPermissionsForSignerParams,\n) {\n  return encodeAbiParameters(FN_INPUTS, [options.req, options.signature]);\n}\n\n/**\n * Encodes the \"setPermissionsForSigner\" function into a Hex string with its parameters.\n * @param options - The options for the setPermissionsForSigner function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeSetPermissionsForSigner } from \"thirdweb/extensions/erc4337\";\n * const result = encodeSetPermissionsForSigner({\n *  req: ...,\n *  signature: ...,\n * });\n * ```\n */\nexport function encodeSetPermissionsForSigner(\n  options: SetPermissionsForSignerParams,\n) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeSetPermissionsForSignerParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"setPermissionsForSigner\" function on the contract.\n * @param options - The options for the \"setPermissionsForSigner\" function.\n * @returns A prepared transaction object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { setPermissionsForSigner } from \"thirdweb/extensions/erc4337\";\n *\n * const transaction = setPermissionsForSigner({\n *  contract,\n *  req: ...,\n *  signature: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function setPermissionsForSigner(\n  options: BaseTransactionOptions<\n    | SetPermissionsForSignerParams\n    | {\n        asyncParams: () => Promise<SetPermissionsForSignerParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [resolvedOptions.req, resolvedOptions.signature] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import { toBigInt } from \"./bigint.js\";\n\n/**\n * @internal\n */\nexport function tenYearsFromNow() {\n  return new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10); // 10 years\n}\n\n/**\n * @internal\n */\nexport function dateToSeconds(date: Date) {\n  return toBigInt(Math.floor(date.getTime() / 1000));\n}\n", "export type AccountPermissions = {\n  approvedTargets: string[] | \"*\";\n  nativeTokenLimitPerTransaction?: number | string;\n  permissionStartTimestamp?: Date;\n  permissionEndTimestamp?: Date;\n  reqValidityStartTimestamp?: Date;\n  reqValidityEndTimestamp?: Date;\n};\n\nexport const SignerPermissionRequest = [\n  { name: \"signer\", type: \"address\" },\n  { name: \"isAdmin\", type: \"uint8\" },\n  { name: \"approvedTargets\", type: \"address[]\" },\n  { name: \"nativeTokenLimitPerTransaction\", type: \"uint256\" },\n  { name: \"permissionStartTimestamp\", type: \"uint128\" },\n  { name: \"permissionEndTimestamp\", type: \"uint128\" },\n  { name: \"reqValidityStartTimestamp\", type: \"uint128\" },\n  { name: \"reqValidityEndTimestamp\", type: \"uint128\" },\n  { name: \"uid\", type: \"bytes32\" },\n];\n", "import { ZERO_ADDRESS } from \"../../../constants/addresses.js\";\nimport type { ThirdwebContract } from \"../../../contract/contract.js\";\nimport { dateToSeconds, tenYearsFromNow } from \"../../../utils/date.js\";\nimport { randomBytesHex } from \"../../../utils/random.js\";\nimport { toWei } from \"../../../utils/units.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport type { SetPermissionsForSignerParams } from \"../__generated__/IAccountPermissions/write/setPermissionsForSigner.js\";\nimport { type AccountPermissions, SignerPermissionRequest } from \"./types.js\";\n\n/**\n * @internal\n */\nexport async function signPermissionRequest(options: {\n  account: Account;\n  contract: ThirdwebContract;\n  req: SetPermissionsForSignerParams[\"req\"];\n}) {\n  const { account, contract, req } = options;\n  const signature = await account.signTypedData({\n    domain: {\n      name: \"Account\",\n      version: \"1\",\n      verifyingContract: contract.address,\n      chainId: contract.chain.id,\n    },\n    primaryType: \"SignerPermissionRequest\",\n    types: { SignerPermissionRequest },\n    message: req,\n  });\n  return { req, signature };\n}\n\n/**\n * @internal\n */\nexport async function toContractPermissions(options: {\n  target: string;\n  permissions: AccountPermissions;\n}): Promise<SetPermissionsForSignerParams[\"req\"]> {\n  const { target, permissions } = options;\n  return {\n    approvedTargets:\n      permissions.approvedTargets === \"*\"\n        ? [ZERO_ADDRESS]\n        : permissions.approvedTargets,\n    nativeTokenLimitPerTransaction: toWei(\n      permissions.nativeTokenLimitPerTransaction?.toString() || \"0\",\n    ),\n    permissionStartTimestamp: dateToSeconds(\n      permissions.permissionStartTimestamp || new Date(0),\n    ),\n    permissionEndTimestamp: dateToSeconds(\n      permissions.permissionEndTimestamp || tenYearsFromNow(),\n    ),\n    reqValidityStartTimestamp: 0n,\n    reqValidityEndTimestamp: dateToSeconds(tenYearsFromNow()),\n    uid: await randomBytesHex(),\n    isAdmin: 0, // session key flag\n    signer: target,\n  };\n}\n\n/**\n * @internal\n */\nexport async function defaultPermissionsForAdmin(options: {\n  target: string;\n  action: \"add-admin\" | \"remove-admin\";\n}): Promise<SetPermissionsForSignerParams[\"req\"]> {\n  const { target, action } = options;\n  return {\n    approvedTargets: [],\n    nativeTokenLimitPerTransaction: 0n,\n    permissionStartTimestamp: 0n,\n    permissionEndTimestamp: 0n,\n    reqValidityStartTimestamp: 0n,\n    reqValidityEndTimestamp: dateToSeconds(tenYearsFromNow()),\n    uid: await randomBytesHex(),\n    isAdmin: action === \"add-admin\" ? 1 : action === \"remove-admin\" ? 2 : 0,\n    signer: target,\n  };\n}\n", "import { ZERO_ADDRESS } from \"../../../constants/addresses.js\";\nimport type { ThirdwebContract } from \"../../../contract/contract.js\";\nimport type { BaseTransactionOptions } from \"../../../transaction/types.js\";\nimport { isContractDeployed } from \"../../../utils/bytecode/is-contract-deployed.js\";\nimport { toWei } from \"../../../utils/units.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport { getPermissionsForSigner } from \"../__generated__/IAccountPermissions/read/getPermissionsForSigner.js\";\nimport {\n  isSetPermissionsForSignerSupported,\n  setPermissionsForSigner,\n} from \"../__generated__/IAccountPermissions/write/setPermissionsForSigner.js\";\nimport { signPermissionRequest, toContractPermissions } from \"./common.js\";\nimport type { AccountPermissions } from \"./types.js\";\n\n/**\n * @extension ERC4337\n */\nexport type AddSessionKeyOptions = {\n  /**\n   * The admin account that will perform the operation.\n   */\n  account: Account;\n  /**\n   * The address to add as a session key.\n   */\n  sessionKeyAddress: string;\n  /**\n   * The permissions to assign to the session key.\n   */\n  permissions: AccountPermissions;\n};\n\n/**\n * Adds session key permissions for a specified address.\n * @param options - The options for the removeSessionKey function.\n * @param {Contract} options.contract - The smart account contract to add the session key to\n * @returns The transaction object to be sent.\n * @example\n * ```ts\n * import { addSessionKey } from 'thirdweb/extensions/erc4337';\n * import { sendTransaction } from 'thirdweb';\n *\n * const transaction = addSessionKey({\n * contract,\n * account,\n * sessionKeyAddress,\n * permissions: {\n *  approvedTargets: ['0x...'],\n *  nativeTokenLimitPerTransaction: 0.1, // in ETH\n *  permissionStartTimestamp: new Date(),\n *  permissionEndTimestamp: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365), // 1 year from now\n * }\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n * @extension ERC4337\n */\nexport function addSessionKey(\n  options: BaseTransactionOptions<AddSessionKeyOptions>,\n) {\n  const { contract, sessionKeyAddress, account, permissions } = options;\n  return setPermissionsForSigner({\n    contract,\n    async asyncParams() {\n      const { req, signature } = await signPermissionRequest({\n        account,\n        contract,\n        req: await toContractPermissions({\n          target: sessionKeyAddress,\n          permissions,\n        }),\n      });\n      return { signature, req };\n    },\n  });\n}\n\n/**\n * Checks if the `isAddSessionKeySupported` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isAddSessionKeySupported` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isAddSessionKeySupported } from \"thirdweb/extensions/erc4337\";\n *\n * const supported = isAddSessionKeySupported([\"0x...\"]);\n * ```\n */\nexport function isAddSessionKeySupported(availableSelectors: string[]) {\n  return isSetPermissionsForSignerSupported(availableSelectors);\n}\n\n/**\n * Checks if the session key should be updated.\n * @param currentPermissions - The current permissions of the session key.\n * @param newPermissions - The new permissions to set for the session key.\n * @returns A boolean indicating if the session key should be updated.\n * @extension ERC4337\n * @example\n * ```ts\n * import { shouldUpdateSessionKey } from \"thirdweb/extensions/erc4337\";\n *\n * const shouldUpdate = await shouldUpdateSessionKey({ accountContract, sessionKeyAddress, newPermissions });\n * ```\n */\nexport async function shouldUpdateSessionKey(args: {\n  accountContract: ThirdwebContract;\n  sessionKeyAddress: string;\n  newPermissions: AccountPermissions;\n}): Promise<boolean> {\n  const { accountContract, sessionKeyAddress, newPermissions } = args;\n\n  // check if account is deployed\n  const accountDeployed = await isContractDeployed(accountContract);\n  if (!accountDeployed) {\n    return true;\n  }\n\n  // get current permissions\n  const currentPermissions = await getPermissionsForSigner({\n    contract: accountContract,\n    signer: sessionKeyAddress,\n  });\n  // check end time validity\n  if (\n    currentPermissions.endTimestamp &&\n    currentPermissions.endTimestamp < Math.floor(new Date().getTime() / 1000)\n  ) {\n    return true;\n  }\n\n  // check targets\n  if (\n    !areSessionKeyContractTargetsEqual(\n      currentPermissions.approvedTargets,\n      newPermissions.approvedTargets,\n    )\n  ) {\n    return true;\n  }\n\n  // check if the new native token limit is greater than the current one\n  if (\n    toWei(newPermissions.nativeTokenLimitPerTransaction?.toString() ?? \"0\") >\n    currentPermissions.nativeTokenLimitPerTransaction\n  ) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction areSessionKeyContractTargetsEqual(\n  currentTargets: readonly string[],\n  newTargets: string[] | \"*\",\n): boolean {\n  // Handle the case where approvedTargets is \"*\"\n  if (\n    newTargets === \"*\" &&\n    currentTargets.length === 1 &&\n    currentTargets[0] === ZERO_ADDRESS\n  ) {\n    return true;\n  }\n  if (newTargets !== \"*\") {\n    return newTargets\n      .map((target) => target.toLowerCase())\n      .every((target) =>\n        currentTargets.map((t) => t.toLowerCase()).includes(target),\n      );\n  }\n  return false;\n}\n", "import type * as ox__Bytes from \"ox/Bytes\";\nimport type * as ox__Hex from \"ox/Hex\";\nimport type { Chain } from \"../chains/types.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport type { Account } from \"../wallets/interfaces/wallet.js\";\nimport type { Prettify } from \"./type-utils.js\";\n\n/**\n * @internal\n */\nexport type ClientAndChain = {\n  client: ThirdwebClient;\n  chain: Chain;\n};\n\n/**\n * @internal\n */\nexport type ClientAndChainAndAccount = Prettify<\n  ClientAndChain & { account: Account }\n>;\n\n/**\n * A message that can be signed, either as in plaintext or as a raw hex string.\n */\nexport type SignableMessage =\n  | string\n  | {\n      /** Raw data representation of the message. */\n      raw: ox__Hex.Hex | ox__Bytes.Bytes;\n    };\n\n/**\n * @internal\n */\nexport const maxUint96 = 2n ** 96n - 1n;\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"UserOperationRevertReason\" event.\n */\nexport type UserOperationRevertReasonEventFilters = Partial<{\n  userOpHash: AbiParameterToPrimitiveType<{\n    type: \"bytes32\";\n    name: \"userOpHash\";\n    indexed: true;\n  }>;\n  sender: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"sender\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the UserOperationRevertReason event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { userOperationRevertReasonEvent } from \"thirdweb/extensions/erc4337\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  userOperationRevertReasonEvent({\n *  userOpHash: ...,\n *  sender: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function userOperationRevertReasonEvent(\n  filters: UserOperationRevertReasonEventFilters = {},\n) {\n  return prepareEvent({\n    signature:\n      \"event UserOperationRevertReason(bytes32 indexed userOpHash, address indexed sender, uint256 nonce, bytes revertReason)\",\n    filters,\n  });\n}\n", "import { prepareEvent } from \"../../../../../event/prepare-event.js\";\nimport type { AbiParameterToPrimitiveType } from \"abitype\";\n\n/**\n * Represents the filters for the \"PostOpRevertReason\" event.\n */\nexport type PostOpRevertReasonEventFilters = Partial<{\n  userOpHash: AbiParameterToPrimitiveType<{\n    type: \"bytes32\";\n    name: \"userOpHash\";\n    indexed: true;\n  }>;\n  sender: AbiParameterToPrimitiveType<{\n    type: \"address\";\n    name: \"sender\";\n    indexed: true;\n  }>;\n}>;\n\n/**\n * Creates an event object for the PostOpRevertReason event.\n * @param filters - Optional filters to apply to the event.\n * @returns The prepared event object.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getContractEvents } from \"thirdweb\";\n * import { postOpRevertReasonEvent } from \"thirdweb/extensions/erc4337\";\n *\n * const events = await getContractEvents({\n * contract,\n * events: [\n *  postOpRevertReasonEvent({\n *  userOpHash: ...,\n *  sender: ...,\n * })\n * ],\n * });\n * ```\n */\nexport function postOpRevertReasonEvent(\n  filters: PostOpRevertReasonEventFilters = {},\n) {\n  return prepareEvent({\n    signature:\n      \"event PostOpRevertReason(bytes32 indexed userOpHash, address indexed sender, uint256 nonce, bytes revertReason)\",\n    filters,\n  });\n}\n", "import type * as ox__Address from \"ox/Address\";\nimport type * as ox__TypedData from \"ox/TypedData\";\nimport type { Chain } from \"../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport type { ThirdwebContract } from \"../../contract/contract.js\";\nimport type { AccountPermissions } from \"../../extensions/erc4337/account/types.js\";\nimport type { PreparedTransaction } from \"../../transaction/prepare-transaction.js\";\nimport type { TransactionReceipt } from \"../../transaction/types.js\";\nimport type { Hex } from \"../../utils/encoding/hex.js\";\nimport type { Prettify } from \"../../utils/type-utils.js\";\nimport type { SignableMessage } from \"../../utils/types.js\";\nimport type { Account, SendTransactionOption } from \"../interfaces/wallet.js\";\n\nexport type TokenPaymasterConfig = {\n  chainId: number;\n  paymasterAddress: string;\n  tokenAddress: string;\n  balanceStorageSlot: bigint;\n};\n\nexport type SmartWalletOptions = Prettify<\n  {\n    chain: Chain; // TODO consider making default chain optional\n    factoryAddress?: string;\n    sessionKey?: {\n      address: string;\n      permissions: AccountPermissions;\n    };\n    overrides?: {\n      bundlerUrl?: string;\n      accountAddress?: string;\n      accountSalt?: string;\n      entrypointAddress?: string;\n      tokenPaymaster?: TokenPaymasterConfig;\n      paymaster?: (\n        userOp: UserOperationV06 | UserOperationV07,\n      ) => Promise<PaymasterResult>;\n      predictAddress?: (\n        factoryContract: ThirdwebContract,\n        admin: string,\n      ) => Promise<string>;\n      createAccount?: (\n        factoryContract: ThirdwebContract,\n        admin: string,\n      ) => PreparedTransaction;\n      execute?: (\n        accountContract: ThirdwebContract,\n        transaction: SendTransactionOption,\n      ) => PreparedTransaction;\n      executeBatch?: (\n        accountContract: ThirdwebContract,\n        transactions: SendTransactionOption[],\n      ) => PreparedTransaction;\n      getAccountNonce?: (accountContract: ThirdwebContract) => Promise<bigint>;\n      signMessage?: (options: {\n        adminAccount: Account;\n        accountContract: ThirdwebContract;\n        factoryContract: ThirdwebContract;\n        message: SignableMessage;\n      }) => Promise<Hex>;\n      signTypedData?: <\n        const typedData extends\n          | ox__TypedData.TypedData\n          | Record<string, unknown>,\n        primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n      >(options: {\n        adminAccount: Account;\n        accountContract: ThirdwebContract;\n        factoryContract: ThirdwebContract;\n        typedData: ox__TypedData.Definition<typedData, primaryType>;\n      }) => Promise<Hex>;\n    };\n  } & (\n    | {\n        /**\n         * @deprecated use 'sponsorGas' instead\n         */\n        gasless: boolean;\n      }\n    | {\n        sponsorGas: boolean;\n      }\n  )\n>;\n\n// internal type\nexport type SmartAccountOptions = Prettify<\n  Omit<SmartWalletOptions, \"chain\" | \"gasless\" | \"sponsorGas\"> & {\n    chain: Chain;\n    sponsorGas: boolean;\n    personalAccount: Account;\n    factoryContract: ThirdwebContract;\n    accountContract: ThirdwebContract;\n    client: ThirdwebClient;\n  }\n>;\n\nexport type BundlerOptions = {\n  bundlerUrl?: string;\n  entrypointAddress?: string;\n  chain: Chain;\n  client: ThirdwebClient;\n};\n\nexport type SmartWalletConnectionOptions = {\n  personalAccount: Account;\n  client: ThirdwebClient;\n  chain?: Chain;\n};\n\nexport type UserOperationV06 = {\n  sender: ox__Address.Address;\n  nonce: bigint;\n  initCode: Hex;\n  callData: Hex;\n  callGasLimit: bigint;\n  verificationGasLimit: bigint;\n  preVerificationGas: bigint;\n  maxFeePerGas: bigint;\n  maxPriorityFeePerGas: bigint;\n  paymasterAndData: Hex;\n  signature: Hex;\n};\n\nexport type UserOperationV07 = {\n  sender: string; // address\n  nonce: bigint; // uint256\n  factory: string | undefined; // address\n  factoryData: Hex; // bytes\n  callData: Hex; // bytes\n  callGasLimit: bigint; // uint256\n  verificationGasLimit: bigint; // uint256\n  preVerificationGas: bigint; // uint256\n  maxFeePerGas: bigint; // uint256\n  maxPriorityFeePerGas: bigint; // uint256\n  paymaster: string | undefined; // address\n  paymasterData: Hex; // bytes\n  paymasterVerificationGasLimit: bigint; // uint256\n  paymasterPostOpGasLimit: bigint; // uint256\n  signature: Hex; // bytes\n};\n\nexport type PackedUserOperation = {\n  sender: string; // address\n  nonce: bigint; // uint256\n  initCode: Hex; // bytes\n  callData: Hex; // bytes\n  accountGasLimits: Hex; // bytes32\n  preVerificationGas: bigint; // uint256\n  gasFees: Hex; // bytes32\n  paymasterAndData: Hex; // bytes\n  signature: Hex; // bytes\n};\n\nexport type UserOperationV06Hexed = {\n  sender: ox__Address.Address;\n  nonce: Hex;\n  initCode: Hex;\n  callData: Hex;\n  callGasLimit: Hex;\n  verificationGasLimit: Hex;\n  preVerificationGas: Hex;\n  maxFeePerGas: Hex;\n  maxPriorityFeePerGas: Hex;\n  paymasterAndData: Hex;\n  signature: Hex;\n};\n\nexport type UserOperationV07Hexed = {\n  sender: Hex;\n  nonce: Hex;\n  factory: Hex;\n  factoryData: Hex;\n  callData: Hex;\n  callGasLimit: Hex;\n  verificationGasLimit: Hex;\n  preVerificationGas: Hex;\n  maxFeePerGas: Hex;\n  maxPriorityFeePerGas: Hex;\n  paymaster: Hex;\n  paymasterVerificationGasLimit: Hex;\n  paymasterPostOpGasLimit: Hex;\n  paymasterData: Hex;\n  signature: Hex;\n};\n\nexport type PaymasterResult = {\n  preVerificationGas?: bigint;\n  verificationGasLimit?: bigint;\n  callGasLimit?: bigint;\n} & (\n  | {\n      // v0.6 types\n      paymasterAndData: string;\n    }\n  | {\n      // v0.7 types\n      paymaster: string;\n      paymasterData: string;\n      paymasterVerificationGasLimit?: bigint;\n      paymasterPostOpGasLimit?: bigint;\n    }\n);\n\nexport type EstimationResult = {\n  preVerificationGas: bigint;\n  verificationGas?: bigint;\n  verificationGasLimit: bigint;\n  callGasLimit: bigint;\n  // v0.7 types\n  paymasterVerificationGasLimit?: bigint;\n  paymasterPostOpGasLimit?: bigint;\n};\n\nexport type GasPriceResult = {\n  maxFeePerGas: bigint;\n  maxPriorityFeePerGas: bigint;\n};\n\nexport type PmTransactionData = {\n  paymaster: ox__Address.Address;\n  paymasterInput: Hex;\n};\n\nexport type UserOperationReceipt = {\n  receipt: TransactionReceipt;\n  logs: TransactionReceipt[\"logs\"];\n  userOpHash: Hex;\n  entryPoint: ox__Address.Address;\n  sender: ox__Address.Address;\n  nonce: bigint;\n  paymaster: ox__Address.Address;\n  actualGasUsed: bigint;\n  actualGasCost: bigint;\n  success: boolean;\n};\n\nexport function formatUserOperationReceipt(\n  userOpReceiptRaw: UserOperationReceipt,\n) {\n  const { receipt: transactionReceipt } = userOpReceiptRaw;\n\n  const receipt = {\n    ...transactionReceipt,\n    transactionHash: transactionReceipt.transactionHash,\n    blockNumber: transactionReceipt.blockNumber\n      ? BigInt(transactionReceipt.blockNumber)\n      : null,\n    contractAddress: transactionReceipt.contractAddress\n      ? transactionReceipt.contractAddress\n      : null,\n    cumulativeGasUsed: transactionReceipt.cumulativeGasUsed\n      ? BigInt(transactionReceipt.cumulativeGasUsed)\n      : null,\n    effectiveGasPrice: transactionReceipt.effectiveGasPrice\n      ? BigInt(transactionReceipt.effectiveGasPrice)\n      : null,\n    gasUsed: transactionReceipt.gasUsed\n      ? BigInt(transactionReceipt.gasUsed)\n      : null,\n    logs: transactionReceipt.logs,\n    to: transactionReceipt.to ? transactionReceipt.to : null,\n    transactionIndex: transactionReceipt.transactionIndex,\n    status: transactionReceipt.status,\n    type: transactionReceipt.type,\n  } as TransactionReceipt;\n\n  if (transactionReceipt.blobGasPrice)\n    receipt.blobGasPrice = BigInt(transactionReceipt.blobGasPrice);\n  if (transactionReceipt.blobGasUsed)\n    receipt.blobGasUsed = BigInt(transactionReceipt.blobGasUsed);\n\n  const userOpReceipt = {\n    ...userOpReceiptRaw,\n    receipt,\n    userOpHash: userOpReceiptRaw.userOpHash,\n    actualGasCost: BigInt(userOpReceiptRaw.actualGasCost),\n    actualGasUsed: BigInt(userOpReceiptRaw.actualGasUsed),\n    nonce: BigInt(userOpReceiptRaw.nonce),\n  } as UserOperationReceipt;\n  return userOpReceipt;\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"getNonce\" function.\n */\nexport type GetNonceParams = {\n  sender: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"sender\" }>;\n  key: AbiParameterToPrimitiveType<{ type: \"uint192\"; name: \"key\" }>;\n};\n\nexport const FN_SELECTOR = \"0x35567e1a\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"sender\",\n  },\n  {\n    type: \"uint192\",\n    name: \"key\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"uint256\",\n    name: \"nonce\",\n  },\n] as const;\n\n/**\n * Checks if the `getNonce` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getNonce` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetNonceSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetNonceSupported([\"0x...\"]);\n * ```\n */\nexport function isGetNonceSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"getNonce\" function.\n * @param options - The options for the getNonce function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetNonceParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetNonceParams({\n *  sender: ...,\n *  key: ...,\n * });\n * ```\n */\nexport function encodeGetNonceParams(options: GetNonceParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.sender, options.key]);\n}\n\n/**\n * Encodes the \"getNonce\" function into a Hex string with its parameters.\n * @param options - The options for the getNonce function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetNonce } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetNonce({\n *  sender: ...,\n *  key: ...,\n * });\n * ```\n */\nexport function encodeGetNonce(options: GetNonceParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeGetNonceParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the getNonce function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetNonceResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetNonceResultResult(\"...\");\n * ```\n */\nexport function decodeGetNonceResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getNonce\" function on the contract.\n * @param options - The options for the getNonce function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getNonce } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getNonce({\n *  contract,\n *  sender: ...,\n *  key: ...,\n * });\n *\n * ```\n */\nexport async function getNonce(\n  options: BaseTransactionOptions<GetNonceParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.sender, options.key],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"getUserOpHash\" function.\n */\nexport type GetUserOpHashParams = {\n  userOp: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"userOp\";\n    components: [\n      { type: \"address\"; name: \"sender\" },\n      { type: \"uint256\"; name: \"nonce\" },\n      { type: \"bytes\"; name: \"initCode\" },\n      { type: \"bytes\"; name: \"callData\" },\n      { type: \"uint256\"; name: \"callGasLimit\" },\n      { type: \"uint256\"; name: \"verificationGasLimit\" },\n      { type: \"uint256\"; name: \"preVerificationGas\" },\n      { type: \"uint256\"; name: \"maxFeePerGas\" },\n      { type: \"uint256\"; name: \"maxPriorityFeePerGas\" },\n      { type: \"bytes\"; name: \"paymasterAndData\" },\n      { type: \"bytes\"; name: \"signature\" },\n    ];\n  }>;\n};\n\nexport const FN_SELECTOR = \"0xa6193531\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"tuple\",\n    name: \"userOp\",\n    components: [\n      {\n        type: \"address\",\n        name: \"sender\",\n      },\n      {\n        type: \"uint256\",\n        name: \"nonce\",\n      },\n      {\n        type: \"bytes\",\n        name: \"initCode\",\n      },\n      {\n        type: \"bytes\",\n        name: \"callData\",\n      },\n      {\n        type: \"uint256\",\n        name: \"callGasLimit\",\n      },\n      {\n        type: \"uint256\",\n        name: \"verificationGasLimit\",\n      },\n      {\n        type: \"uint256\",\n        name: \"preVerificationGas\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxFeePerGas\",\n      },\n      {\n        type: \"uint256\",\n        name: \"maxPriorityFeePerGas\",\n      },\n      {\n        type: \"bytes\",\n        name: \"paymasterAndData\",\n      },\n      {\n        type: \"bytes\",\n        name: \"signature\",\n      },\n    ],\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bytes32\",\n  },\n] as const;\n\n/**\n * Checks if the `getUserOpHash` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getUserOpHash` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetUserOpHashSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetUserOpHashSupported([\"0x...\"]);\n * ```\n */\nexport function isGetUserOpHashSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"getUserOpHash\" function.\n * @param options - The options for the getUserOpHash function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetUserOpHashParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetUserOpHashParams({\n *  userOp: ...,\n * });\n * ```\n */\nexport function encodeGetUserOpHashParams(options: GetUserOpHashParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.userOp]);\n}\n\n/**\n * Encodes the \"getUserOpHash\" function into a Hex string with its parameters.\n * @param options - The options for the getUserOpHash function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetUserOpHash } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetUserOpHash({\n *  userOp: ...,\n * });\n * ```\n */\nexport function encodeGetUserOpHash(options: GetUserOpHashParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeGetUserOpHashParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the getUserOpHash function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetUserOpHashResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetUserOpHashResultResult(\"...\");\n * ```\n */\nexport function decodeGetUserOpHashResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getUserOpHash\" function on the contract.\n * @param options - The options for the getUserOpHash function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getUserOpHash } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getUserOpHash({\n *  contract,\n *  userOp: ...,\n * });\n *\n * ```\n */\nexport async function getUserOpHash(\n  options: BaseTransactionOptions<GetUserOpHashParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.userOp],\n  });\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"getUserOpHash\" function.\n */\nexport type GetUserOpHashParams = {\n  userOp: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"userOp\";\n    components: [\n      { type: \"address\"; name: \"sender\" },\n      { type: \"uint256\"; name: \"nonce\" },\n      { type: \"bytes\"; name: \"initCode\" },\n      { type: \"bytes\"; name: \"callData\" },\n      { type: \"bytes32\"; name: \"accountGasLimits\" },\n      { type: \"uint256\"; name: \"preVerificationGas\" },\n      { type: \"bytes32\"; name: \"gasFees\" },\n      { type: \"bytes\"; name: \"paymasterAndData\" },\n      { type: \"bytes\"; name: \"signature\" },\n    ];\n  }>;\n};\n\nexport const FN_SELECTOR = \"0x22cdde4c\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"tuple\",\n    name: \"userOp\",\n    components: [\n      {\n        type: \"address\",\n        name: \"sender\",\n      },\n      {\n        type: \"uint256\",\n        name: \"nonce\",\n      },\n      {\n        type: \"bytes\",\n        name: \"initCode\",\n      },\n      {\n        type: \"bytes\",\n        name: \"callData\",\n      },\n      {\n        type: \"bytes32\",\n        name: \"accountGasLimits\",\n      },\n      {\n        type: \"uint256\",\n        name: \"preVerificationGas\",\n      },\n      {\n        type: \"bytes32\",\n        name: \"gasFees\",\n      },\n      {\n        type: \"bytes\",\n        name: \"paymasterAndData\",\n      },\n      {\n        type: \"bytes\",\n        name: \"signature\",\n      },\n    ],\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bytes32\",\n  },\n] as const;\n\n/**\n * Checks if the `getUserOpHash` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `getUserOpHash` method is supported.\n * @extension ERC4337\n * @example\n * ```ts\n * import { isGetUserOpHashSupported } from \"thirdweb/extensions/erc4337\";\n * const supported = isGetUserOpHashSupported([\"0x...\"]);\n * ```\n */\nexport function isGetUserOpHashSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"getUserOpHash\" function.\n * @param options - The options for the getUserOpHash function.\n * @returns The encoded ABI parameters.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetUserOpHashParams } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetUserOpHashParams({\n *  userOp: ...,\n * });\n * ```\n */\nexport function encodeGetUserOpHashParams(options: GetUserOpHashParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.userOp]);\n}\n\n/**\n * Encodes the \"getUserOpHash\" function into a Hex string with its parameters.\n * @param options - The options for the getUserOpHash function.\n * @returns The encoded hexadecimal string.\n * @extension ERC4337\n * @example\n * ```ts\n * import { encodeGetUserOpHash } from \"thirdweb/extensions/erc4337\";\n * const result = encodeGetUserOpHash({\n *  userOp: ...,\n * });\n * ```\n */\nexport function encodeGetUserOpHash(options: GetUserOpHashParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeGetUserOpHashParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the getUserOpHash function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC4337\n * @example\n * ```ts\n * import { decodeGetUserOpHashResult } from \"thirdweb/extensions/erc4337\";\n * const result = decodeGetUserOpHashResultResult(\"...\");\n * ```\n */\nexport function decodeGetUserOpHashResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"getUserOpHash\" function on the contract.\n * @param options - The options for the getUserOpHash function.\n * @returns The parsed result of the function call.\n * @extension ERC4337\n * @example\n * ```ts\n * import { getUserOpHash } from \"thirdweb/extensions/erc4337\";\n *\n * const result = await getUserOpHash({\n *  contract,\n *  userOp: ...,\n * });\n *\n * ```\n */\nexport async function getUserOpHash(\n  options: BaseTransactionOptions<GetUserOpHashParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.userOp],\n  });\n}\n", "import { type Hex, concat, pad, toHex } from \"viem\";\nimport type { PackedUserOperation, UserOperationV07 } from \"../types.js\";\n\nfunction getInitCode(unpackedUserOperation: UserOperationV07) {\n  return unpackedUserOperation.factory\n    ? concat([\n        unpackedUserOperation.factory as Hex,\n        unpackedUserOperation.factoryData || (\"0x\" as Hex),\n      ])\n    : \"0x\";\n}\n\nfunction getAccountGasLimits(unpackedUserOperation: UserOperationV07) {\n  return concat([\n    pad(toHex(BigInt(unpackedUserOperation.verificationGasLimit)), {\n      size: 16,\n    }),\n    pad(toHex(BigInt(unpackedUserOperation.callGasLimit)), { size: 16 }),\n  ]) as Hex;\n}\n\nfunction getGasLimits(unpackedUserOperation: UserOperationV07) {\n  return concat([\n    pad(toHex(BigInt(unpackedUserOperation.maxPriorityFeePerGas)), {\n      size: 16,\n    }),\n    pad(toHex(BigInt(unpackedUserOperation.maxFeePerGas)), { size: 16 }),\n  ]) as Hex;\n}\n\nfunction getPaymasterAndData(unpackedUserOperation: UserOperationV07) {\n  return unpackedUserOperation.paymaster\n    ? concat([\n        unpackedUserOperation.paymaster as Hex,\n        pad(\n          toHex(\n            BigInt(unpackedUserOperation.paymasterVerificationGasLimit || 0),\n          ),\n          {\n            size: 16,\n          },\n        ),\n        pad(toHex(BigInt(unpackedUserOperation.paymasterPostOpGasLimit || 0)), {\n          size: 16,\n        }),\n        unpackedUserOperation.paymasterData || (\"0x\" as Hex),\n      ])\n    : \"0x\";\n}\n\nexport const getPackedUserOperation = (\n  userOperation: UserOperationV07,\n): PackedUserOperation => {\n  return {\n    sender: userOperation.sender,\n    nonce: BigInt(userOperation.nonce),\n    initCode: getInitCode(userOperation),\n    callData: userOperation.callData,\n    accountGasLimits: getAccountGasLimits(userOperation),\n    preVerificationGas: BigInt(userOperation.preVerificationGas),\n    gasFees: getGasLimits(userOperation),\n    paymasterAndData: getPaymasterAndData(userOperation),\n    signature: userOperation.signature,\n  };\n};\n", "import { isHex, toHex } from \"../../../utils/encoding/hex.js\";\nimport type {\n  UserOperationV06,\n  UserOperationV06Hexed,\n  UserOperationV07,\n  UserOperationV07Hexed,\n} from \"../types.js\";\n\nexport const generateRandomUint192 = (): bigint => {\n  const rand1 = BigInt(Math.floor(Math.random() * 0x100000000));\n  const rand2 = BigInt(Math.floor(Math.random() * 0x100000000));\n  const rand3 = BigInt(Math.floor(Math.random() * 0x100000000));\n  const rand4 = BigInt(Math.floor(Math.random() * 0x100000000));\n  const rand5 = BigInt(Math.floor(Math.random() * 0x100000000));\n  const rand6 = BigInt(Math.floor(Math.random() * 0x100000000));\n  return (\n    (rand1 << BigInt(160)) |\n    (rand2 << BigInt(128)) |\n    (rand3 << BigInt(96)) |\n    (rand4 << BigInt(64)) |\n    (rand5 << BigInt(32)) |\n    rand6\n  );\n};\n\n/**\n * @internal\n */\nexport function hexlifyUserOp(\n  userOp: UserOperationV06 | UserOperationV07,\n): UserOperationV06Hexed | UserOperationV07Hexed {\n  return Object.fromEntries(\n    Object.entries(userOp).map(([key, val]) => [\n      key,\n      // turn any value that's not hex into hex\n      val === undefined || val === null || isHex(val) ? val : toHex(val),\n    ]),\n  ) as UserOperationV06Hexed | UserOperationV07Hexed;\n}\n", "import type { Chain } from \"../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport { hexToBigInt } from \"../../../utils/encoding/hex.js\";\nimport { getClientFetch } from \"../../../utils/fetch.js\";\nimport { stringify } from \"../../../utils/json.js\";\nimport type {\n  PaymasterResult,\n  UserOperationV06,\n  UserOperationV07,\n} from \"../types.js\";\nimport { ENTRYPOINT_ADDRESS_v0_6, getDefaultBundlerUrl } from \"./constants.js\";\nimport { hexlifyUserOp } from \"./utils.js\";\n\n/**\n * Get paymaster and data details for a user operation.\n * @param args - The userOp and options\n * @returns - The paymaster and data details for the user operation.\n * @example\n * ```ts\n * import { getPaymasterAndData } from \"thirdweb/wallets/smart\";\n *\n * const userOp = createUnsignedUserOp(...);\n *\n * const paymasterAndData = await getPaymasterAndData({\n *  userOp,\n *  client,\n *  chain,\n * });\n * ```\n * @walletUtils\n */\nexport async function getPaymasterAndData(args: {\n  userOp: UserOperationV06 | UserOperationV07;\n  client: ThirdwebClient;\n  chain: Chain;\n  entrypointAddress?: string;\n  paymasterOverride?: (\n    userOp: UserOperationV06 | UserOperationV07,\n  ) => Promise<PaymasterResult>;\n}): Promise<PaymasterResult> {\n  const { userOp, paymasterOverride, client, chain, entrypointAddress } = args;\n\n  if (paymasterOverride) {\n    return paymasterOverride(userOp);\n  }\n\n  const headers: Record<string, string> = {\n    \"Content-Type\": \"application/json\",\n  };\n\n  const entrypoint = entrypointAddress ?? ENTRYPOINT_ADDRESS_v0_6;\n  const paymasterUrl = getDefaultBundlerUrl(chain);\n\n  const body = {\n    jsonrpc: \"2.0\",\n    id: 1,\n    method: \"pm_sponsorUserOperation\",\n    params: [hexlifyUserOp(userOp), entrypoint],\n  };\n\n  // Ask the paymaster to sign the transaction and return a valid paymasterAndData value.\n  const fetchWithHeaders = getClientFetch(client);\n  const response = await fetchWithHeaders(paymasterUrl, {\n    method: \"POST\",\n    headers,\n    body: stringify(body),\n  });\n\n  if (!response.ok) {\n    const error = (await response.text()) || response.statusText;\n\n    throw new Error(`Paymaster error: ${response.status} - ${error}`);\n  }\n\n  const res = await response.json();\n\n  if (res.result) {\n    // some paymasters return a string, some return an object with more data\n    if (typeof res.result === \"string\") {\n      return {\n        paymasterAndData: res.result,\n      };\n    }\n    // check for policy errors\n    if (res.result.reason) {\n      console.warn(\n        `Paymaster policy rejected this transaction with reason: ${res.result.reason} ${res.result.policyId ? `(policyId: ${res.result.policyId})` : \"\"}`,\n      );\n    }\n\n    return {\n      paymasterAndData: res.result.paymasterAndData,\n      verificationGasLimit: res.result.verificationGasLimit\n        ? hexToBigInt(res.result.verificationGasLimit)\n        : undefined,\n      preVerificationGas: res.result.preVerificationGas\n        ? hexToBigInt(res.result.preVerificationGas)\n        : undefined,\n      callGasLimit: res.result.callGasLimit\n        ? hexToBigInt(res.result.callGasLimit)\n        : undefined,\n      paymaster: res.result.paymaster,\n      paymasterData: res.result.paymasterData,\n      paymasterVerificationGasLimit: res.result.paymasterVerificationGasLimit\n        ? hexToBigInt(res.result.paymasterVerificationGasLimit)\n        : undefined,\n      paymasterPostOpGasLimit: res.result.paymasterPostOpGasLimit\n        ? hexToBigInt(res.result.paymasterPostOpGasLimit)\n        : undefined,\n    };\n  }\n  const error =\n    res.error?.message || res.error || response.statusText || \"unknown error\";\n  throw new Error(`Paymaster error from ${paymasterUrl}: ${error}`);\n}\n", "import { maxUint96 } from \"ox/Solidity\";\nimport { concat } from \"viem\";\nimport type { Chain } from \"../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport {\n  type ThirdwebContract,\n  getContract,\n} from \"../../../contract/contract.js\";\nimport { getNonce } from \"../../../extensions/erc4337/__generated__/IEntryPoint/read/getNonce.js\";\nimport { getUserOpHash as getUserOpHashV06 } from \"../../../extensions/erc4337/__generated__/IEntryPoint/read/getUserOpHash.js\";\nimport { getUserOpHash as getUserOpHashV07 } from \"../../../extensions/erc4337/__generated__/IEntryPoint_v07/read/getUserOpHash.js\";\nimport { getDefaultGasOverrides } from \"../../../gas/fee-data.js\";\nimport { encode } from \"../../../transaction/actions/encode.js\";\nimport { toSerializableTransaction } from \"../../../transaction/actions/to-serializable-transaction.js\";\nimport type { PreparedTransaction } from \"../../../transaction/prepare-transaction.js\";\nimport type { TransactionReceipt } from \"../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../utils/abi/encodeAbiParameters.js\";\nimport { isContractDeployed } from \"../../../utils/bytecode/is-contract-deployed.js\";\nimport { type Hex, toHex } from \"../../../utils/encoding/hex.js\";\nimport { hexToBytes } from \"../../../utils/encoding/to-bytes.js\";\nimport { isThirdwebUrl } from \"../../../utils/fetch.js\";\nimport { keccak256 } from \"../../../utils/hashing/keccak256.js\";\nimport { stringify } from \"../../../utils/json.js\";\nimport { resolvePromisedValue } from \"../../../utils/promise/resolve-promised-value.js\";\nimport type { Account } from \"../../interfaces/wallet.js\";\nimport { getEntrypointFromFactory } from \"../index.js\";\nimport type {\n  BundlerOptions,\n  PaymasterResult,\n  SmartWalletOptions,\n  UserOperationV06,\n  UserOperationV07,\n} from \"../types.js\";\nimport {\n  estimateUserOpGas,\n  getUserOpGasFees,\n  getUserOpReceipt,\n} from \"./bundler.js\";\nimport {\n  predictAddress,\n  prepareBatchExecute,\n  prepareCreateAccount,\n  prepareExecute,\n} from \"./calls.js\";\nimport {\n  DUMMY_SIGNATURE,\n  ENTRYPOINT_ADDRESS_v0_6,\n  ENTRYPOINT_ADDRESS_v0_7,\n  getDefaultAccountFactory,\n  getDefaultBundlerUrl,\n  getEntryPointVersion,\n} from \"./constants.js\";\nimport { getPackedUserOperation } from \"./packUserOp.js\";\nimport { getPaymasterAndData } from \"./paymaster.js\";\nimport { generateRandomUint192 } from \"./utils.js\";\n\nconst isDeployingSet: Set<string> = new Set();\n\nconst getKey = (accountContract: ThirdwebContract) => {\n  return `${accountContract.chain.id}:${accountContract.address}`;\n};\n\nconst markAccountDeploying = (accountContract: ThirdwebContract) => {\n  isDeployingSet.add(getKey(accountContract));\n};\n\nexport const clearAccountDeploying = (accountContract: ThirdwebContract) => {\n  isDeployingSet.delete(getKey(accountContract));\n};\n\nconst isAccountDeploying = (accountContract: ThirdwebContract) => {\n  return isDeployingSet.has(getKey(accountContract));\n};\n\n/**\n * Wait for the user operation to be mined.\n * @param args - The options and user operation hash\n * @returns - The transaction receipt\n *\n * @example\n * ```ts\n * import { waitForUserOpReceipt } from \"thirdweb/wallets/smart\";\n *\n * const receipt = await waitForUserOpReceipt({\n *  chain,\n *  client,\n *  userOpHash,\n * });\n * ```\n * @walletUtils\n */\nexport async function waitForUserOpReceipt(\n  args: BundlerOptions & {\n    userOpHash: Hex;\n    timeoutMs?: number;\n    intervalMs?: number;\n  },\n): Promise<TransactionReceipt> {\n  const timeout = args.timeoutMs || 120000; // 2mins\n  const interval = args.intervalMs || 1000; // 1s\n  const endtime = Date.now() + timeout;\n  while (Date.now() < endtime) {\n    const userOpReceipt = await getUserOpReceipt(args);\n    if (userOpReceipt) {\n      return userOpReceipt;\n    }\n    await new Promise((resolve) => setTimeout(resolve, interval));\n  }\n  throw new Error(\n    `Timeout waiting for userOp to be mined on chain ${args.chain.id} with UserOp hash: ${args.userOpHash}`,\n  );\n}\n\n/**\n * Creates an unsigned user operation from a prepared transaction.\n * @param args - The prepared transaction and options\n * @returns - The unsigned user operation\n * @example\n * ```ts\n * import { createUnsignedUserOp } from \"thirdweb/wallets/smart\";\n *\n * const transaction = prepareContractCall(...);\n *\n * const userOp = await createUnsignedUserOp({\n *  transaction,\n *  factoryContract,\n *  accountContract,\n *  adminAddress,\n *  sponsorGas,\n *  overrides,\n * });\n * ```\n * @walletUtils\n */\nexport async function createUnsignedUserOp(args: {\n  transaction: PreparedTransaction;\n  factoryContract: ThirdwebContract;\n  accountContract: ThirdwebContract;\n  adminAddress: string;\n  sponsorGas: boolean;\n  waitForDeployment?: boolean;\n  isDeployedOverride?: boolean;\n  overrides?: SmartWalletOptions[\"overrides\"];\n}): Promise<UserOperationV06 | UserOperationV07> {\n  const {\n    transaction: executeTx,\n    accountContract,\n    factoryContract,\n    adminAddress,\n    overrides,\n    sponsorGas,\n    waitForDeployment = true,\n    isDeployedOverride,\n  } = args;\n  const chain = executeTx.chain;\n  const client = executeTx.client;\n\n  const bundlerOptions = {\n    client,\n    chain,\n    bundlerUrl: overrides?.bundlerUrl,\n    entrypointAddress: overrides?.entrypointAddress,\n  };\n\n  const entrypointVersion = getEntryPointVersion(\n    args.overrides?.entrypointAddress || ENTRYPOINT_ADDRESS_v0_6,\n  );\n\n  const [isDeployed, callData, callGasLimit, gasFees, nonce] =\n    await Promise.all([\n      typeof isDeployedOverride === \"boolean\"\n        ? isDeployedOverride\n        : isContractDeployed(accountContract).then(\n            (isDeployed) => isDeployed || isAccountDeploying(accountContract),\n          ),\n      encode(executeTx),\n      resolvePromisedValue(executeTx.gas),\n      getGasFees({\n        executeTx,\n        bundlerOptions,\n        chain,\n        client,\n      }),\n      getAccountNonce({\n        accountContract,\n        chain,\n        client,\n        entrypointAddress: overrides?.entrypointAddress,\n        getNonceOverride: overrides?.getAccountNonce,\n      }),\n    ]);\n\n  const { maxFeePerGas, maxPriorityFeePerGas } = gasFees;\n\n  if (entrypointVersion === \"v0.7\") {\n    return populateUserOp_v0_7({\n      bundlerOptions,\n      factoryContract,\n      accountContract,\n      adminAddress,\n      sponsorGas,\n      overrides,\n      isDeployed,\n      nonce,\n      callData,\n      callGasLimit,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      waitForDeployment,\n    });\n  }\n\n  // default to v0.6\n  return populateUserOp_v0_6({\n    bundlerOptions,\n    factoryContract,\n    accountContract,\n    adminAddress,\n    sponsorGas,\n    overrides,\n    isDeployed,\n    nonce,\n    callData,\n    callGasLimit,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    waitForDeployment,\n  });\n}\n\nasync function getGasFees(args: {\n  executeTx: PreparedTransaction;\n  bundlerOptions: BundlerOptions;\n  chain: Chain;\n  client: ThirdwebClient;\n}): Promise<{\n  maxFeePerGas: bigint;\n  maxPriorityFeePerGas: bigint;\n}> {\n  const { executeTx, bundlerOptions, chain, client } = args;\n  let { maxFeePerGas, maxPriorityFeePerGas } = executeTx;\n\n  const bundlerUrl = bundlerOptions?.bundlerUrl ?? getDefaultBundlerUrl(chain);\n\n  if (isThirdwebUrl(bundlerUrl)) {\n    // get gas prices from bundler\n    const bundlerGasPrice = await getUserOpGasFees({\n      options: bundlerOptions,\n    });\n    maxFeePerGas = bundlerGasPrice.maxFeePerGas;\n    maxPriorityFeePerGas = bundlerGasPrice.maxPriorityFeePerGas;\n  } else {\n    // Check for explicity values\n    const [resolvedMaxFeePerGas, resolvedMaxPriorityFeePerGas] =\n      await Promise.all([\n        resolvePromisedValue(maxFeePerGas),\n        resolvePromisedValue(maxPriorityFeePerGas),\n      ]);\n\n    if (resolvedMaxFeePerGas && resolvedMaxPriorityFeePerGas) {\n      // Save a network call if the values are provided\n      maxFeePerGas = resolvedMaxFeePerGas;\n      maxPriorityFeePerGas = resolvedMaxPriorityFeePerGas;\n    } else {\n      // Fallback to RPC gas prices if no explicit values provided\n      const feeData = await getDefaultGasOverrides(client, chain);\n\n      // Still check for explicit values in case one is provided and not the other\n      maxPriorityFeePerGas =\n        resolvedMaxPriorityFeePerGas ?? feeData.maxPriorityFeePerGas ?? 0n;\n      maxFeePerGas = resolvedMaxFeePerGas ?? feeData.maxFeePerGas ?? 0n;\n    }\n  }\n  return { maxFeePerGas, maxPriorityFeePerGas };\n}\n\nasync function populateUserOp_v0_7(args: {\n  bundlerOptions: BundlerOptions;\n  factoryContract: ThirdwebContract;\n  accountContract: ThirdwebContract;\n  adminAddress: string;\n  sponsorGas: boolean;\n  overrides?: SmartWalletOptions[\"overrides\"];\n  isDeployed: boolean;\n  nonce: bigint;\n  callData: Hex;\n  callGasLimit?: bigint;\n  maxFeePerGas: bigint;\n  maxPriorityFeePerGas: bigint;\n  waitForDeployment: boolean;\n}): Promise<UserOperationV07> {\n  const {\n    bundlerOptions,\n    isDeployed,\n    factoryContract,\n    accountContract,\n    adminAddress,\n    sponsorGas,\n    overrides,\n    nonce,\n    callData,\n    callGasLimit,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    waitForDeployment,\n  } = args;\n  const { chain, client } = bundlerOptions;\n\n  let factory: string | undefined;\n  let factoryData: Hex;\n  if (isDeployed) {\n    factoryData = \"0x\";\n    if (waitForDeployment) {\n      // lock until account is deployed if needed to avoid 'sender already created' errors when sending multiple transactions in parallel\n      await waitForAccountDeployed(accountContract);\n    }\n  } else {\n    factory = factoryContract.address;\n    factoryData = await encode(\n      prepareCreateAccount({\n        factoryContract: factoryContract,\n        adminAddress,\n        accountSalt: overrides?.accountSalt,\n        createAccountOverride: overrides?.createAccount,\n      }),\n    );\n    if (waitForDeployment) {\n      markAccountDeploying(accountContract);\n    }\n  }\n\n  const partialOp: UserOperationV07 = {\n    sender: accountContract.address,\n    nonce,\n    callData,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    callGasLimit: callGasLimit ?? 0n,\n    verificationGasLimit: 0n,\n    preVerificationGas: 0n,\n    factory,\n    factoryData,\n    paymaster: undefined,\n    paymasterData: \"0x\",\n    paymasterVerificationGasLimit: 0n,\n    paymasterPostOpGasLimit: 0n,\n    signature: DUMMY_SIGNATURE,\n  };\n\n  if (sponsorGas) {\n    const paymasterResult = (await getPaymasterAndData({\n      userOp: partialOp,\n      chain,\n      client,\n      entrypointAddress: overrides?.entrypointAddress,\n      paymasterOverride: overrides?.paymaster,\n    })) as Extract<PaymasterResult, { paymaster: string }>;\n    if (paymasterResult.paymaster && paymasterResult.paymasterData) {\n      partialOp.paymaster = paymasterResult.paymaster;\n      partialOp.paymasterData = paymasterResult.paymasterData as Hex;\n    }\n    // paymaster can have the gas limits in the response\n    if (\n      paymasterResult.callGasLimit &&\n      paymasterResult.verificationGasLimit &&\n      paymasterResult.preVerificationGas &&\n      paymasterResult.paymasterPostOpGasLimit &&\n      paymasterResult.paymasterVerificationGasLimit\n    ) {\n      partialOp.callGasLimit = paymasterResult.callGasLimit;\n      partialOp.verificationGasLimit = paymasterResult.verificationGasLimit;\n      partialOp.preVerificationGas = paymasterResult.preVerificationGas;\n      partialOp.paymasterPostOpGasLimit =\n        paymasterResult.paymasterPostOpGasLimit;\n      partialOp.paymasterVerificationGasLimit =\n        paymasterResult.paymasterVerificationGasLimit;\n    } else {\n      // otherwise fallback to bundler for gas limits\n      const stateOverrides = overrides?.tokenPaymaster\n        ? {\n            [overrides.tokenPaymaster.tokenAddress]: {\n              stateDiff: {\n                [keccak256(\n                  encodeAbiParameters(\n                    [{ type: \"address\" }, { type: \"uint256\" }],\n                    [\n                      accountContract.address,\n                      overrides.tokenPaymaster.balanceStorageSlot,\n                    ],\n                  ),\n                )]: toHex(maxUint96, { size: 32 }),\n              },\n            },\n          }\n        : undefined;\n      const estimates = await estimateUserOpGas(\n        {\n          userOp: partialOp,\n          options: bundlerOptions,\n        },\n        stateOverrides,\n      );\n      partialOp.callGasLimit = estimates.callGasLimit;\n      partialOp.verificationGasLimit = estimates.verificationGasLimit;\n      partialOp.preVerificationGas = estimates.preVerificationGas;\n      partialOp.paymasterPostOpGasLimit = overrides?.tokenPaymaster\n        ? 500000n // TODO: estimate this better, needed if there's an extra swap needed in the paymaster\n        : estimates.paymasterPostOpGasLimit || 0n;\n      partialOp.paymasterVerificationGasLimit =\n        estimates.paymasterVerificationGasLimit || 0n;\n      // need paymaster to re-sign after estimates\n      const paymasterResult2 = (await getPaymasterAndData({\n        userOp: partialOp,\n        chain,\n        client,\n        entrypointAddress: overrides?.entrypointAddress,\n        paymasterOverride: overrides?.paymaster,\n      })) as Extract<PaymasterResult, { paymaster: string }>;\n      if (paymasterResult2.paymaster && paymasterResult2.paymasterData) {\n        partialOp.paymaster = paymasterResult2.paymaster;\n        partialOp.paymasterData = paymasterResult2.paymasterData as Hex;\n      }\n    }\n  } else {\n    // not gasless, so we just need to estimate gas limits\n    const estimates = await estimateUserOpGas({\n      userOp: partialOp,\n      options: bundlerOptions,\n    });\n    partialOp.callGasLimit = estimates.callGasLimit;\n    partialOp.verificationGasLimit = estimates.verificationGasLimit;\n    partialOp.preVerificationGas = estimates.preVerificationGas;\n    partialOp.paymasterPostOpGasLimit = estimates.paymasterPostOpGasLimit || 0n;\n    partialOp.paymasterVerificationGasLimit =\n      estimates.paymasterVerificationGasLimit || 0n;\n  }\n  return {\n    ...partialOp,\n    signature: \"0x\" as Hex,\n  };\n}\n\nasync function populateUserOp_v0_6(args: {\n  bundlerOptions: BundlerOptions;\n  factoryContract: ThirdwebContract;\n  accountContract: ThirdwebContract;\n  adminAddress: string;\n  sponsorGas: boolean;\n  overrides?: SmartWalletOptions[\"overrides\"];\n  isDeployed: boolean;\n  nonce: bigint;\n  callData: Hex;\n  callGasLimit?: bigint;\n  maxFeePerGas: bigint;\n  maxPriorityFeePerGas: bigint;\n  waitForDeployment: boolean;\n}): Promise<UserOperationV06> {\n  const {\n    bundlerOptions,\n    isDeployed,\n    factoryContract,\n    accountContract,\n    adminAddress,\n    sponsorGas,\n    overrides,\n    nonce,\n    callData,\n    callGasLimit,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    waitForDeployment,\n  } = args;\n  const { chain, client } = bundlerOptions;\n  let initCode: Hex;\n\n  if (isDeployed) {\n    initCode = \"0x\";\n    if (waitForDeployment) {\n      // lock until account is deployed if needed to avoid 'sender already created' errors when sending multiple transactions in parallel\n      await waitForAccountDeployed(accountContract);\n    }\n  } else {\n    initCode = await getAccountInitCode({\n      factoryContract: factoryContract,\n      adminAddress,\n      accountSalt: overrides?.accountSalt,\n      createAccountOverride: overrides?.createAccount,\n    });\n    if (waitForDeployment) {\n      markAccountDeploying(accountContract);\n    }\n  }\n\n  const partialOp: UserOperationV06 = {\n    sender: accountContract.address,\n    nonce,\n    initCode,\n    callData,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    callGasLimit: callGasLimit ?? 0n,\n    verificationGasLimit: 0n,\n    preVerificationGas: 0n,\n    paymasterAndData: \"0x\",\n    signature: DUMMY_SIGNATURE,\n  };\n\n  if (sponsorGas) {\n    const paymasterResult = await getPaymasterAndData({\n      userOp: partialOp,\n      chain,\n      client,\n      entrypointAddress: overrides?.entrypointAddress,\n      paymasterOverride: overrides?.paymaster,\n    });\n    const paymasterAndData =\n      \"paymasterAndData\" in paymasterResult\n        ? paymasterResult.paymasterAndData\n        : \"0x\";\n    if (paymasterAndData && paymasterAndData !== \"0x\") {\n      partialOp.paymasterAndData = paymasterAndData as Hex;\n    }\n    // paymaster can have the gas limits in the response\n    if (\n      paymasterResult.callGasLimit &&\n      paymasterResult.verificationGasLimit &&\n      paymasterResult.preVerificationGas\n    ) {\n      partialOp.callGasLimit = paymasterResult.callGasLimit;\n      partialOp.verificationGasLimit = paymasterResult.verificationGasLimit;\n      partialOp.preVerificationGas = paymasterResult.preVerificationGas;\n    } else {\n      // otherwise fallback to bundler for gas limits\n      const estimates = await estimateUserOpGas({\n        userOp: partialOp,\n        options: bundlerOptions,\n      });\n      partialOp.callGasLimit = estimates.callGasLimit;\n      partialOp.verificationGasLimit = estimates.verificationGasLimit;\n      partialOp.preVerificationGas = estimates.preVerificationGas;\n      // need paymaster to re-sign after estimates\n      if (paymasterAndData && paymasterAndData !== \"0x\") {\n        const paymasterResult2 = await getPaymasterAndData({\n          userOp: partialOp,\n          chain,\n          client,\n          entrypointAddress: overrides?.entrypointAddress,\n          paymasterOverride: overrides?.paymaster,\n        });\n        const paymasterAndData2 =\n          \"paymasterAndData\" in paymasterResult2\n            ? paymasterResult2.paymasterAndData\n            : \"0x\";\n        if (paymasterAndData2 && paymasterAndData2 !== \"0x\") {\n          partialOp.paymasterAndData = paymasterAndData2 as Hex;\n        }\n      }\n    }\n  } else {\n    // not gasless, so we just need to estimate gas limits\n    const estimates = await estimateUserOpGas({\n      userOp: partialOp,\n      options: bundlerOptions,\n    });\n    partialOp.callGasLimit = estimates.callGasLimit;\n    partialOp.verificationGasLimit = estimates.verificationGasLimit;\n    partialOp.preVerificationGas = estimates.preVerificationGas;\n  }\n  return {\n    ...partialOp,\n    signature: \"0x\" as Hex,\n  };\n}\n\n/**\n * Sign a user operation.\n * @param userOp - The UserOperation to sign (with signature field ignored)\n * @returns - The user operation with the signature field populated\n * @example\n * ```ts\n * import { signUserOp } from \"thirdweb/wallets/smart\";\n *\n * const userOp = await createUnsignedUserOp(...);\n *\n * const signedUserOp = await signUserOp({\n *  client,\n *  userOp,\n *  chain,\n *  adminAccount,\n * });\n * ```\n * @walletUtils\n */\nexport async function signUserOp(args: {\n  client: ThirdwebClient;\n  userOp: UserOperationV06 | UserOperationV07;\n  chain: Chain;\n  entrypointAddress?: string;\n  adminAccount: Account;\n}): Promise<UserOperationV06 | UserOperationV07> {\n  const { userOp, chain, entrypointAddress, adminAccount } = args;\n\n  const userOpHash = await getUserOpHash({\n    client: args.client,\n    userOp,\n    chain,\n    entrypointAddress,\n  });\n\n  if (adminAccount.signMessage) {\n    const signature = await adminAccount.signMessage({\n      message: {\n        raw: hexToBytes(userOpHash),\n      },\n      originalMessage: stringify(userOp),\n      chainId: chain.id,\n    });\n    return {\n      ...userOp,\n      signature,\n    };\n  }\n  throw new Error(\"signMessage not implemented in signingAccount\");\n}\n\n/**\n * Get the hash of a user operation.\n * @param args - The options for getting the user operation hash\n * @returns - The user operation hash\n * @example\n * ```ts\n * import { getUserOpHash } from \"thirdweb/wallets/smart\";\n *\n * const userOp = await createUnsignedUserOp(...);\n * const userOpHash = await getUserOpHash({\n *  client,\n *  userOp,\n *  chain,\n * });\n * ```\n * @walletUtils\n */\nexport async function getUserOpHash(args: {\n  client: ThirdwebClient;\n  userOp: UserOperationV06 | UserOperationV07;\n  chain: Chain;\n  entrypointAddress?: string;\n}): Promise<Hex> {\n  const { userOp, chain, entrypointAddress } = args;\n\n  const entrypointVersion = getEntryPointVersion(\n    entrypointAddress || ENTRYPOINT_ADDRESS_v0_6,\n  );\n\n  let userOpHash: Hex;\n\n  if (entrypointVersion === \"v0.7\") {\n    const packedUserOp = getPackedUserOperation(userOp as UserOperationV07);\n    userOpHash = await getUserOpHashV07({\n      contract: getContract({\n        address: entrypointAddress || ENTRYPOINT_ADDRESS_v0_7,\n        chain,\n        client: args.client,\n      }),\n      userOp: packedUserOp,\n    });\n  } else {\n    userOpHash = await getUserOpHashV06({\n      contract: getContract({\n        address: entrypointAddress || ENTRYPOINT_ADDRESS_v0_6,\n        chain,\n        client: args.client,\n      }),\n      userOp: userOp as UserOperationV06,\n    });\n  }\n  return userOpHash;\n}\n\nasync function getAccountInitCode(options: {\n  factoryContract: ThirdwebContract;\n  adminAddress: string;\n  accountSalt?: string;\n  createAccountOverride?: (\n    factoryContract: ThirdwebContract,\n    adminAddress: string,\n  ) => PreparedTransaction;\n}): Promise<Hex> {\n  const { factoryContract, adminAddress, accountSalt, createAccountOverride } =\n    options;\n  const deployTx = prepareCreateAccount({\n    factoryContract,\n    adminAddress,\n    accountSalt,\n    createAccountOverride,\n  });\n  return concat([factoryContract.address as Hex, await encode(deployTx)]);\n}\n\nasync function getAccountNonce(options: {\n  accountContract: ThirdwebContract;\n  chain: Chain;\n  client: ThirdwebClient;\n  entrypointAddress?: string;\n  getNonceOverride?: (accountContract: ThirdwebContract) => Promise<bigint>;\n}): Promise<bigint> {\n  const {\n    accountContract,\n    chain,\n    client,\n    entrypointAddress,\n    getNonceOverride,\n  } = options;\n  if (getNonceOverride) {\n    return getNonceOverride(accountContract);\n  }\n  return await getNonce({\n    contract: getContract({\n      address: entrypointAddress || ENTRYPOINT_ADDRESS_v0_6,\n      chain,\n      client,\n    }),\n    key: generateRandomUint192(),\n    sender: accountContract.address,\n  });\n}\n\n/**\n * Create and sign a user operation.\n * @param options - The options for creating and signing the user operation\n * @returns - The signed user operation\n * @example\n * ```ts\n * import { createAndSignUserOp } from \"thirdweb/wallets/smart\";\n *\n * const userOp = await createAndSignUserOp({\n *  client,\n *  adminAccount,\n *  smartWalletOptions,\n *  transactions,\n * });\n * ```\n * @walletUtils\n */\nexport async function createAndSignUserOp(options: {\n  transactions: PreparedTransaction[];\n  adminAccount: Account;\n  client: ThirdwebClient;\n  smartWalletOptions: SmartWalletOptions;\n  waitForDeployment?: boolean;\n  isDeployedOverride?: boolean;\n}) {\n  // if factory is passed, but no entrypoint, try to resolve entrypoint from factory\n  if (\n    options.smartWalletOptions.factoryAddress &&\n    !options.smartWalletOptions.overrides?.entrypointAddress\n  ) {\n    const entrypointAddress = await getEntrypointFromFactory(\n      options.smartWalletOptions.factoryAddress,\n      options.client,\n      options.smartWalletOptions.chain,\n    );\n    if (entrypointAddress) {\n      options.smartWalletOptions.overrides = {\n        ...options.smartWalletOptions.overrides,\n        entrypointAddress,\n      };\n    }\n  }\n  const unsignedUserOp = await prepareUserOp({\n    transactions: options.transactions,\n    adminAccount: options.adminAccount,\n    client: options.client,\n    smartWalletOptions: options.smartWalletOptions,\n    waitForDeployment: options.waitForDeployment,\n    isDeployedOverride: options.isDeployedOverride,\n  });\n  const signedUserOp = await signUserOp({\n    client: options.client,\n    chain: options.smartWalletOptions.chain,\n    adminAccount: options.adminAccount,\n    entrypointAddress: options.smartWalletOptions.overrides?.entrypointAddress,\n    userOp: unsignedUserOp,\n  });\n  return signedUserOp;\n}\n\n/**\n * Prepare a user operation for signing.\n * @param options - The options for preparing the user operation\n * @returns - The prepared user operation\n * @example\n * ```ts\n * import { prepareUserOp } from \"thirdweb/wallets/smart\";\n *\n * const userOp = await prepareUserOp({\n *  transactions,\n *  adminAccount,\n *  client,\n *  smartWalletOptions,\n * });\n * ```\n *\n * You can then sign the user operation with signUserOp(). and send it to the bundler with bundlerUserOp().\n * @walletUtils\n */\nexport async function prepareUserOp(options: {\n  transactions: PreparedTransaction[];\n  adminAccount: Account;\n  client: ThirdwebClient;\n  smartWalletOptions: SmartWalletOptions;\n  waitForDeployment?: boolean;\n  isDeployedOverride?: boolean;\n}) {\n  const config = options.smartWalletOptions;\n  const factoryContract = getContract({\n    address:\n      config.factoryAddress ||\n      getDefaultAccountFactory(config.overrides?.entrypointAddress),\n    chain: config.chain,\n    client: options.client,\n  });\n  const accountAddress = await predictAddress({\n    factoryContract,\n    adminAddress: options.adminAccount.address,\n    predictAddressOverride: config.overrides?.predictAddress,\n    accountSalt: config.overrides?.accountSalt,\n    accountAddress: config.overrides?.accountAddress,\n  });\n  const accountContract = getContract({\n    address: accountAddress,\n    chain: config.chain,\n    client: options.client,\n  });\n\n  let executeTx: PreparedTransaction;\n  if (options.transactions.length === 1) {\n    const tx = options.transactions[0] as PreparedTransaction;\n    // for single tx, simulate fully\n    const serializedTx = await toSerializableTransaction({\n      transaction: tx,\n      from: accountAddress,\n    });\n    executeTx = prepareExecute({\n      accountContract,\n      transaction: serializedTx,\n      executeOverride: config.overrides?.execute,\n    });\n  } else {\n    // for multiple txs, we can't simulate, just encode\n    const serializedTxs = await Promise.all(\n      options.transactions.map(async (tx) => {\n        const [data, to, value] = await Promise.all([\n          encode(tx),\n          resolvePromisedValue(tx.to),\n          resolvePromisedValue(tx.value),\n        ]);\n        return {\n          data,\n          to,\n          value,\n          chainId: tx.chain.id,\n        };\n      }),\n    );\n    executeTx = prepareBatchExecute({\n      accountContract,\n      transactions: serializedTxs,\n      executeBatchOverride: config.overrides?.executeBatch,\n    });\n  }\n\n  return createUnsignedUserOp({\n    transaction: executeTx,\n    factoryContract,\n    accountContract,\n    adminAddress: options.adminAccount.address,\n    sponsorGas: \"sponsorGas\" in config ? config.sponsorGas : config.gasless,\n    overrides: config.overrides,\n    waitForDeployment: options.waitForDeployment,\n    isDeployedOverride: options.isDeployedOverride,\n  });\n}\n\nasync function waitForAccountDeployed(accountContract: ThirdwebContract) {\n  const startTime = Date.now();\n  while (isAccountDeploying(accountContract)) {\n    if (Date.now() - startTime > 60000) {\n      clearAccountDeploying(accountContract); // clear the flag so it doesnt stay stuck in this state\n      throw new Error(\n        \"Account deployment is taking too long (over 1 minute). Please try again.\",\n      );\n    }\n    await new Promise((resolve) => setTimeout(resolve, 500));\n  }\n}\n", "import { decodeErrorR<PERSON>ult } from \"viem\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport { getContract } from \"../../../contract/contract.js\";\nimport { parseEventLogs } from \"../../../event/actions/parse-logs.js\";\nimport { userOperationRevertReasonEvent } from \"../../../extensions/erc4337/__generated__/IEntryPoint/events/UserOperationRevertReason.js\";\nimport { postOpRevertReasonEvent } from \"../../../extensions/erc4337/__generated__/IEntryPoint_v07/events/PostOpRevertReason.js\";\nimport type { ExecuteWithSigParams } from \"../../../extensions/erc7702/__generated__/MinimalAccount/write/executeWithSig.js\";\nimport type { SignedAuthorization } from \"../../../transaction/actions/eip7702/authorization.js\";\nimport type { PreparedTransaction } from \"../../../transaction/prepare-transaction.js\";\nimport type { SerializableTransaction } from \"../../../transaction/serialize-transaction.js\";\nimport type { TransactionReceipt } from \"../../../transaction/types.js\";\nimport { isContractDeployed } from \"../../../utils/bytecode/is-contract-deployed.js\";\nimport { type Hex, hexToBigInt } from \"../../../utils/encoding/hex.js\";\nimport { getClientFetch } from \"../../../utils/fetch.js\";\nimport { stringify } from \"../../../utils/json.js\";\nimport { toEther } from \"../../../utils/units.js\";\nimport type { Account } from \"../../interfaces/wallet.js\";\nimport { getEntrypointFromFactory } from \"../index.js\";\nimport {\n  type BundlerOptions,\n  type EstimationResult,\n  type GasPriceResult,\n  type PmTransactionData,\n  type SmartWalletOptions,\n  type UserOperationReceipt,\n  type UserOperationV06,\n  type UserOperationV07,\n  formatUserOperationReceipt,\n} from \"../types.js\";\nimport { predictSmartAccountAddress } from \"./calls.js\";\nimport {\n  ENTRYPOINT_ADDRESS_v0_6,\n  MANAGED_ACCOUNT_GAS_BUFFER,\n  getDefaultBundlerUrl,\n} from \"./constants.js\";\nimport { prepareUserOp } from \"./userop.js\";\nimport { hexlifyUserOp } from \"./utils.js\";\n\n/**\n * Bundle a user operation.\n * @param args - The options for bundling a user operation.\n * @returns The bundle hash of the user operation.\n * @example\n * ```ts\n * import { bundleUserOp } from \"thirdweb/wallets/smart\";\n *\n * const userOpHash = await bundleUserOp({\n *  userOp,\n *  options,\n * });\n * ```\n * @walletUtils\n */\nexport async function bundleUserOp(args: {\n  userOp: UserOperationV06 | UserOperationV07;\n  options: BundlerOptions;\n}): Promise<Hex> {\n  return sendBundlerRequest({\n    ...args,\n    operation: \"eth_sendUserOperation\",\n    params: [\n      hexlifyUserOp(args.userOp),\n      args.options.entrypointAddress ?? ENTRYPOINT_ADDRESS_v0_6,\n    ],\n  });\n}\n\n/**\n * Estimate the gas cost of a user operation.\n * @param args - The options for estimating the gas cost of a user operation.\n * @returns The estimated gas cost of the user operation.\n * @example\n * ```ts\n * import { estimateUserOpGas } from \"thirdweb/wallets/smart\";\n *\n * const gasCost = await estimateUserOpGas({\n *  userOp,\n *  options,\n * });\n * ```\n * @walletUtils\n */\nexport async function estimateUserOpGas(\n  args: {\n    userOp: UserOperationV06 | UserOperationV07;\n    options: BundlerOptions;\n  },\n  stateOverrides?: {\n    [x: string]: {\n      stateDiff: {\n        [x: string]: `0x${string}`;\n      };\n    };\n  },\n): Promise<EstimationResult> {\n  const res = await sendBundlerRequest({\n    ...args,\n    operation: \"eth_estimateUserOperationGas\",\n    params: [\n      hexlifyUserOp(args.userOp),\n      args.options.entrypointAddress ?? ENTRYPOINT_ADDRESS_v0_6,\n      stateOverrides ?? {},\n    ],\n  });\n\n  // add gas buffer for managed account factory delegate calls\n  return {\n    preVerificationGas: hexToBigInt(res.preVerificationGas),\n    verificationGas:\n      res.verificationGas !== undefined\n        ? hexToBigInt(res.verificationGas)\n        : undefined,\n    verificationGasLimit: hexToBigInt(res.verificationGasLimit),\n    callGasLimit: hexToBigInt(res.callGasLimit) + MANAGED_ACCOUNT_GAS_BUFFER,\n    paymasterVerificationGasLimit:\n      res.paymasterVerificationGasLimit !== undefined\n        ? hexToBigInt(res.paymasterVerificationGasLimit)\n        : undefined,\n    paymasterPostOpGasLimit:\n      res.paymasterPostOpGasLimit !== undefined\n        ? hexToBigInt(res.paymasterPostOpGasLimit)\n        : undefined,\n  };\n}\n\n/**\n * Estimate the gas cost of a user operation.\n * @param args - The options for estimating the gas cost of a user operation.\n * @returns The estimated gas cost of the user operation.\n * @example\n * ```ts\n * import { estimateUserOpGasCost } from \"thirdweb/wallets/smart\";\n *\n * const gasCost = await estimateUserOpGasCost({\n *  transactions,\n *  adminAccount,\n *  client,\n *  smartWalletOptions,\n * });\n * ```\n * @walletUtils\n */\nexport async function estimateUserOpGasCost(args: {\n  transactions: PreparedTransaction[];\n  adminAccount: Account;\n  client: ThirdwebClient;\n  smartWalletOptions: SmartWalletOptions;\n}) {\n  // if factory is passed, but no entrypoint, try to resolve entrypoint from factory\n  if (\n    args.smartWalletOptions.factoryAddress &&\n    !args.smartWalletOptions.overrides?.entrypointAddress\n  ) {\n    const entrypointAddress = await getEntrypointFromFactory(\n      args.smartWalletOptions.factoryAddress,\n      args.client,\n      args.smartWalletOptions.chain,\n    );\n    if (entrypointAddress) {\n      args.smartWalletOptions.overrides = {\n        ...args.smartWalletOptions.overrides,\n        entrypointAddress,\n      };\n    }\n  }\n\n  const userOp = await prepareUserOp({\n    transactions: args.transactions,\n    adminAccount: args.adminAccount,\n    client: args.client,\n    smartWalletOptions: args.smartWalletOptions,\n    isDeployedOverride: await isContractDeployed(\n      getContract({\n        address: await predictSmartAccountAddress({\n          adminAddress: args.adminAccount.address,\n          factoryAddress: args.smartWalletOptions.factoryAddress,\n          chain: args.smartWalletOptions.chain,\n          client: args.client,\n        }),\n        chain: args.smartWalletOptions.chain,\n        client: args.client,\n      }),\n    ),\n    waitForDeployment: false,\n  });\n\n  let gasLimit = 0n;\n  if (\"paymasterVerificationGasLimit\" in userOp) {\n    // v0.7\n    gasLimit =\n      BigInt(userOp.paymasterVerificationGasLimit ?? 0) +\n      BigInt(userOp.paymasterPostOpGasLimit ?? 0) +\n      BigInt(userOp.verificationGasLimit ?? 0) +\n      BigInt(userOp.preVerificationGas ?? 0) +\n      BigInt(userOp.callGasLimit ?? 0);\n  } else {\n    // v0.6\n    gasLimit =\n      BigInt(userOp.verificationGasLimit ?? 0) +\n      BigInt(userOp.preVerificationGas ?? 0) +\n      BigInt(userOp.callGasLimit ?? 0);\n  }\n\n  const gasCost = gasLimit * (userOp.maxFeePerGas ?? 0n);\n\n  return {\n    ether: toEther(gasCost),\n    wei: gasCost,\n  };\n}\n\n/**\n * Get the gas fees of a user operation.\n * @param args - The options for getting the gas price of a user operation.\n * @returns The gas price of the user operation.\n * @example\n * ```ts\n * import { getUserOpGasPrice } from \"thirdweb/wallets/smart\";\n *\n * const fees = await getUserOpGasPrice({\n *  options,\n * });\n * ```\n * @walletUtils\n */\nexport async function getUserOpGasFees(args: {\n  options: BundlerOptions;\n}): Promise<GasPriceResult> {\n  const res = await sendBundlerRequest({\n    ...args,\n    operation: \"thirdweb_getUserOperationGasPrice\",\n    params: [],\n  });\n\n  return {\n    maxPriorityFeePerGas: hexToBigInt(res.maxPriorityFeePerGas),\n    maxFeePerGas: hexToBigInt(res.maxFeePerGas),\n  };\n}\n\n/**\n * Get the receipt of a user operation.\n * @param args - The options for getting the receipt of a user operation.\n * @returns The receipt of the user operation.\n * @example\n * ```ts\n * import { getUserOpReceipt } from \"thirdweb/wallets/smart\";\n *\n * const receipt = await getUserOpReceipt({\n *  client,\n *  chain,\n *  userOpHash,\n * });\n * ```\n * @walletUtils\n */\nexport async function getUserOpReceipt(\n  args: BundlerOptions & {\n    userOpHash: Hex;\n  },\n): Promise<TransactionReceipt | undefined> {\n  const res = await getUserOpReceiptRaw(args);\n\n  if (!res) {\n    return undefined;\n  }\n\n  if (res.success === false) {\n    // parse revert reason\n    const logs = parseEventLogs({\n      events: [userOperationRevertReasonEvent(), postOpRevertReasonEvent()],\n      logs: res.logs,\n    });\n    const revertReason = logs[0]?.args?.revertReason;\n    if (!revertReason) {\n      throw new Error(\n        `UserOp failed at txHash: ${res.receipt.transactionHash}`,\n      );\n    }\n    const revertMsg = decodeErrorResult({\n      data: revertReason,\n    });\n    throw new Error(\n      `UserOp failed with reason: '${revertMsg.args.join(\",\")}' at txHash: ${\n        res.receipt.transactionHash\n      }`,\n    );\n  }\n  return res.receipt;\n}\n\n/**\n * Get the receipt of a user operation.\n * @param args - The options for getting the receipt of a user operation.\n * @returns The raw receipt of the user operation.\n * @example\n * ```ts\n * import { getUserOpReceiptRaw } from \"thirdweb/wallets/smart\";\n *\n * const receipt = await getUserOpReceiptRaw({\n *  client,\n *  chain,\n *  userOpHash,\n * });\n * ```\n * @walletUtils\n */\nexport async function getUserOpReceiptRaw(\n  args: BundlerOptions & {\n    userOpHash: Hex;\n  },\n): Promise<UserOperationReceipt | undefined> {\n  const res = await sendBundlerRequest({\n    options: args,\n    operation: \"eth_getUserOperationReceipt\",\n    params: [args.userOpHash],\n  });\n  if (!res) {\n    return undefined;\n  }\n  return formatUserOperationReceipt(res as UserOperationReceipt);\n}\n\n/**\n * @internal\n */\nexport async function getZkPaymasterData(args: {\n  options: BundlerOptions;\n  transaction: SerializableTransaction;\n}): Promise<PmTransactionData> {\n  const res = await sendBundlerRequest({\n    options: args.options,\n    operation: \"zk_paymasterData\",\n    params: [args.transaction],\n  });\n\n  return {\n    paymaster: res.paymaster,\n    paymasterInput: res.paymasterInput,\n  };\n}\n\n/**\n * @internal\n */\nexport async function executeWithSignature(args: {\n  eoaAddress: `0x${string}`;\n  wrappedCalls: ExecuteWithSigParams[\"wrappedCalls\"];\n  signature: `0x${string}`;\n  authorization: SignedAuthorization | undefined;\n  options: BundlerOptions;\n}): Promise<{ transactionId: string }> {\n  const res = await sendBundlerRequest({\n    ...args,\n    operation: \"tw_execute\",\n    params: [\n      args.eoaAddress,\n      args.wrappedCalls,\n      args.signature,\n      args.authorization,\n    ],\n  });\n\n  return {\n    transactionId: res.queueId,\n  };\n}\n\n/**\n * @internal\n */\nexport async function getQueuedTransactionHash(args: {\n  transactionId: string;\n  options: BundlerOptions;\n}): Promise<{ transactionHash: Hex }> {\n  const res = await sendBundlerRequest({\n    ...args,\n    operation: \"tw_getTransactionHash\",\n    params: [args.transactionId],\n  });\n  return {\n    transactionHash: res.transactionHash,\n  };\n}\n\nexport async function broadcastZkTransaction(args: {\n  options: BundlerOptions;\n  transaction: SerializableTransaction;\n  signedTransaction: Hex;\n}): Promise<{ transactionHash: Hex }> {\n  const res = await sendBundlerRequest({\n    options: args.options,\n    operation: \"zk_broadcastTransaction\",\n    params: [\n      {\n        ...args.transaction,\n        signedTransaction: args.signedTransaction,\n      },\n    ],\n  });\n\n  return {\n    transactionHash: res.transactionHash,\n  };\n}\n\nasync function sendBundlerRequest(args: {\n  options: BundlerOptions;\n  operation:\n    | \"eth_estimateUserOperationGas\"\n    | \"eth_sendUserOperation\"\n    | \"eth_getUserOperationReceipt\"\n    | \"thirdweb_getUserOperationGasPrice\"\n    | \"zk_paymasterData\"\n    | \"zk_broadcastTransaction\"\n    | \"tw_execute\"\n    | \"tw_getTransactionHash\";\n  // biome-ignore lint/suspicious/noExplicitAny: TODO: fix any\n  params: any[];\n}) {\n  const { options, operation, params } = args;\n\n  const bundlerUrl = options.bundlerUrl ?? getDefaultBundlerUrl(options.chain);\n  const fetchWithHeaders = getClientFetch(options.client);\n  const response = await fetchWithHeaders(bundlerUrl, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: stringify({\n      jsonrpc: \"2.0\",\n      id: 1,\n      method: operation,\n      params,\n    }),\n  });\n  const res = await response.json();\n  if (!response.ok || res.error) {\n    let error = res.error || response.statusText;\n    if (typeof error === \"object\") {\n      error = stringify(error);\n    }\n    const code = res.code || \"UNKNOWN\";\n\n    throw new Error(\n      `${operation} error: ${error}\nStatus: ${response.status}\nCode: ${code}`,\n    );\n  }\n\n  return res.result;\n}\n", "import type { Wallet } from \"../interfaces/wallet.js\";\n\n/**\n * Checks if the given wallet is a smart wallet.\n *\n * @param {Wallet} wallet - The wallet to check.\n * @returns {boolean} True if the wallet is a smart wallet, false otherwise.\n * @internal\n */\nexport function isSmartWallet(wallet: Wallet): boolean {\n  if (wallet.id === \"smart\") {\n    return true;\n  }\n\n  const config = wallet.getConfig();\n  if (!!config && \"smartAccount\" in config && !!config.smartAccount) {\n    return true;\n  }\n\n  return false;\n}\n", "import type * as ox__TypedData from \"ox/TypedData\";\nimport { trackTransaction } from \"../../analytics/track/transaction.js\";\nimport type { Chain } from \"../../chains/types.js\";\nimport { getCached<PERSON><PERSON><PERSON> } from \"../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { type ThirdwebContract, getContract } from \"../../contract/contract.js\";\nimport { allowance } from \"../../extensions/erc20/__generated__/IERC20/read/allowance.js\";\nimport { approve } from \"../../extensions/erc20/write/approve.js\";\nimport {\n  addSessionKey,\n  shouldUpdateSessionKey,\n} from \"../../extensions/erc4337/account/addSessionKey.js\";\nimport { sendTransaction } from \"../../transaction/actions/send-transaction.js\";\nimport { toSerializableTransaction } from \"../../transaction/actions/to-serializable-transaction.js\";\nimport type { WaitForReceiptOptions } from \"../../transaction/actions/wait-for-tx-receipt.js\";\nimport {\n  populateEip712Transaction,\n  signEip712Transaction,\n} from \"../../transaction/actions/zksync/send-eip712-transaction.js\";\nimport type { PreparedTransaction } from \"../../transaction/prepare-transaction.js\";\nimport { readContract } from \"../../transaction/read-contract.js\";\nimport { getAddress } from \"../../utils/address.js\";\nimport { isZkSyncChain } from \"../../utils/any-evm/zksync/isZkSyncChain.js\";\nimport type { Hex } from \"../../utils/encoding/hex.js\";\nimport { resolvePromisedValue } from \"../../utils/promise/resolve-promised-value.js\";\nimport { parseTypedData } from \"../../utils/signatures/helpers/parse-typed-data.js\";\nimport { type SignableMessage, maxUint96 } from \"../../utils/types.js\";\nimport type { Account, SendTransactionOption } from \"../interfaces/wallet.js\";\nimport {\n  broadcastZkTransaction,\n  bundleUserOp,\n  getZkPaymasterData,\n} from \"./lib/bundler.js\";\nimport {\n  predictAddress,\n  prepareBatchExecute,\n  prepareExecute,\n} from \"./lib/calls.js\";\nimport {\n  ENTRYPOINT_ADDRESS_v0_6,\n  ENTRYPOINT_ADDRESS_v0_7,\n  getDefaultAccountFactory,\n  getEntryPointVersion,\n} from \"./lib/constants.js\";\nimport {\n  clearAccountDeploying,\n  createUnsignedUserOp,\n  signUserOp,\n  waitForUserOpReceipt,\n} from \"./lib/userop.js\";\nimport type {\n  BundlerOptions,\n  PaymasterResult,\n  SmartAccountOptions,\n  SmartWalletConnectionOptions,\n  SmartWalletOptions,\n  TokenPaymasterConfig,\n  UserOperationV06,\n  UserOperationV07,\n} from \"./types.js\";\nexport { isSmartWallet } from \"./is-smart-wallet.js\";\n\n/**\n * For in-app wallets, the smart wallet creation is implicit so we track these to be able to retrieve the personal account for a smart account on the wallet API.\n * Note: We have to go account to account here and NOT wallet to account because the smart wallet itself is never exposed to the in-app wallet, only the account.\n * @internal\n */\nconst adminAccountToSmartAccountMap = new WeakMap<Account, Account>();\nconst smartAccountToAdminAccountMap = new WeakMap<Account, Account>();\n\n/**\n * @internal\n */\nexport async function connectSmartAccount(\n  connectionOptions: SmartWalletConnectionOptions,\n  creationOptions: SmartWalletOptions,\n): Promise<[Account, Chain]> {\n  const { personalAccount, client, chain: connectChain } = connectionOptions;\n\n  if (!personalAccount) {\n    throw new Error(\n      \"No personal account provided for smart account connection\",\n    );\n  }\n\n  const options = creationOptions;\n  const chain = connectChain ?? options.chain;\n  const sponsorGas =\n    \"gasless\" in options ? options.gasless : options.sponsorGas;\n  if (await isZkSyncChain(chain)) {\n    return [\n      createZkSyncAccount({\n        creationOptions,\n        connectionOptions,\n        chain,\n        sponsorGas,\n      }),\n      chain,\n    ];\n  }\n\n  // if factory is passed, but no entrypoint, try to resolve entrypoint from factory\n  if (options.factoryAddress && !options.overrides?.entrypointAddress) {\n    const entrypointAddress = await getEntrypointFromFactory(\n      options.factoryAddress,\n      client,\n      chain,\n    );\n    if (entrypointAddress) {\n      options.overrides = {\n        ...options.overrides,\n        entrypointAddress,\n      };\n    }\n  }\n\n  if (\n    options.overrides?.tokenPaymaster &&\n    !options.overrides?.entrypointAddress\n  ) {\n    // if token paymaster is set, but no entrypoint address, set the entrypoint address to v0.7\n    options.overrides = {\n      ...options.overrides,\n      entrypointAddress: ENTRYPOINT_ADDRESS_v0_7,\n    };\n  }\n\n  const factoryAddress =\n    options.factoryAddress ??\n    getDefaultAccountFactory(options.overrides?.entrypointAddress);\n\n  const factoryContract = getContract({\n    client: client,\n    address: factoryAddress,\n    chain: chain,\n  });\n\n  const accountAddress = await predictAddress({\n    factoryContract,\n    adminAddress: personalAccount.address,\n    predictAddressOverride: options.overrides?.predictAddress,\n    accountSalt: options.overrides?.accountSalt,\n    accountAddress: options.overrides?.accountAddress,\n  })\n    .then((address) => address)\n    .catch((err) => {\n      throw new Error(\n        `Failed to get account address with factory contract ${factoryContract.address} on chain ID ${chain.id}: ${err?.message || \"unknown error\"}`,\n        { cause: err },\n      );\n    });\n\n  const accountContract = getContract({\n    client,\n    address: accountAddress,\n    chain,\n  });\n\n  const account = await createSmartAccount({\n    ...options,\n    chain,\n    sponsorGas,\n    personalAccount,\n    accountContract,\n    factoryContract,\n    client,\n  });\n\n  adminAccountToSmartAccountMap.set(personalAccount, account);\n  smartAccountToAdminAccountMap.set(account, personalAccount);\n\n  if (options.sessionKey) {\n    if (\n      await shouldUpdateSessionKey({\n        accountContract,\n        sessionKeyAddress: options.sessionKey.address,\n        newPermissions: options.sessionKey.permissions,\n      })\n    ) {\n      const transaction = addSessionKey({\n        account: personalAccount,\n        contract: accountContract,\n        permissions: options.sessionKey.permissions,\n        sessionKeyAddress: options.sessionKey.address,\n      });\n      await sendTransaction({\n        account: account,\n        transaction,\n      });\n    }\n  }\n\n  return [account, chain] as const;\n}\n\n/**\n * @internal\n */\nexport async function disconnectSmartAccount(account: Account): Promise<void> {\n  // look up the personalAccount for the smart wallet\n  const personalAccount = smartAccountToAdminAccountMap.get(account);\n  if (personalAccount) {\n    // remove the mappings\n    adminAccountToSmartAccountMap.delete(personalAccount);\n    smartAccountToAdminAccountMap.delete(account);\n  }\n}\n\nasync function createSmartAccount(\n  options: SmartAccountOptions,\n): Promise<Account> {\n  const erc20Paymaster = options.overrides?.tokenPaymaster;\n  if (erc20Paymaster) {\n    if (\n      getEntryPointVersion(\n        options.overrides?.entrypointAddress || ENTRYPOINT_ADDRESS_v0_6,\n      ) !== \"v0.7\"\n    ) {\n      throw new Error(\n        \"Token paymaster is only supported for entrypoint version v0.7\",\n      );\n    }\n  }\n\n  let accountContract = options.accountContract;\n  const account: Account = {\n    address: getAddress(accountContract.address),\n    async sendTransaction(transaction: SendTransactionOption) {\n      // if erc20 paymaster - check allowance and approve if needed\n      let paymasterOverride:\n        | undefined\n        | ((\n            userOp: UserOperationV06 | UserOperationV07,\n          ) => Promise<PaymasterResult>) = undefined;\n      if (erc20Paymaster) {\n        await approveERC20({\n          accountContract,\n          erc20Paymaster,\n          options,\n        });\n        const paymasterCallback = async (): Promise<PaymasterResult> => {\n          return {\n            paymaster: erc20Paymaster.paymasterAddress as Hex,\n            paymasterData: \"0x\",\n          };\n        };\n        paymasterOverride = options.overrides?.paymaster || paymasterCallback;\n      } else {\n        paymasterOverride = options.overrides?.paymaster;\n      }\n\n      // If this transaction is for a different chain than the initial one, get the account contract for that chain\n      if (transaction.chainId !== accountContract.chain.id) {\n        accountContract = getContract({\n          address: account.address,\n          chain: getCachedChain(transaction.chainId),\n          client: options.client,\n        });\n      }\n\n      const executeTx = prepareExecute({\n        accountContract: accountContract,\n        transaction,\n        executeOverride: options.overrides?.execute,\n      });\n\n      const chain = getCachedChain(transaction.chainId);\n      const result = await _sendUserOp({\n        executeTx,\n        options: {\n          ...options,\n          chain,\n          accountContract,\n          overrides: {\n            ...options.overrides,\n            paymaster: paymasterOverride,\n          },\n        },\n      });\n      trackTransaction({\n        client: options.client,\n        chainId: chain.id,\n        transactionHash: result.transactionHash,\n        walletAddress: options.accountContract.address,\n        walletType: \"smart\",\n        contractAddress: transaction.to ?? undefined,\n      });\n      return result;\n    },\n    async sendBatchTransaction(transactions: SendTransactionOption[]) {\n      const executeTx = prepareBatchExecute({\n        accountContract,\n        transactions,\n        executeBatchOverride: options.overrides?.executeBatch,\n      });\n      if (transactions.length === 0) {\n        throw new Error(\"No transactions to send\");\n      }\n      const firstTx = transactions[0];\n      if (!firstTx) {\n        throw new Error(\"No transactions to send\");\n      }\n      const chain = getCachedChain(firstTx.chainId);\n      const result = await _sendUserOp({\n        executeTx,\n        options: {\n          ...options,\n          chain,\n          accountContract,\n        },\n      });\n      trackTransaction({\n        client: options.client,\n        chainId: chain.id,\n        transactionHash: result.transactionHash,\n        walletAddress: options.accountContract.address,\n        walletType: \"smart\",\n        contractAddress: transactions[0]?.to ?? undefined,\n      });\n      return result;\n    },\n    async signMessage({ message }: { message: SignableMessage }) {\n      if (options.overrides?.signMessage) {\n        return options.overrides.signMessage({\n          adminAccount: options.personalAccount,\n          factoryContract: options.factoryContract,\n          accountContract,\n          message,\n        });\n      }\n\n      const { smartAccountSignMessage } = await import(\"./lib/signing.js\");\n      return smartAccountSignMessage({\n        accountContract,\n        factoryContract: options.factoryContract,\n        options,\n        message,\n      });\n    },\n    async signTypedData<\n      const typedData extends ox__TypedData.TypedData | Record<string, unknown>,\n      primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n    >(typedData: ox__TypedData.Definition<typedData, primaryType>) {\n      if (options.overrides?.signTypedData) {\n        return options.overrides.signTypedData({\n          adminAccount: options.personalAccount,\n          factoryContract: options.factoryContract,\n          accountContract,\n          typedData,\n        });\n      }\n\n      const { smartAccountSignTypedData } = await import(\"./lib/signing.js\");\n      return smartAccountSignTypedData({\n        accountContract,\n        factoryContract: options.factoryContract,\n        options,\n        typedData,\n      });\n    },\n    async onTransactionRequested(transaction) {\n      return options.personalAccount.onTransactionRequested?.(transaction);\n    },\n  };\n  return account;\n}\n\nasync function approveERC20(args: {\n  accountContract: ThirdwebContract;\n  options: SmartAccountOptions;\n  erc20Paymaster: TokenPaymasterConfig;\n}) {\n  const { accountContract, erc20Paymaster, options } = args;\n  const tokenAddress = erc20Paymaster.tokenAddress;\n  const tokenContract = getContract({\n    address: tokenAddress,\n    chain: accountContract.chain,\n    client: accountContract.client,\n  });\n  const accountAllowance = await allowance({\n    contract: tokenContract,\n    owner: accountContract.address,\n    spender: erc20Paymaster.paymasterAddress,\n  });\n\n  if (accountAllowance > 0n) {\n    return;\n  }\n\n  const approveTx = approve({\n    contract: tokenContract,\n    spender: erc20Paymaster.paymasterAddress,\n    amountWei: maxUint96 - 1n,\n  });\n  const transaction = await toSerializableTransaction({\n    transaction: approveTx,\n    from: accountContract.address,\n  });\n  const executeTx = prepareExecute({\n    accountContract,\n    transaction,\n    executeOverride: options.overrides?.execute,\n  });\n  await _sendUserOp({\n    executeTx,\n    options: {\n      ...options,\n      overrides: {\n        ...options.overrides,\n        tokenPaymaster: undefined,\n      },\n    },\n  });\n}\n\nfunction createZkSyncAccount(args: {\n  creationOptions: SmartWalletOptions;\n  connectionOptions: SmartWalletConnectionOptions;\n  chain: Chain;\n  sponsorGas: boolean;\n}): Account {\n  const { creationOptions, connectionOptions, chain } = args;\n  const account: Account = {\n    address: getAddress(connectionOptions.personalAccount.address),\n    async sendTransaction(transaction: SendTransactionOption) {\n      // override passed tx, we have to refetch gas and fees always\n      const prepTx = {\n        data: transaction.data,\n        to: transaction.to ?? undefined,\n        value: transaction.value ?? 0n,\n        chain: getCachedChain(transaction.chainId),\n        client: connectionOptions.client,\n        eip712: transaction.eip712,\n      };\n\n      let serializableTransaction = await populateEip712Transaction({\n        account,\n        transaction: prepTx,\n      });\n\n      if (args.sponsorGas && !serializableTransaction.paymaster) {\n        // get paymaster input\n        const pmData = await getZkPaymasterData({\n          options: {\n            client: connectionOptions.client,\n            chain,\n            bundlerUrl: creationOptions.overrides?.bundlerUrl,\n            entrypointAddress: creationOptions.overrides?.entrypointAddress,\n          },\n          transaction: serializableTransaction,\n        });\n        serializableTransaction = {\n          ...serializableTransaction,\n          ...pmData,\n        };\n      }\n\n      // sign\n      const signedTransaction = await signEip712Transaction({\n        account,\n        chainId: chain.id,\n        eip712Transaction: serializableTransaction,\n      });\n\n      // broadcast via bundler\n      const txHash = await broadcastZkTransaction({\n        options: {\n          client: connectionOptions.client,\n          chain,\n          bundlerUrl: creationOptions.overrides?.bundlerUrl,\n          entrypointAddress: creationOptions.overrides?.entrypointAddress,\n        },\n        transaction: serializableTransaction,\n        signedTransaction,\n      });\n\n      trackTransaction({\n        client: connectionOptions.client,\n        chainId: chain.id,\n        transactionHash: txHash.transactionHash,\n        walletAddress: account.address,\n        walletType: \"smart\",\n        contractAddress: transaction.to ?? undefined,\n      });\n\n      return {\n        transactionHash: txHash.transactionHash,\n        client: connectionOptions.client,\n        chain: chain,\n      };\n    },\n    async signMessage({ message }: { message: SignableMessage }) {\n      return connectionOptions.personalAccount.signMessage({ message });\n    },\n    async signTypedData<\n      const typedData extends ox__TypedData.TypedData | Record<string, unknown>,\n      primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n    >(_typedData: ox__TypedData.Definition<typedData, primaryType>) {\n      const typedData = parseTypedData(_typedData);\n      return connectionOptions.personalAccount.signTypedData(typedData);\n    },\n    async onTransactionRequested(transaction) {\n      return connectionOptions.personalAccount.onTransactionRequested?.(\n        transaction,\n      );\n    },\n  };\n  return account;\n}\n\nasync function _sendUserOp(args: {\n  executeTx: PreparedTransaction;\n  options: SmartAccountOptions;\n}): Promise<WaitForReceiptOptions> {\n  const { executeTx, options } = args;\n  try {\n    const unsignedUserOp = await createUnsignedUserOp({\n      transaction: executeTx,\n      factoryContract: options.factoryContract,\n      accountContract: options.accountContract,\n      adminAddress: options.personalAccount.address,\n      sponsorGas: options.sponsorGas,\n      overrides: options.overrides,\n    });\n    const signedUserOp = await signUserOp({\n      client: options.client,\n      chain: options.chain,\n      adminAccount: options.personalAccount,\n      entrypointAddress: options.overrides?.entrypointAddress,\n      userOp: unsignedUserOp,\n    });\n    const bundlerOptions: BundlerOptions = {\n      chain: options.chain,\n      client: options.client,\n      bundlerUrl: options.overrides?.bundlerUrl,\n      entrypointAddress: options.overrides?.entrypointAddress,\n    };\n    const userOpHash = await bundleUserOp({\n      options: bundlerOptions,\n      userOp: signedUserOp,\n    });\n    // wait for tx receipt rather than return the userOp hash\n    const receipt = await waitForUserOpReceipt({\n      ...bundlerOptions,\n      userOpHash,\n    });\n\n    trackTransaction({\n      client: options.client,\n      chainId: options.chain.id,\n      transactionHash: receipt.transactionHash,\n      walletAddress: options.accountContract.address,\n      walletType: \"smart\",\n      contractAddress: await resolvePromisedValue(executeTx.to ?? undefined),\n    });\n\n    return {\n      client: options.client,\n      chain: options.chain,\n      transactionHash: receipt.transactionHash,\n    };\n  } finally {\n    // reset the isDeploying flag after every transaction or error\n    clearAccountDeploying(options.accountContract);\n  }\n}\n\nexport async function getEntrypointFromFactory(\n  factoryAddress: string,\n  client: ThirdwebClient,\n  chain: Chain,\n) {\n  const factoryContract = getContract({\n    address: factoryAddress,\n    client,\n    chain,\n  });\n  try {\n    const entrypointAddress = await readContract({\n      contract: factoryContract,\n      method: \"function entrypoint() public view returns (address)\",\n    });\n    return entrypointAddress;\n  } catch {\n    return undefined;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCM,SAAUA,SAAQ,SAA8C;AACpE,SAAO,QAAiB;IACtB,UAAU,QAAQ;IAClB,aAAa,YAAW;AACtB,UAAI;AACJ,UAAI,YAAY,SAAS;AAEvB,cAAM,EAAE,SAAQ,IAAK,MAAM,OAAO,wBAAqB;AAEvD,cAAM,IAAI,MAAM,SAAS,OAAO,EAAE,MAAM,MAAM,EAAE;AAEhD,iBAAS,QAAQ,QAAQ,OAAO,SAAQ,GAAI,CAAC;MAC/C,OAAO;AACL,iBAAS,QAAQ;MACnB;AACA,aAAO;QACL,SAAS,QAAQ;QACjB,OAAO;QACP,WAAW;UACT,YAAY;YACV,WAAW;YACX,cAAc,QAAQ,SAAS;;;;IAIvC;GACD;AACH;;;ACpDO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAoGd,eAAsB,wBACpB,SAA8D;AAE9D,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,MAAM;GACxB;AACH;;;ACzHO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa,CAAA;AA2Fb,SAAU,wBACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO,CAAC,gBAAgB,KAAK,gBAAgB,SAAS;IACxD;IACA,OAAO,YAAS;AA1LpB;AA0LwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA3LzB;AA2L6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AA5LlB;AA4LsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AA7LvB;AA6L2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AA9L3B;AA8L+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AA/LnC;AAgMO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAjMpB;AAiMwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAlMvB;AAkM2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAnMzB;AAmM6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AApMhC;AAqMO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;ACvMM,SAAU,kBAAe;AAC7B,SAAO,IAAI,KAAK,KAAK,IAAG,IAAK,MAAO,KAAK,KAAK,KAAK,MAAM,EAAE;AAC7D;AAKM,SAAU,cAAc,MAAU;AACtC,SAAO,SAAS,KAAK,MAAM,KAAK,QAAO,IAAK,GAAI,CAAC;AACnD;;;ACLO,IAAM,0BAA0B;EACrC,EAAE,MAAM,UAAU,MAAM,UAAS;EACjC,EAAE,MAAM,WAAW,MAAM,QAAO;EAChC,EAAE,MAAM,mBAAmB,MAAM,YAAW;EAC5C,EAAE,MAAM,kCAAkC,MAAM,UAAS;EACzD,EAAE,MAAM,4BAA4B,MAAM,UAAS;EACnD,EAAE,MAAM,0BAA0B,MAAM,UAAS;EACjD,EAAE,MAAM,6BAA6B,MAAM,UAAS;EACpD,EAAE,MAAM,2BAA2B,MAAM,UAAS;EAClD,EAAE,MAAM,OAAO,MAAM,UAAS;;;;ACNhC,eAAsB,sBAAsB,SAI3C;AACC,QAAM,EAAE,SAAS,UAAU,IAAG,IAAK;AACnC,QAAM,YAAY,MAAM,QAAQ,cAAc;IAC5C,QAAQ;MACN,MAAM;MACN,SAAS;MACT,mBAAmB,SAAS;MAC5B,SAAS,SAAS,MAAM;;IAE1B,aAAa;IACb,OAAO,EAAE,wBAAuB;IAChC,SAAS;GACV;AACD,SAAO,EAAE,KAAK,UAAS;AACzB;AAKA,eAAsB,sBAAsB,SAG3C;AAtCD;AAuCE,QAAM,EAAE,QAAQ,YAAW,IAAK;AAChC,SAAO;IACL,iBACE,YAAY,oBAAoB,MAC5B,CAAC,YAAY,IACb,YAAY;IAClB,gCAAgC,QAC9B,iBAAY,mCAAZ,mBAA4C,eAAc,GAAG;IAE/D,0BAA0B,cACxB,YAAY,4BAA4B,oBAAI,KAAK,CAAC,CAAC;IAErD,wBAAwB,cACtB,YAAY,0BAA0B,gBAAe,CAAE;IAEzD,2BAA2B;IAC3B,yBAAyB,cAAc,gBAAe,CAAE;IACxD,KAAK,MAAM,eAAc;IACzB,SAAS;;IACT,QAAQ;;AAEZ;;;ACFM,SAAU,cACd,SAAqD;AAErD,QAAM,EAAE,UAAU,mBAAmB,SAAS,YAAW,IAAK;AAC9D,SAAO,wBAAwB;IAC7B;IACA,MAAM,cAAW;AACf,YAAM,EAAE,KAAK,UAAS,IAAK,MAAM,sBAAsB;QACrD;QACA;QACA,KAAK,MAAM,sBAAsB;UAC/B,QAAQ;UACR;SACD;OACF;AACD,aAAO,EAAE,WAAW,IAAG;IACzB;GACD;AACH;AA+BA,eAAsB,uBAAuB,MAI5C;AA/GD;AAgHE,QAAM,EAAE,iBAAiB,mBAAmB,eAAc,IAAK;AAG/D,QAAM,kBAAkB,MAAM,mBAAmB,eAAe;AAChE,MAAI,CAAC,iBAAiB;AACpB,WAAO;EACT;AAGA,QAAM,qBAAqB,MAAM,wBAAwB;IACvD,UAAU;IACV,QAAQ;GACT;AAED,MACE,mBAAmB,gBACnB,mBAAmB,eAAe,KAAK,OAAM,oBAAI,KAAI,GAAG,QAAO,IAAK,GAAI,GACxE;AACA,WAAO;EACT;AAGA,MACE,CAAC,kCACC,mBAAmB,iBACnB,eAAe,eAAe,GAEhC;AACA,WAAO;EACT;AAGA,MACE,QAAM,oBAAe,mCAAf,mBAA+C,eAAc,GAAG,IACtE,mBAAmB,gCACnB;AACA,WAAO;EACT;AAEA,SAAO;AACT;AAEA,SAAS,kCACP,gBACA,YAA0B;AAG1B,MACE,eAAe,OACf,eAAe,WAAW,KAC1B,eAAe,CAAC,MAAM,cACtB;AACA,WAAO;EACT;AACA,MAAI,eAAe,KAAK;AACtB,WAAO,WACJ,IAAI,CAAC,WAAW,OAAO,YAAW,CAAE,EACpC,MAAM,CAAC,WACN,eAAe,IAAI,CAAC,MAAM,EAAE,YAAW,CAAE,EAAE,SAAS,MAAM,CAAC;EAEjE;AACA,SAAO;AACT;;;AC3IO,IAAMC,aAAY,MAAM,MAAM;;;ACK/B,SAAU,+BACd,UAAiD,CAAA,GAAE;AAEnD,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;ACRM,SAAU,wBACd,UAA0C,CAAA,GAAE;AAE5C,SAAO,aAAa;IAClB,WACE;IACF;GACD;AACH;;;AC6LM,SAAU,2BACd,kBAAsC;AAEtC,QAAM,EAAE,SAAS,mBAAkB,IAAK;AAExC,QAAM,UAAU;IACd,GAAG;IACH,iBAAiB,mBAAmB;IACpC,aAAa,mBAAmB,cAC5B,OAAO,mBAAmB,WAAW,IACrC;IACJ,iBAAiB,mBAAmB,kBAChC,mBAAmB,kBACnB;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,SAAS,mBAAmB,UACxB,OAAO,mBAAmB,OAAO,IACjC;IACJ,MAAM,mBAAmB;IACzB,IAAI,mBAAmB,KAAK,mBAAmB,KAAK;IACpD,kBAAkB,mBAAmB;IACrC,QAAQ,mBAAmB;IAC3B,MAAM,mBAAmB;;AAG3B,MAAI,mBAAmB;AACrB,YAAQ,eAAe,OAAO,mBAAmB,YAAY;AAC/D,MAAI,mBAAmB;AACrB,YAAQ,cAAc,OAAO,mBAAmB,WAAW;AAE7D,QAAM,gBAAgB;IACpB,GAAG;IACH;IACA,YAAY,iBAAiB;IAC7B,eAAe,OAAO,iBAAiB,aAAa;IACpD,eAAe,OAAO,iBAAiB,aAAa;IACpD,OAAO,OAAO,iBAAiB,KAAK;;AAEtC,SAAO;AACT;;;ACzQO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAMC,cAAa;EACjB;IACE,MAAM;IACN,MAAM;;;AA6FV,eAAsB,SACpB,SAA+C;AAE/C,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,GAAG;GACrC;AACH;;;ACpGO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAKd,IAAMC,cAAa;EACjB;IACE,MAAM;;;AA4FV,eAAsB,cACpB,SAAoD;AAEpD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAC,QAAQ,MAAM;GACxB;AACH;;;AC7JO,IAAMC,eAAc;AAC3B,IAAMC,aAAY;EAChB;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;;AAKd,IAAMC,cAAa;EACjB;IACE,MAAM;;;AA4FV,eAAsBC,eACpB,SAAoD;AAEpD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAACC,cAAaC,YAAWC,WAAU;IAC3C,QAAQ,CAAC,QAAQ,MAAM;GACxB;AACH;;;AC7KA,SAAS,YAAY,uBAAuC;AAC1D,SAAO,sBAAsB,UACzB,OAAO;IACL,sBAAsB;IACtB,sBAAsB,eAAgB;GACvC,IACD;AACN;AAEA,SAAS,oBAAoB,uBAAuC;AAClE,SAAO,OAAO;IACZ,IAAIC,OAAM,OAAO,sBAAsB,oBAAoB,CAAC,GAAG;MAC7D,MAAM;KACP;IACD,IAAIA,OAAM,OAAO,sBAAsB,YAAY,CAAC,GAAG,EAAE,MAAM,GAAE,CAAE;GACpE;AACH;AAEA,SAAS,aAAa,uBAAuC;AAC3D,SAAO,OAAO;IACZ,IAAIA,OAAM,OAAO,sBAAsB,oBAAoB,CAAC,GAAG;MAC7D,MAAM;KACP;IACD,IAAIA,OAAM,OAAO,sBAAsB,YAAY,CAAC,GAAG,EAAE,MAAM,GAAE,CAAE;GACpE;AACH;AAEA,SAAS,oBAAoB,uBAAuC;AAClE,SAAO,sBAAsB,YACzB,OAAO;IACL,sBAAsB;IACtB,IACEA,OACE,OAAO,sBAAsB,iCAAiC,CAAC,CAAC,GAElE;MACE,MAAM;KACP;IAEH,IAAIA,OAAM,OAAO,sBAAsB,2BAA2B,CAAC,CAAC,GAAG;MACrE,MAAM;KACP;IACD,sBAAsB,iBAAkB;GACzC,IACD;AACN;AAEO,IAAM,yBAAyB,CACpC,kBACuB;AACvB,SAAO;IACL,QAAQ,cAAc;IACtB,OAAO,OAAO,cAAc,KAAK;IACjC,UAAU,YAAY,aAAa;IACnC,UAAU,cAAc;IACxB,kBAAkB,oBAAoB,aAAa;IACnD,oBAAoB,OAAO,cAAc,kBAAkB;IAC3D,SAAS,aAAa,aAAa;IACnC,kBAAkB,oBAAoB,aAAa;IACnD,WAAW,cAAc;;AAE7B;;;ACxDO,IAAM,wBAAwB,MAAa;AAChD,QAAM,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAW,CAAC;AAC5D,QAAM,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAW,CAAC;AAC5D,QAAM,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAW,CAAC;AAC5D,QAAM,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAW,CAAC;AAC5D,QAAM,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAW,CAAC;AAC5D,QAAM,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAM,IAAK,UAAW,CAAC;AAC5D,SACG,SAAS,OAAO,GAAG,IACnB,SAAS,OAAO,GAAG,IACnB,SAAS,OAAO,EAAE,IAClB,SAAS,OAAO,EAAE,IAClB,SAAS,OAAO,EAAE,IACnB;AAEJ;AAKM,SAAU,cACd,QAA2C;AAE3C,SAAO,OAAO,YACZ,OAAO,QAAQ,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,GAAG,MAAM;IACzC;;IAEA,QAAQ,UAAa,QAAQ,QAAQ,MAAM,GAAG,IAAI,MAAM,MAAM,GAAG;GAClE,CAAC;AAEN;;;ACPA,eAAsBC,qBAAoB,MAQzC;AArCD;AAsCE,QAAM,EAAE,QAAQ,mBAAmB,QAAQ,OAAO,kBAAiB,IAAK;AAExE,MAAI,mBAAmB;AACrB,WAAO,kBAAkB,MAAM;EACjC;AAEA,QAAM,UAAkC;IACtC,gBAAgB;;AAGlB,QAAM,aAAa,qBAAqB;AACxC,QAAM,eAAe,qBAAqB,KAAK;AAE/C,QAAM,OAAO;IACX,SAAS;IACT,IAAI;IACJ,QAAQ;IACR,QAAQ,CAAC,cAAc,MAAM,GAAG,UAAU;;AAI5C,QAAM,mBAAmB,eAAe,MAAM;AAC9C,QAAM,WAAW,MAAM,iBAAiB,cAAc;IACpD,QAAQ;IACR;IACA,MAAM,UAAU,IAAI;GACrB;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAMC,SAAS,MAAM,SAAS,KAAI,KAAO,SAAS;AAElD,UAAM,IAAI,MAAM,oBAAoB,SAAS,MAAM,MAAMA,MAAK,EAAE;EAClE;AAEA,QAAM,MAAM,MAAM,SAAS,KAAI;AAE/B,MAAI,IAAI,QAAQ;AAEd,QAAI,OAAO,IAAI,WAAW,UAAU;AAClC,aAAO;QACL,kBAAkB,IAAI;;IAE1B;AAEA,QAAI,IAAI,OAAO,QAAQ;AACrB,cAAQ,KACN,2DAA2D,IAAI,OAAO,MAAM,IAAI,IAAI,OAAO,WAAW,cAAc,IAAI,OAAO,QAAQ,MAAM,EAAE,EAAE;IAErJ;AAEA,WAAO;MACL,kBAAkB,IAAI,OAAO;MAC7B,sBAAsB,IAAI,OAAO,uBAC7B,YAAY,IAAI,OAAO,oBAAoB,IAC3C;MACJ,oBAAoB,IAAI,OAAO,qBAC3B,YAAY,IAAI,OAAO,kBAAkB,IACzC;MACJ,cAAc,IAAI,OAAO,eACrB,YAAY,IAAI,OAAO,YAAY,IACnC;MACJ,WAAW,IAAI,OAAO;MACtB,eAAe,IAAI,OAAO;MAC1B,+BAA+B,IAAI,OAAO,gCACtC,YAAY,IAAI,OAAO,6BAA6B,IACpD;MACJ,yBAAyB,IAAI,OAAO,0BAChC,YAAY,IAAI,OAAO,uBAAuB,IAC9C;;EAER;AACA,QAAM,UACJ,SAAI,UAAJ,mBAAW,YAAW,IAAI,SAAS,SAAS,cAAc;AAC5D,QAAM,IAAI,MAAM,wBAAwB,YAAY,KAAK,KAAK,EAAE;AAClE;;;AC1DA,IAAM,iBAA8B,oBAAI,IAAG;AAE3C,IAAM,SAAS,CAAC,oBAAqC;AACnD,SAAO,GAAG,gBAAgB,MAAM,EAAE,IAAI,gBAAgB,OAAO;AAC/D;AAEA,IAAM,uBAAuB,CAAC,oBAAqC;AACjE,iBAAe,IAAI,OAAO,eAAe,CAAC;AAC5C;AAEO,IAAM,wBAAwB,CAAC,oBAAqC;AACzE,iBAAe,OAAO,OAAO,eAAe,CAAC;AAC/C;AAEA,IAAM,qBAAqB,CAAC,oBAAqC;AAC/D,SAAO,eAAe,IAAI,OAAO,eAAe,CAAC;AACnD;AAmBA,eAAsB,qBACpB,MAIC;AAED,QAAM,UAAU,KAAK,aAAa;AAClC,QAAM,WAAW,KAAK,cAAc;AACpC,QAAM,UAAU,KAAK,IAAG,IAAK;AAC7B,SAAO,KAAK,IAAG,IAAK,SAAS;AAC3B,UAAM,gBAAgB,MAAM,iBAAiB,IAAI;AACjD,QAAI,eAAe;AACjB,aAAO;IACT;AACA,UAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,QAAQ,CAAC;EAC9D;AACA,QAAM,IAAI,MACR,mDAAmD,KAAK,MAAM,EAAE,sBAAsB,KAAK,UAAU,EAAE;AAE3G;AAuBA,eAAsB,qBAAqB,MAS1C;AA/ID;AAgJE,QAAM,EACJ,aAAa,WACb,iBACA,iBACA,cACA,WACA,YACA,oBAAoB,MACpB,mBAAkB,IAChB;AACJ,QAAM,QAAQ,UAAU;AACxB,QAAM,SAAS,UAAU;AAEzB,QAAM,iBAAiB;IACrB;IACA;IACA,YAAY,uCAAW;IACvB,mBAAmB,uCAAW;;AAGhC,QAAM,oBAAoB,uBACxB,UAAK,cAAL,mBAAgB,sBAAqB,uBAAuB;AAG9D,QAAM,CAAC,YAAY,UAAU,cAAc,SAAS,KAAK,IACvD,MAAM,QAAQ,IAAI;IAChB,OAAO,uBAAuB,YAC1B,qBACA,mBAAmB,eAAe,EAAE,KAClC,CAACC,gBAAeA,eAAc,mBAAmB,eAAe,CAAC;IAEvE,OAAO,SAAS;IAChB,qBAAqB,UAAU,GAAG;IAClC,WAAW;MACT;MACA;MACA;MACA;KACD;IACD,gBAAgB;MACd;MACA;MACA;MACA,mBAAmB,uCAAW;MAC9B,kBAAkB,uCAAW;KAC9B;GACF;AAEH,QAAM,EAAE,cAAc,qBAAoB,IAAK;AAE/C,MAAI,sBAAsB,QAAQ;AAChC,WAAO,oBAAoB;MACzB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACD;EACH;AAGA,SAAO,oBAAoB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;GACD;AACH;AAEA,eAAe,WAAW,MAKzB;AAIC,QAAM,EAAE,WAAW,gBAAgB,OAAO,OAAM,IAAK;AACrD,MAAI,EAAE,cAAc,qBAAoB,IAAK;AAE7C,QAAM,cAAa,iDAAgB,eAAc,qBAAqB,KAAK;AAE3E,MAAI,cAAc,UAAU,GAAG;AAE7B,UAAM,kBAAkB,MAAM,iBAAiB;MAC7C,SAAS;KACV;AACD,mBAAe,gBAAgB;AAC/B,2BAAuB,gBAAgB;EACzC,OAAO;AAEL,UAAM,CAAC,sBAAsB,4BAA4B,IACvD,MAAM,QAAQ,IAAI;MAChB,qBAAqB,YAAY;MACjC,qBAAqB,oBAAoB;KAC1C;AAEH,QAAI,wBAAwB,8BAA8B;AAExD,qBAAe;AACf,6BAAuB;IACzB,OAAO;AAEL,YAAM,UAAU,MAAM,uBAAuB,QAAQ,KAAK;AAG1D,6BACE,gCAAgC,QAAQ,wBAAwB;AAClE,qBAAe,wBAAwB,QAAQ,gBAAgB;IACjE;EACF;AACA,SAAO,EAAE,cAAc,qBAAoB;AAC7C;AAEA,eAAe,oBAAoB,MAclC;AACC,QAAM,EACJ,gBACA,YACA,iBACA,iBACA,cACA,YACA,WACA,OACA,UACA,cACA,cACA,sBACA,kBAAiB,IACf;AACJ,QAAM,EAAE,OAAO,OAAM,IAAK;AAE1B,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY;AACd,kBAAc;AACd,QAAI,mBAAmB;AAErB,YAAM,uBAAuB,eAAe;IAC9C;EACF,OAAO;AACL,cAAU,gBAAgB;AAC1B,kBAAc,MAAM,OAClB,qBAAqB;MACnB;MACA;MACA,aAAa,uCAAW;MACxB,uBAAuB,uCAAW;KACnC,CAAC;AAEJ,QAAI,mBAAmB;AACrB,2BAAqB,eAAe;IACtC;EACF;AAEA,QAAM,YAA8B;IAClC,QAAQ,gBAAgB;IACxB;IACA;IACA;IACA;IACA,cAAc,gBAAgB;IAC9B,sBAAsB;IACtB,oBAAoB;IACpB;IACA;IACA,WAAW;IACX,eAAe;IACf,+BAA+B;IAC/B,yBAAyB;IACzB,WAAW;;AAGb,MAAI,YAAY;AACd,UAAM,kBAAmB,MAAMC,qBAAoB;MACjD,QAAQ;MACR;MACA;MACA,mBAAmB,uCAAW;MAC9B,mBAAmB,uCAAW;KAC/B;AACD,QAAI,gBAAgB,aAAa,gBAAgB,eAAe;AAC9D,gBAAU,YAAY,gBAAgB;AACtC,gBAAU,gBAAgB,gBAAgB;IAC5C;AAEA,QACE,gBAAgB,gBAChB,gBAAgB,wBAChB,gBAAgB,sBAChB,gBAAgB,2BAChB,gBAAgB,+BAChB;AACA,gBAAU,eAAe,gBAAgB;AACzC,gBAAU,uBAAuB,gBAAgB;AACjD,gBAAU,qBAAqB,gBAAgB;AAC/C,gBAAU,0BACR,gBAAgB;AAClB,gBAAU,gCACR,gBAAgB;IACpB,OAAO;AAEL,YAAM,kBAAiB,uCAAW,kBAC9B;QACE,CAAC,UAAU,eAAe,YAAY,GAAG;UACvC,WAAW;YACT,CAAC,UACC,oBACE,CAAC,EAAE,MAAM,UAAS,GAAI,EAAE,MAAM,UAAS,CAAE,GACzC;cACE,gBAAgB;cAChB,UAAU,eAAe;aAC1B,CACF,CACF,GAAG,MAAM,WAAW,EAAE,MAAM,GAAE,CAAE;;;UAIvC;AACJ,YAAM,YAAY,MAAM,kBACtB;QACE,QAAQ;QACR,SAAS;SAEX,cAAc;AAEhB,gBAAU,eAAe,UAAU;AACnC,gBAAU,uBAAuB,UAAU;AAC3C,gBAAU,qBAAqB,UAAU;AACzC,gBAAU,2BAA0B,uCAAW,kBAC3C,UACA,UAAU,2BAA2B;AACzC,gBAAU,gCACR,UAAU,iCAAiC;AAE7C,YAAM,mBAAoB,MAAMA,qBAAoB;QAClD,QAAQ;QACR;QACA;QACA,mBAAmB,uCAAW;QAC9B,mBAAmB,uCAAW;OAC/B;AACD,UAAI,iBAAiB,aAAa,iBAAiB,eAAe;AAChE,kBAAU,YAAY,iBAAiB;AACvC,kBAAU,gBAAgB,iBAAiB;MAC7C;IACF;EACF,OAAO;AAEL,UAAM,YAAY,MAAM,kBAAkB;MACxC,QAAQ;MACR,SAAS;KACV;AACD,cAAU,eAAe,UAAU;AACnC,cAAU,uBAAuB,UAAU;AAC3C,cAAU,qBAAqB,UAAU;AACzC,cAAU,0BAA0B,UAAU,2BAA2B;AACzE,cAAU,gCACR,UAAU,iCAAiC;EAC/C;AACA,SAAO;IACL,GAAG;IACH,WAAW;;AAEf;AAEA,eAAe,oBAAoB,MAclC;AACC,QAAM,EACJ,gBACA,YACA,iBACA,iBACA,cACA,YACA,WACA,OACA,UACA,cACA,cACA,sBACA,kBAAiB,IACf;AACJ,QAAM,EAAE,OAAO,OAAM,IAAK;AAC1B,MAAI;AAEJ,MAAI,YAAY;AACd,eAAW;AACX,QAAI,mBAAmB;AAErB,YAAM,uBAAuB,eAAe;IAC9C;EACF,OAAO;AACL,eAAW,MAAM,mBAAmB;MAClC;MACA;MACA,aAAa,uCAAW;MACxB,uBAAuB,uCAAW;KACnC;AACD,QAAI,mBAAmB;AACrB,2BAAqB,eAAe;IACtC;EACF;AAEA,QAAM,YAA8B;IAClC,QAAQ,gBAAgB;IACxB;IACA;IACA;IACA;IACA;IACA,cAAc,gBAAgB;IAC9B,sBAAsB;IACtB,oBAAoB;IACpB,kBAAkB;IAClB,WAAW;;AAGb,MAAI,YAAY;AACd,UAAM,kBAAkB,MAAMA,qBAAoB;MAChD,QAAQ;MACR;MACA;MACA,mBAAmB,uCAAW;MAC9B,mBAAmB,uCAAW;KAC/B;AACD,UAAM,mBACJ,sBAAsB,kBAClB,gBAAgB,mBAChB;AACN,QAAI,oBAAoB,qBAAqB,MAAM;AACjD,gBAAU,mBAAmB;IAC/B;AAEA,QACE,gBAAgB,gBAChB,gBAAgB,wBAChB,gBAAgB,oBAChB;AACA,gBAAU,eAAe,gBAAgB;AACzC,gBAAU,uBAAuB,gBAAgB;AACjD,gBAAU,qBAAqB,gBAAgB;IACjD,OAAO;AAEL,YAAM,YAAY,MAAM,kBAAkB;QACxC,QAAQ;QACR,SAAS;OACV;AACD,gBAAU,eAAe,UAAU;AACnC,gBAAU,uBAAuB,UAAU;AAC3C,gBAAU,qBAAqB,UAAU;AAEzC,UAAI,oBAAoB,qBAAqB,MAAM;AACjD,cAAM,mBAAmB,MAAMA,qBAAoB;UACjD,QAAQ;UACR;UACA;UACA,mBAAmB,uCAAW;UAC9B,mBAAmB,uCAAW;SAC/B;AACD,cAAM,oBACJ,sBAAsB,mBAClB,iBAAiB,mBACjB;AACN,YAAI,qBAAqB,sBAAsB,MAAM;AACnD,oBAAU,mBAAmB;QAC/B;MACF;IACF;EACF,OAAO;AAEL,UAAM,YAAY,MAAM,kBAAkB;MACxC,QAAQ;MACR,SAAS;KACV;AACD,cAAU,eAAe,UAAU;AACnC,cAAU,uBAAuB,UAAU;AAC3C,cAAU,qBAAqB,UAAU;EAC3C;AACA,SAAO;IACL,GAAG;IACH,WAAW;;AAEf;AAqBA,eAAsB,WAAW,MAMhC;AACC,QAAM,EAAE,QAAQ,OAAO,mBAAmB,aAAY,IAAK;AAE3D,QAAM,aAAa,MAAMC,eAAc;IACrC,QAAQ,KAAK;IACb;IACA;IACA;GACD;AAED,MAAI,aAAa,aAAa;AAC5B,UAAM,YAAY,MAAM,aAAa,YAAY;MAC/C,SAAS;QACP,KAAK,WAAW,UAAU;;MAE5B,iBAAiB,UAAU,MAAM;MACjC,SAAS,MAAM;KAChB;AACD,WAAO;MACL,GAAG;MACH;;EAEJ;AACA,QAAM,IAAI,MAAM,+CAA+C;AACjE;AAmBA,eAAsBA,eAAc,MAKnC;AACC,QAAM,EAAE,QAAQ,OAAO,kBAAiB,IAAK;AAE7C,QAAM,oBAAoB,qBACxB,qBAAqB,uBAAuB;AAG9C,MAAI;AAEJ,MAAI,sBAAsB,QAAQ;AAChC,UAAM,eAAe,uBAAuB,MAA0B;AACtE,iBAAa,MAAMA,eAAiB;MAClC,UAAU,YAAY;QACpB,SAAS,qBAAqB;QAC9B;QACA,QAAQ,KAAK;OACd;MACD,QAAQ;KACT;EACH,OAAO;AACL,iBAAa,MAAM,cAAiB;MAClC,UAAU,YAAY;QACpB,SAAS,qBAAqB;QAC9B;QACA,QAAQ,KAAK;OACd;MACD;KACD;EACH;AACA,SAAO;AACT;AAEA,eAAe,mBAAmB,SAQjC;AACC,QAAM,EAAE,iBAAiB,cAAc,aAAa,sBAAqB,IACvE;AACF,QAAM,WAAW,qBAAqB;IACpC;IACA;IACA;IACA;GACD;AACD,SAAO,OAAO,CAAC,gBAAgB,SAAgB,MAAM,OAAO,QAAQ,CAAC,CAAC;AACxE;AAEA,eAAe,gBAAgB,SAM9B;AACC,QAAM,EACJ,iBACA,OACA,QACA,mBACA,iBAAgB,IACd;AACJ,MAAI,kBAAkB;AACpB,WAAO,iBAAiB,eAAe;EACzC;AACA,SAAO,MAAM,SAAS;IACpB,UAAU,YAAY;MACpB,SAAS,qBAAqB;MAC9B;MACA;KACD;IACD,KAAK,sBAAqB;IAC1B,QAAQ,gBAAgB;GACzB;AACH;AA+JA,eAAe,uBAAuB,iBAAiC;AACrE,QAAM,YAAY,KAAK,IAAG;AAC1B,SAAO,mBAAmB,eAAe,GAAG;AAC1C,QAAI,KAAK,IAAG,IAAK,YAAY,KAAO;AAClC,4BAAsB,eAAe;AACrC,YAAM,IAAI,MACR,0EAA0E;IAE9E;AACA,UAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC;EACzD;AACF;;;AC10BA,eAAsB,aAAa,MAGlC;AACC,SAAO,mBAAmB;IACxB,GAAG;IACH,WAAW;IACX,QAAQ;MACN,cAAc,KAAK,MAAM;MACzB,KAAK,QAAQ,qBAAqB;;GAErC;AACH;AAiBA,eAAsB,kBACpB,MAIA,gBAMC;AAED,QAAM,MAAM,MAAM,mBAAmB;IACnC,GAAG;IACH,WAAW;IACX,QAAQ;MACN,cAAc,KAAK,MAAM;MACzB,KAAK,QAAQ,qBAAqB;MAClC,kBAAkB,CAAA;;GAErB;AAGD,SAAO;IACL,oBAAoB,YAAY,IAAI,kBAAkB;IACtD,iBACE,IAAI,oBAAoB,SACpB,YAAY,IAAI,eAAe,IAC/B;IACN,sBAAsB,YAAY,IAAI,oBAAoB;IAC1D,cAAc,YAAY,IAAI,YAAY,IAAI;IAC9C,+BACE,IAAI,kCAAkC,SAClC,YAAY,IAAI,6BAA6B,IAC7C;IACN,yBACE,IAAI,4BAA4B,SAC5B,YAAY,IAAI,uBAAuB,IACvC;;AAEV;AAsGA,eAAsB,iBAAiB,MAEtC;AACC,QAAM,MAAM,MAAM,mBAAmB;IACnC,GAAG;IACH,WAAW;IACX,QAAQ,CAAA;GACT;AAED,SAAO;IACL,sBAAsB,YAAY,IAAI,oBAAoB;IAC1D,cAAc,YAAY,IAAI,YAAY;;AAE9C;AAkBA,eAAsB,iBACpB,MAEC;AAnQH;AAqQE,QAAM,MAAM,MAAM,oBAAoB,IAAI;AAE1C,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AAEA,MAAI,IAAI,YAAY,OAAO;AAEzB,UAAM,OAAO,eAAe;MAC1B,QAAQ,CAAC,+BAA8B,GAAI,wBAAuB,CAAE;MACpE,MAAM,IAAI;KACX;AACD,UAAM,gBAAe,gBAAK,CAAC,MAAN,mBAAS,SAAT,mBAAe;AACpC,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,MACR,4BAA4B,IAAI,QAAQ,eAAe,EAAE;IAE7D;AACA,UAAM,YAAY,kBAAkB;MAClC,MAAM;KACP;AACD,UAAM,IAAI,MACR,+BAA+B,UAAU,KAAK,KAAK,GAAG,CAAC,gBACrD,IAAI,QAAQ,eACd,EAAE;EAEN;AACA,SAAO,IAAI;AACb;AAkBA,eAAsB,oBACpB,MAEC;AAED,QAAM,MAAM,MAAM,mBAAmB;IACnC,SAAS;IACT,WAAW;IACX,QAAQ,CAAC,KAAK,UAAU;GACzB;AACD,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AACA,SAAO,2BAA2B,GAA2B;AAC/D;AAKA,eAAsB,mBAAmB,MAGxC;AACC,QAAM,MAAM,MAAM,mBAAmB;IACnC,SAAS,KAAK;IACd,WAAW;IACX,QAAQ,CAAC,KAAK,WAAW;GAC1B;AAED,SAAO;IACL,WAAW,IAAI;IACf,gBAAgB,IAAI;;AAExB;AAKA,eAAsB,qBAAqB,MAM1C;AACC,QAAM,MAAM,MAAM,mBAAmB;IACnC,GAAG;IACH,WAAW;IACX,QAAQ;MACN,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;;GAER;AAED,SAAO;IACL,eAAe,IAAI;;AAEvB;AAKA,eAAsB,yBAAyB,MAG9C;AACC,QAAM,MAAM,MAAM,mBAAmB;IACnC,GAAG;IACH,WAAW;IACX,QAAQ,CAAC,KAAK,aAAa;GAC5B;AACD,SAAO;IACL,iBAAiB,IAAI;;AAEzB;AAEA,eAAsB,uBAAuB,MAI5C;AACC,QAAM,MAAM,MAAM,mBAAmB;IACnC,SAAS,KAAK;IACd,WAAW;IACX,QAAQ;MACN;QACE,GAAG,KAAK;QACR,mBAAmB,KAAK;;;GAG7B;AAED,SAAO;IACL,iBAAiB,IAAI;;AAEzB;AAEA,eAAe,mBAAmB,MAajC;AACC,QAAM,EAAE,SAAS,WAAW,OAAM,IAAK;AAEvC,QAAM,aAAa,QAAQ,cAAc,qBAAqB,QAAQ,KAAK;AAC3E,QAAM,mBAAmB,eAAe,QAAQ,MAAM;AACtD,QAAM,WAAW,MAAM,iBAAiB,YAAY;IAClD,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM,UAAU;MACd,SAAS;MACT,IAAI;MACJ,QAAQ;MACR;KACD;GACF;AACD,QAAM,MAAM,MAAM,SAAS,KAAI;AAC/B,MAAI,CAAC,SAAS,MAAM,IAAI,OAAO;AAC7B,QAAI,QAAQ,IAAI,SAAS,SAAS;AAClC,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,UAAU,KAAK;IACzB;AACA,UAAM,OAAO,IAAI,QAAQ;AAEzB,UAAM,IAAI,MACR,GAAG,SAAS,WAAW,KAAK;UACxB,SAAS,MAAM;QACjB,IAAI,EAAE;EAEZ;AAEA,SAAO,IAAI;AACb;;;AC3bM,SAAU,cAAc,QAAc;AAC1C,MAAI,OAAO,OAAO,SAAS;AACzB,WAAO;EACT;AAEA,QAAM,SAAS,OAAO,UAAS;AAC/B,MAAI,CAAC,CAAC,UAAU,kBAAkB,UAAU,CAAC,CAAC,OAAO,cAAc;AACjE,WAAO;EACT;AAEA,SAAO;AACT;;;AC+CA,IAAM,gCAAgC,oBAAI,QAAO;AACjD,IAAM,gCAAgC,oBAAI,QAAO;AAKjD,eAAsB,oBACpB,mBACA,iBAAmC;AA1ErC;AA4EE,QAAM,EAAE,iBAAiB,QAAQ,OAAO,aAAY,IAAK;AAEzD,MAAI,CAAC,iBAAiB;AACpB,UAAM,IAAI,MACR,2DAA2D;EAE/D;AAEA,QAAM,UAAU;AAChB,QAAM,QAAQ,gBAAgB,QAAQ;AACtC,QAAM,aACJ,aAAa,UAAU,QAAQ,UAAU,QAAQ;AACnD,MAAI,MAAM,cAAc,KAAK,GAAG;AAC9B,WAAO;MACL,oBAAoB;QAClB;QACA;QACA;QACA;OACD;MACD;;EAEJ;AAGA,MAAI,QAAQ,kBAAkB,GAAC,aAAQ,cAAR,mBAAmB,oBAAmB;AACnE,UAAM,oBAAoB,MAAM,yBAC9B,QAAQ,gBACR,QACA,KAAK;AAEP,QAAI,mBAAmB;AACrB,cAAQ,YAAY;QAClB,GAAG,QAAQ;QACX;;IAEJ;EACF;AAEA,QACE,aAAQ,cAAR,mBAAmB,mBACnB,GAAC,aAAQ,cAAR,mBAAmB,oBACpB;AAEA,YAAQ,YAAY;MAClB,GAAG,QAAQ;MACX,mBAAmB;;EAEvB;AAEA,QAAM,iBACJ,QAAQ,kBACR,0BAAyB,aAAQ,cAAR,mBAAmB,iBAAiB;AAE/D,QAAM,kBAAkB,YAAY;IAClC;IACA,SAAS;IACT;GACD;AAED,QAAM,iBAAiB,MAAM,eAAe;IAC1C;IACA,cAAc,gBAAgB;IAC9B,yBAAwB,aAAQ,cAAR,mBAAmB;IAC3C,cAAa,aAAQ,cAAR,mBAAmB;IAChC,iBAAgB,aAAQ,cAAR,mBAAmB;GACpC,EACE,KAAK,CAAC,YAAY,OAAO,EACzB,MAAM,CAAC,QAAO;AACb,UAAM,IAAI,MACR,uDAAuD,gBAAgB,OAAO,gBAAgB,MAAM,EAAE,MAAK,2BAAK,YAAW,eAAe,IAC1I,EAAE,OAAO,IAAG,CAAE;EAElB,CAAC;AAEH,QAAM,kBAAkB,YAAY;IAClC;IACA,SAAS;IACT;GACD;AAED,QAAM,UAAU,MAAM,mBAAmB;IACvC,GAAG;IACH;IACA;IACA;IACA;IACA;IACA;GACD;AAED,gCAA8B,IAAI,iBAAiB,OAAO;AAC1D,gCAA8B,IAAI,SAAS,eAAe;AAE1D,MAAI,QAAQ,YAAY;AACtB,QACE,MAAM,uBAAuB;MAC3B;MACA,mBAAmB,QAAQ,WAAW;MACtC,gBAAgB,QAAQ,WAAW;KACpC,GACD;AACA,YAAM,cAAc,cAAc;QAChC,SAAS;QACT,UAAU;QACV,aAAa,QAAQ,WAAW;QAChC,mBAAmB,QAAQ,WAAW;OACvC;AACD,YAAM,gBAAgB;QACpB;QACA;OACD;IACH;EACF;AAEA,SAAO,CAAC,SAAS,KAAK;AACxB;AAKA,eAAsB,uBAAuB,SAAgB;AAE3D,QAAM,kBAAkB,8BAA8B,IAAI,OAAO;AACjE,MAAI,iBAAiB;AAEnB,kCAA8B,OAAO,eAAe;AACpD,kCAA8B,OAAO,OAAO;EAC9C;AACF;AAEA,eAAe,mBACb,SAA4B;AAhN9B;AAkNE,QAAM,kBAAiB,aAAQ,cAAR,mBAAmB;AAC1C,MAAI,gBAAgB;AAClB,QACE,uBACE,aAAQ,cAAR,mBAAmB,sBAAqB,uBAAuB,MAC3D,QACN;AACA,YAAM,IAAI,MACR,+DAA+D;IAEnE;EACF;AAEA,MAAI,kBAAkB,QAAQ;AAC9B,QAAM,UAAmB;IACvB,SAAS,WAAW,gBAAgB,OAAO;IAC3C,MAAM,gBAAgB,aAAkC;AAlO5D,UAAAC,KAAAC,KAAA;AAoOM,UAAI,oBAIiC;AACrC,UAAI,gBAAgB;AAClB,cAAM,aAAa;UACjB;UACA;UACA;SACD;AACD,cAAM,oBAAoB,YAAqC;AAC7D,iBAAO;YACL,WAAW,eAAe;YAC1B,eAAe;;QAEnB;AACA,8BAAoBD,MAAA,QAAQ,cAAR,gBAAAA,IAAmB,cAAa;MACtD,OAAO;AACL,6BAAoBC,MAAA,QAAQ,cAAR,gBAAAA,IAAmB;MACzC;AAGA,UAAI,YAAY,YAAY,gBAAgB,MAAM,IAAI;AACpD,0BAAkB,YAAY;UAC5B,SAAS,QAAQ;UACjB,OAAO,eAAe,YAAY,OAAO;UACzC,QAAQ,QAAQ;SACjB;MACH;AAEA,YAAM,YAAY,eAAe;QAC/B;QACA;QACA,kBAAiB,aAAQ,cAAR,mBAAmB;OACrC;AAED,YAAM,QAAQ,eAAe,YAAY,OAAO;AAChD,YAAM,SAAS,MAAM,YAAY;QAC/B;QACA,SAAS;UACP,GAAG;UACH;UACA;UACA,WAAW;YACT,GAAG,QAAQ;YACX,WAAW;;;OAGhB;AACD,uBAAiB;QACf,QAAQ,QAAQ;QAChB,SAAS,MAAM;QACf,iBAAiB,OAAO;QACxB,eAAe,QAAQ,gBAAgB;QACvC,YAAY;QACZ,iBAAiB,YAAY,MAAM;OACpC;AACD,aAAO;IACT;IACA,MAAM,qBAAqB,cAAqC;AAhSpE,UAAAD,KAAAC;AAiSM,YAAM,YAAY,oBAAoB;QACpC;QACA;QACA,uBAAsBD,MAAA,QAAQ,cAAR,gBAAAA,IAAmB;OAC1C;AACD,UAAI,aAAa,WAAW,GAAG;AAC7B,cAAM,IAAI,MAAM,yBAAyB;MAC3C;AACA,YAAM,UAAU,aAAa,CAAC;AAC9B,UAAI,CAAC,SAAS;AACZ,cAAM,IAAI,MAAM,yBAAyB;MAC3C;AACA,YAAM,QAAQ,eAAe,QAAQ,OAAO;AAC5C,YAAM,SAAS,MAAM,YAAY;QAC/B;QACA,SAAS;UACP,GAAG;UACH;UACA;;OAEH;AACD,uBAAiB;QACf,QAAQ,QAAQ;QAChB,SAAS,MAAM;QACf,iBAAiB,OAAO;QACxB,eAAe,QAAQ,gBAAgB;QACvC,YAAY;QACZ,mBAAiBC,MAAA,aAAa,CAAC,MAAd,gBAAAA,IAAiB,OAAM;OACzC;AACD,aAAO;IACT;IACA,MAAM,YAAY,EAAE,QAAO,GAAgC;AAhU/D,UAAAD;AAiUM,WAAIA,MAAA,QAAQ,cAAR,gBAAAA,IAAmB,aAAa;AAClC,eAAO,QAAQ,UAAU,YAAY;UACnC,cAAc,QAAQ;UACtB,iBAAiB,QAAQ;UACzB;UACA;SACD;MACH;AAEA,YAAM,EAAE,wBAAuB,IAAK,MAAM,OAAO,uBAAkB;AACnE,aAAO,wBAAwB;QAC7B;QACA,iBAAiB,QAAQ;QACzB;QACA;OACD;IACH;IACA,MAAM,cAGJ,WAA2D;AArVjE,UAAAA;AAsVM,WAAIA,MAAA,QAAQ,cAAR,gBAAAA,IAAmB,eAAe;AACpC,eAAO,QAAQ,UAAU,cAAc;UACrC,cAAc,QAAQ;UACtB,iBAAiB,QAAQ;UACzB;UACA;SACD;MACH;AAEA,YAAM,EAAE,0BAAyB,IAAK,MAAM,OAAO,uBAAkB;AACrE,aAAO,0BAA0B;QAC/B;QACA,iBAAiB,QAAQ;QACzB;QACA;OACD;IACH;IACA,MAAM,uBAAuB,aAAW;AAvW5C,UAAAA,KAAAC;AAwWM,cAAOA,OAAAD,MAAA,QAAQ,iBAAgB,2BAAxB,gBAAAC,IAAA,KAAAD,KAAiD;IAC1D;;AAEF,SAAO;AACT;AAEA,eAAe,aAAa,MAI3B;AAlXD;AAmXE,QAAM,EAAE,iBAAiB,gBAAgB,QAAO,IAAK;AACrD,QAAM,eAAe,eAAe;AACpC,QAAM,gBAAgB,YAAY;IAChC,SAAS;IACT,OAAO,gBAAgB;IACvB,QAAQ,gBAAgB;GACzB;AACD,QAAM,mBAAmB,MAAM,UAAU;IACvC,UAAU;IACV,OAAO,gBAAgB;IACvB,SAAS,eAAe;GACzB;AAED,MAAI,mBAAmB,IAAI;AACzB;EACF;AAEA,QAAM,YAAYE,SAAQ;IACxB,UAAU;IACV,SAAS,eAAe;IACxB,WAAWC,aAAY;GACxB;AACD,QAAM,cAAc,MAAM,0BAA0B;IAClD,aAAa;IACb,MAAM,gBAAgB;GACvB;AACD,QAAM,YAAY,eAAe;IAC/B;IACA;IACA,kBAAiB,aAAQ,cAAR,mBAAmB;GACrC;AACD,QAAM,YAAY;IAChB;IACA,SAAS;MACP,GAAG;MACH,WAAW;QACT,GAAG,QAAQ;QACX,gBAAgB;;;GAGrB;AACH;AAEA,SAAS,oBAAoB,MAK5B;AACC,QAAM,EAAE,iBAAiB,mBAAmB,MAAK,IAAK;AACtD,QAAM,UAAmB;IACvB,SAAS,WAAW,kBAAkB,gBAAgB,OAAO;IAC7D,MAAM,gBAAgB,aAAkC;AAva5D;AAyaM,YAAM,SAAS;QACb,MAAM,YAAY;QAClB,IAAI,YAAY,MAAM;QACtB,OAAO,YAAY,SAAS;QAC5B,OAAO,eAAe,YAAY,OAAO;QACzC,QAAQ,kBAAkB;QAC1B,QAAQ,YAAY;;AAGtB,UAAI,0BAA0B,MAAM,0BAA0B;QAC5D;QACA,aAAa;OACd;AAED,UAAI,KAAK,cAAc,CAAC,wBAAwB,WAAW;AAEzD,cAAM,SAAS,MAAM,mBAAmB;UACtC,SAAS;YACP,QAAQ,kBAAkB;YAC1B;YACA,aAAY,qBAAgB,cAAhB,mBAA2B;YACvC,oBAAmB,qBAAgB,cAAhB,mBAA2B;;UAEhD,aAAa;SACd;AACD,kCAA0B;UACxB,GAAG;UACH,GAAG;;MAEP;AAGA,YAAM,oBAAoB,MAAM,sBAAsB;QACpD;QACA,SAAS,MAAM;QACf,mBAAmB;OACpB;AAGD,YAAM,SAAS,MAAM,uBAAuB;QAC1C,SAAS;UACP,QAAQ,kBAAkB;UAC1B;UACA,aAAY,qBAAgB,cAAhB,mBAA2B;UACvC,oBAAmB,qBAAgB,cAAhB,mBAA2B;;QAEhD,aAAa;QACb;OACD;AAED,uBAAiB;QACf,QAAQ,kBAAkB;QAC1B,SAAS,MAAM;QACf,iBAAiB,OAAO;QACxB,eAAe,QAAQ;QACvB,YAAY;QACZ,iBAAiB,YAAY,MAAM;OACpC;AAED,aAAO;QACL,iBAAiB,OAAO;QACxB,QAAQ,kBAAkB;QAC1B;;IAEJ;IACA,MAAM,YAAY,EAAE,QAAO,GAAgC;AACzD,aAAO,kBAAkB,gBAAgB,YAAY,EAAE,QAAO,CAAE;IAClE;IACA,MAAM,cAGJ,YAA4D;AAC5D,YAAM,YAAY,eAAe,UAAU;AAC3C,aAAO,kBAAkB,gBAAgB,cAAc,SAAS;IAClE;IACA,MAAM,uBAAuB,aAAW;AApf5C;AAqfM,cAAO,6BAAkB,iBAAgB,2BAAlC,4BACL;IAEJ;;AAEF,SAAO;AACT;AAEA,eAAe,YAAY,MAG1B;AAhgBD;AAigBE,QAAM,EAAE,WAAW,QAAO,IAAK;AAC/B,MAAI;AACF,UAAM,iBAAiB,MAAM,qBAAqB;MAChD,aAAa;MACb,iBAAiB,QAAQ;MACzB,iBAAiB,QAAQ;MACzB,cAAc,QAAQ,gBAAgB;MACtC,YAAY,QAAQ;MACpB,WAAW,QAAQ;KACpB;AACD,UAAM,eAAe,MAAM,WAAW;MACpC,QAAQ,QAAQ;MAChB,OAAO,QAAQ;MACf,cAAc,QAAQ;MACtB,oBAAmB,aAAQ,cAAR,mBAAmB;MACtC,QAAQ;KACT;AACD,UAAM,iBAAiC;MACrC,OAAO,QAAQ;MACf,QAAQ,QAAQ;MAChB,aAAY,aAAQ,cAAR,mBAAmB;MAC/B,oBAAmB,aAAQ,cAAR,mBAAmB;;AAExC,UAAM,aAAa,MAAM,aAAa;MACpC,SAAS;MACT,QAAQ;KACT;AAED,UAAM,UAAU,MAAM,qBAAqB;MACzC,GAAG;MACH;KACD;AAED,qBAAiB;MACf,QAAQ,QAAQ;MAChB,SAAS,QAAQ,MAAM;MACvB,iBAAiB,QAAQ;MACzB,eAAe,QAAQ,gBAAgB;MACvC,YAAY;MACZ,iBAAiB,MAAM,qBAAqB,UAAU,MAAM,MAAS;KACtE;AAED,WAAO;MACL,QAAQ,QAAQ;MAChB,OAAO,QAAQ;MACf,iBAAiB,QAAQ;;EAE7B;AAEE,0BAAsB,QAAQ,eAAe;EAC/C;AACF;AAEA,eAAsB,yBACpB,gBACA,QACA,OAAY;AAEZ,QAAM,kBAAkB,YAAY;IAClC,SAAS;IACT;IACA;GACD;AACD,MAAI;AACF,UAAM,oBAAoB,MAAM,aAAa;MAC3C,UAAU;MACV,QAAQ;KACT;AACD,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;", "names": ["approve", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "maxUint96", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "getUserOpHash", "FN_SELECTOR", "FN_INPUTS", "FN_OUTPUTS", "toHex", "getPaymasterAndData", "error", "isDeployed", "getPaymasterAndData", "getUserOpHash", "_a", "_b", "approve", "maxUint96"]}