{"version": 3, "sources": ["../../thirdweb/src/extensions/erc1155/__generated__/IERC1155/read/totalSupply.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"totalSupply\" function.\n */\nexport type TotalSupplyParams = {\n  id: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"id\" }>;\n};\n\nexport const FN_SELECTOR = \"0xbd85b039\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"uint256\",\n    name: \"id\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"uint256\",\n  },\n] as const;\n\n/**\n * Checks if the `totalSupply` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `totalSupply` method is supported.\n * @extension ERC1155\n * @example\n * ```ts\n * import { isTotalSupplySupported } from \"thirdweb/extensions/erc1155\";\n * const supported = isTotalSupplySupported([\"0x...\"]);\n * ```\n */\nexport function isTotalSupplySupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"totalSupply\" function.\n * @param options - The options for the totalSupply function.\n * @returns The encoded ABI parameters.\n * @extension ERC1155\n * @example\n * ```ts\n * import { encodeTotalSupplyParams } from \"thirdweb/extensions/erc1155\";\n * const result = encodeTotalSupplyParams({\n *  id: ...,\n * });\n * ```\n */\nexport function encodeTotalSupplyParams(options: TotalSupplyParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.id]);\n}\n\n/**\n * Encodes the \"totalSupply\" function into a Hex string with its parameters.\n * @param options - The options for the totalSupply function.\n * @returns The encoded hexadecimal string.\n * @extension ERC1155\n * @example\n * ```ts\n * import { encodeTotalSupply } from \"thirdweb/extensions/erc1155\";\n * const result = encodeTotalSupply({\n *  id: ...,\n * });\n * ```\n */\nexport function encodeTotalSupply(options: TotalSupplyParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeTotalSupplyParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the totalSupply function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC1155\n * @example\n * ```ts\n * import { decodeTotalSupplyResult } from \"thirdweb/extensions/erc1155\";\n * const result = decodeTotalSupplyResultResult(\"...\");\n * ```\n */\nexport function decodeTotalSupplyResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"totalSupply\" function on the contract.\n * @param options - The options for the totalSupply function.\n * @returns The parsed result of the function call.\n * @extension ERC1155\n * @example\n * ```ts\n * import { totalSupply } from \"thirdweb/extensions/erc1155\";\n *\n * const result = await totalSupply({\n *  contract,\n *  id: ...,\n * });\n *\n * ```\n */\nexport async function totalSupply(\n  options: BaseTransactionOptions<TotalSupplyParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.id],\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;AAeO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;;;AAeJ,SAAU,uBAAuB,oBAA4B;AACjE,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAeM,SAAU,wBAAwB,SAA0B;AAChE,SAAO,oBAAoB,WAAW,CAAC,QAAQ,EAAE,CAAC;AACpD;AAeM,SAAU,kBAAkB,SAA0B;AAG1D,SAAQ,cACN,wBAAwB,OAAO,EAAE,MAC/B,CAAC;AAEP;AAaM,SAAU,wBAAwB,QAAW;AACjD,SAAO,oBAAoB,YAAY,MAAM,EAAE,CAAC;AAClD;AAkBA,eAAsB,YACpB,SAAkD;AAElD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,EAAE;GACpB;AACH;", "names": []}