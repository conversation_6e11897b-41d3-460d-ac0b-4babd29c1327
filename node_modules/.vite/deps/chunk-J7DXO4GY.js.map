{"version": 3, "sources": ["../../thirdweb/src/rpc/actions/eth_blockNumber.ts", "../../thirdweb/src/rpc/watchBlockNumber.ts", "../../thirdweb/src/rpc/actions/eth_getTransactionReceipt.ts", "../../thirdweb/src/transaction/actions/wait-for-tx-receipt.ts"], "sourcesContent": ["import type { EIP1193RequestFn, EIP1474Methods } from \"viem\";\nimport { hexToBigInt } from \"../../utils/encoding/hex.js\";\n\n/**\n * Retrieves the current block number from the Ethereum blockchain.\n * @param request - The EIP1193 request function.\n * @returns A promise that resolves to the current block number as a bigint.\n * @rpc\n * @example\n * ```ts\n * import { getRpcClient, eth_blockNumber } from \"thirdweb/rpc\";\n * const rpcRequest = getRpcClient({ client, chain });\n * const blockNumber = await eth_blockNumber(rpcRequest);\n * ```\n */\nexport async function eth_blockNumber(\n  request: EIP1193RequestFn<EIP1474Methods>,\n): Promise<bigint> {\n  const blockNumberHex = await request({\n    method: \"eth_blockNumber\",\n  });\n  return hexToBigInt(blockNumberHex);\n}\n", "import type { Chain } from \"../chains/types.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport { sleep } from \"../utils/sleep.js\";\nimport { eth_blockNumber } from \"./actions/eth_blockNumber.js\";\nimport { getRpcClient } from \"./rpc.js\";\n\nconst MAX_POLL_DELAY = 5000; // 5 seconds\nconst DEFAULT_POLL_DELAY = 1000; // 1 second\nconst MIN_POLL_DELAY = 500; // 500 milliseconds\nconst DEFAULT_OVERPOLL_RATIO = 2; // poll twice as often as the average block time by default\n\nconst SLIDING_WINDOW_SIZE = 10; // always keep track of the last 10 block times\n\n/**\n * TODO: document\n * @internal\n */\nfunction getAverageBlockTime(blockTimes: number[]): number {\n  // left-pad the blocktimes Array with the DEFAULT_POLL_DELAY\n  while (blockTimes.length < SLIDING_WINDOW_SIZE) {\n    blockTimes.unshift(DEFAULT_POLL_DELAY);\n  }\n\n  const sum = blockTimes.reduce((acc, blockTime) => acc + blockTime, 0);\n  return sum / blockTimes.length;\n}\n\n/**\n * TODO: document\n * @internal\n */\nfunction createBlockNumberPoller(\n  client: ThirdwebClient,\n  chain: Chain,\n  overPollRatio?: number,\n  onError?: (error: Error) => void,\n) {\n  let subscribers: Array<(blockNumber: bigint) => void> = [];\n  let blockTimesWindow: number[] = [];\n\n  let isActive = false;\n  let lastBlockNumber: bigint | undefined;\n  let lastBlockAt: number | undefined;\n\n  const rpcRequest = getRpcClient({ client, chain });\n\n  /**\n   * TODO: document\n   * @internal\n   */\n  async function poll() {\n    // stop polling if there are no more subscriptions\n    if (!isActive) {\n      return;\n    }\n\n    try {\n      const blockNumber = await eth_blockNumber(rpcRequest);\n\n      if (!lastBlockNumber || blockNumber > lastBlockNumber) {\n        let newBlockNumbers = [];\n        if (lastBlockNumber) {\n          for (let i = lastBlockNumber + 1n; i <= blockNumber; i++) {\n            newBlockNumbers.push(BigInt(i));\n          }\n        } else {\n          newBlockNumbers = [blockNumber];\n        }\n        lastBlockNumber = blockNumber;\n        const currentTime = new Date().getTime();\n        if (lastBlockAt) {\n          // if we skipped a block we need to adjust the block time down to that level\n          const blockTime =\n            (currentTime - lastBlockAt) / newBlockNumbers.length;\n\n          blockTimesWindow.push(blockTime);\n          blockTimesWindow = blockTimesWindow.slice(-SLIDING_WINDOW_SIZE);\n        }\n        lastBlockAt = currentTime;\n        // for all new blockNumbers...\n        for (const b of newBlockNumbers) {\n          // ... call all current subscribers\n          for (const subscriberCallback of subscribers) {\n            subscriberCallback(b);\n          }\n        }\n      }\n    } catch (err: unknown) {\n      if (onError) {\n        onError(err as Error);\n      } else {\n        console.error(\n          `[watchBlockNumber]: Failed to poll for latest block number: ${err}`,\n        );\n      }\n    }\n\n    const currentApproximateBlockTime = getAverageBlockTime(blockTimesWindow);\n\n    // make sure we never poll faster than our minimum poll delay or slower than our maximum poll delay\n    const pollDelay = Math.max(\n      MIN_POLL_DELAY,\n      Math.min(\n        MAX_POLL_DELAY,\n        Math.max(MIN_POLL_DELAY, currentApproximateBlockTime),\n      ),\n    );\n\n    // sleep for the pollDelay for this chain (divided by the overPollRatio, which defaults to 2)\n    await sleep(pollDelay / (overPollRatio ?? DEFAULT_OVERPOLL_RATIO));\n    // poll again\n    poll();\n  }\n\n  // return the \"subscribe\" function\n  return function subscribe(\n    callBack: (blockNumber: bigint) => void,\n    initialBlockNumber?: bigint,\n  ) {\n    subscribers.push(callBack);\n    // if we are currently not active -> start polling\n    if (!isActive) {\n      lastBlockNumber = initialBlockNumber;\n      isActive = true;\n      poll();\n    }\n\n    // return the \"unsubscribe\" function (meaning the caller can unsubscribe)\n    return function unSubscribe() {\n      // filter out the callback from the subscribers\n      subscribers = subscribers.filter((fn) => fn !== callBack);\n      // if the new subscribers Array is empty (aka we were the last subscriber) -> stop polling\n      if (subscribers.length === 0) {\n        lastBlockNumber = undefined;\n        lastBlockAt = undefined;\n        isActive = false;\n      }\n    };\n  };\n}\n\nconst existingPollers = new Map<\n  number,\n  ReturnType<typeof createBlockNumberPoller>\n>();\n\nexport type WatchBlockNumberOptions = {\n  client: ThirdwebClient;\n  chain: Chain;\n  onNewBlockNumber: (blockNumber: bigint) => void;\n  onError?: (error: Error) => void;\n  overPollRatio?: number;\n  latestBlockNumber?: bigint;\n};\n\n/**\n * Watches the block number for a specific chain.\n * @param opts - The options for watching the block number.\n * @returns The unwatch function.\n * @example\n * ```ts\n * import { watchBlockNumber } from \"thirdweb\";\n * const unwatch = watchBlockNumber({\n *  client,\n *  chainId,\n *  onNewBlockNumber: (blockNumber) => {\n *    // do something with the block number\n *  },\n *  onError: (err) => {\n *    // do something if getting the block number fails\n *  },\n * });\n *\n * // later stop watching\n * unwatch();\n * ```\n * @rpc\n */\nexport function watchBlockNumber(opts: WatchBlockNumberOptions) {\n  const {\n    client,\n    chain,\n    onNewBlockNumber,\n    overPollRatio,\n    latestBlockNumber,\n    onError,\n  } = opts;\n  const chainId = chain.id;\n  // if we already have a poller for this chainId -> use it.\n  let poller = existingPollers.get(chainId);\n  // otherwise create a new poller\n  if (!poller) {\n    poller = createBlockNumberPoller(client, chain, overPollRatio, onError);\n    // and store it for later use\n    existingPollers.set(chainId, poller);\n  }\n  // subscribe to the poller and return the unSubscribe function to the caller\n  return poller(onNewBlockNumber, latestBlockNumber);\n}\n", "import {\n  type EIP1193RequestFn,\n  type EIP1474Methods,\n  type GetTransactionReceiptParameters,\n  type TransactionReceipt,\n  formatTransactionReceipt,\n} from \"viem\";\n\n/**\n * Retrieves the transaction receipt for a given transaction hash.\n * Throws an error if the receipt is not found.\n * @param request - The EIP1193 request function.\n * @param params - The parameters for retrieving the transaction receipt.\n * @returns A promise that resolves to the transaction receipt.\n * @throws An error if the transaction receipt is not found.\n * @rpc\n * @example\n * ```ts\n * import { getRpcClient, eth_getTransactionReceipt } from \"thirdweb/rpc\";\n * const rpcRequest = getRpcClient({ client, chain });\n * const transactionReceipt = await eth_getTransactionReceipt(rpcRequest, {\n *  hash: \"0x...\",\n * });\n * ```\n */\nexport async function eth_getTransactionReceipt(\n  request: EIP1193RequestFn<EIP1474Methods>,\n  params: GetTransactionReceiptParameters,\n): Promise<TransactionReceipt> {\n  const receipt = await request({\n    method: \"eth_getTransactionReceipt\",\n    params: [params.hash],\n  });\n\n  if (!receipt) {\n    throw new Error(\"Transaction receipt not found.\");\n  }\n\n  return formatTransactionReceipt(receipt);\n}\n", "import type { Hex } from \"viem\";\nimport type { Chain } from \"../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { eth_getTransactionReceipt } from \"../../rpc/actions/eth_getTransactionReceipt.js\";\nimport { getRpcClient } from \"../../rpc/rpc.js\";\nimport { watchBlockNumber } from \"../../rpc/watchBlockNumber.js\";\nimport type { Prettify } from \"../../utils/type-utils.js\";\nimport type { SendTransactionResult, TransactionReceipt } from \"../types.js\";\n\nconst DEFAULT_MAX_BLOCKS_WAIT_TIME = 100;\n\nconst map = new Map<string, Promise<TransactionReceipt>>();\n\nexport type WaitForReceiptOptions = Prettify<\n  SendTransactionResult & {\n    client: ThirdwebClient;\n    chain: Chain;\n    maxBlocksWaitTime?: number;\n  }\n>;\n\n/**\n * Waits for the transaction receipt of a given transaction hash on a specific contract.\n * @param options - The options for waiting for the receipt.\n * By default, it's 100 blocks.\n * @returns A promise that resolves with the transaction receipt.\n * @transaction\n * @example\n * ```ts\n * import { waitForReceipt } from \"thirdweb\";\n * const receipt = await waitForReceipt({\n *   client,\n *   chain,\n *   transactionHash: \"0x123...\",\n * });\n * ```\n */\nexport function waitForReceipt(\n  options: WaitForReceiptOptions,\n): Promise<TransactionReceipt> {\n  const { transactionHash, chain, client } = options;\n\n  const chainId = chain.id;\n  const key = `${chainId}:tx_${transactionHash}`;\n  const maxBlocksWaitTime =\n    options.maxBlocksWaitTime ?? DEFAULT_MAX_BLOCKS_WAIT_TIME;\n\n  if (map.has(key)) {\n    // biome-ignore lint/style/noNonNullAssertion: the `has` above ensures that this will always be set\n    return map.get(key)!;\n  }\n  const promise = new Promise<TransactionReceipt>((resolve, reject) => {\n    if (!transactionHash) {\n      reject(\n        new Error(\n          \"Transaction has no transactionHash to wait for, did you execute it?\",\n        ),\n      );\n    }\n\n    const request = getRpcClient({ client, chain });\n\n    // start at -1 because the first block doesn't count\n    let blocksWaited = -1;\n\n    const unwatch = watchBlockNumber({\n      client: client,\n      chain: chain,\n      onNewBlockNumber: async () => {\n        blocksWaited++;\n        if (blocksWaited >= maxBlocksWaitTime) {\n          unwatch();\n          reject(\n            new Error(\n              `Transaction not found after ${maxBlocksWaitTime} blocks`,\n            ),\n          );\n          return;\n        }\n        try {\n          const receipt = await eth_getTransactionReceipt(request, {\n            hash: transactionHash as Hex,\n          });\n\n          // stop the polling\n          unwatch();\n          // resolve the top level promise with the receipt\n          resolve(receipt);\n        } catch {\n          // noop, we'll try again on the next blocks\n        }\n      },\n    });\n    // remove the promise from the map when it's done (one way or the other)\n  }).finally(() => {\n    map.delete(key);\n  });\n\n  map.set(key, promise);\n  return promise;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAeA,eAAsB,gBACpB,SAAyC;AAEzC,QAAM,iBAAiB,MAAM,QAAQ;IACnC,QAAQ;GACT;AACD,SAAO,YAAY,cAAc;AACnC;;;AChBA,IAAM,iBAAiB;AACvB,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB;AACvB,IAAM,yBAAyB;AAE/B,IAAM,sBAAsB;AAM5B,SAAS,oBAAoB,YAAoB;AAE/C,SAAO,WAAW,SAAS,qBAAqB;AAC9C,eAAW,QAAQ,kBAAkB;EACvC;AAEA,QAAM,MAAM,WAAW,OAAO,CAAC,KAAK,cAAc,MAAM,WAAW,CAAC;AACpE,SAAO,MAAM,WAAW;AAC1B;AAMA,SAAS,wBACP,QACA,OACA,eACA,SAAgC;AAEhC,MAAI,cAAoD,CAAA;AACxD,MAAI,mBAA6B,CAAA;AAEjC,MAAI,WAAW;AACf,MAAI;AACJ,MAAI;AAEJ,QAAM,aAAa,aAAa,EAAE,QAAQ,MAAK,CAAE;AAMjD,iBAAe,OAAI;AAEjB,QAAI,CAAC,UAAU;AACb;IACF;AAEA,QAAI;AACF,YAAM,cAAc,MAAM,gBAAgB,UAAU;AAEpD,UAAI,CAAC,mBAAmB,cAAc,iBAAiB;AACrD,YAAI,kBAAkB,CAAA;AACtB,YAAI,iBAAiB;AACnB,mBAAS,IAAI,kBAAkB,IAAI,KAAK,aAAa,KAAK;AACxD,4BAAgB,KAAK,OAAO,CAAC,CAAC;UAChC;QACF,OAAO;AACL,4BAAkB,CAAC,WAAW;QAChC;AACA,0BAAkB;AAClB,cAAM,eAAc,oBAAI,KAAI,GAAG,QAAO;AACtC,YAAI,aAAa;AAEf,gBAAM,aACH,cAAc,eAAe,gBAAgB;AAEhD,2BAAiB,KAAK,SAAS;AAC/B,6BAAmB,iBAAiB,MAAM,CAAC,mBAAmB;QAChE;AACA,sBAAc;AAEd,mBAAW,KAAK,iBAAiB;AAE/B,qBAAW,sBAAsB,aAAa;AAC5C,+BAAmB,CAAC;UACtB;QACF;MACF;IACF,SAAS,KAAc;AACrB,UAAI,SAAS;AACX,gBAAQ,GAAY;MACtB,OAAO;AACL,gBAAQ,MACN,+DAA+D,GAAG,EAAE;MAExE;IACF;AAEA,UAAM,8BAA8B,oBAAoB,gBAAgB;AAGxE,UAAM,YAAY,KAAK,IACrB,gBACA,KAAK,IACH,gBACA,KAAK,IAAI,gBAAgB,2BAA2B,CAAC,CACtD;AAIH,UAAM,MAAM,aAAa,iBAAiB,uBAAuB;AAEjE,SAAI;EACN;AAGA,SAAO,SAAS,UACd,UACA,oBAA2B;AAE3B,gBAAY,KAAK,QAAQ;AAEzB,QAAI,CAAC,UAAU;AACb,wBAAkB;AAClB,iBAAW;AACX,WAAI;IACN;AAGA,WAAO,SAAS,cAAW;AAEzB,oBAAc,YAAY,OAAO,CAAC,OAAO,OAAO,QAAQ;AAExD,UAAI,YAAY,WAAW,GAAG;AAC5B,0BAAkB;AAClB,sBAAc;AACd,mBAAW;MACb;IACF;EACF;AACF;AAEA,IAAM,kBAAkB,oBAAI,IAAG;AAqCzB,SAAU,iBAAiB,MAA6B;AAC5D,QAAM,EACJ,QACA,OACA,kBACA,eACA,mBACA,QAAO,IACL;AACJ,QAAM,UAAU,MAAM;AAEtB,MAAI,SAAS,gBAAgB,IAAI,OAAO;AAExC,MAAI,CAAC,QAAQ;AACX,aAAS,wBAAwB,QAAQ,OAAO,eAAe,OAAO;AAEtE,oBAAgB,IAAI,SAAS,MAAM;EACrC;AAEA,SAAO,OAAO,kBAAkB,iBAAiB;AACnD;;;AC7KA,eAAsB,0BACpB,SACA,QAAuC;AAEvC,QAAM,UAAU,MAAM,QAAQ;IAC5B,QAAQ;IACR,QAAQ,CAAC,OAAO,IAAI;GACrB;AAED,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,gCAAgC;EAClD;AAEA,SAAO,yBAAyB,OAAO;AACzC;;;AC9BA,IAAM,+BAA+B;AAErC,IAAM,MAAM,oBAAI,IAAG;AA0Bb,SAAU,eACd,SAA8B;AAE9B,QAAM,EAAE,iBAAiB,OAAO,OAAM,IAAK;AAE3C,QAAM,UAAU,MAAM;AACtB,QAAM,MAAM,GAAG,OAAO,OAAO,eAAe;AAC5C,QAAM,oBACJ,QAAQ,qBAAqB;AAE/B,MAAI,IAAI,IAAI,GAAG,GAAG;AAEhB,WAAO,IAAI,IAAI,GAAG;EACpB;AACA,QAAM,UAAU,IAAI,QAA4B,CAAC,SAAS,WAAU;AAClE,QAAI,CAAC,iBAAiB;AACpB,aACE,IAAI,MACF,qEAAqE,CACtE;IAEL;AAEA,UAAM,UAAU,aAAa,EAAE,QAAQ,MAAK,CAAE;AAG9C,QAAI,eAAe;AAEnB,UAAM,UAAU,iBAAiB;MAC/B;MACA;MACA,kBAAkB,YAAW;AAC3B;AACA,YAAI,gBAAgB,mBAAmB;AACrC,kBAAO;AACP,iBACE,IAAI,MACF,+BAA+B,iBAAiB,SAAS,CAC1D;AAEH;QACF;AACA,YAAI;AACF,gBAAM,UAAU,MAAM,0BAA0B,SAAS;YACvD,MAAM;WACP;AAGD,kBAAO;AAEP,kBAAQ,OAAO;QACjB,QAAQ;QAER;MACF;KACD;EAEH,CAAC,EAAE,QAAQ,MAAK;AACd,QAAI,OAAO,GAAG;EAChB,CAAC;AAED,MAAI,IAAI,KAAK,OAAO;AACpB,SAAO;AACT;", "names": []}