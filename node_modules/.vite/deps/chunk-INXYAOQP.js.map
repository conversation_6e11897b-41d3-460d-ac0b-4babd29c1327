{"version": 3, "sources": ["../../ox/core/Hash.ts", "../../ox/core/PublicKey.ts", "../../ox/core/Address.ts", "../../ox/core/Caches.ts", "../../ox/core/internal/lru.ts", "../../ox/core/internal/cursor.ts"], "sourcesContent": ["import { ripemd160 as noble_ripemd160 } from '@noble/hashes/ripemd160'\nimport { keccak_256 as noble_keccak256 } from '@noble/hashes/sha3'\nimport { sha256 as noble_sha256 } from '@noble/hashes/sha256'\nimport * as Bytes from './Bytes.js'\nimport type * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\n\n/**\n * Calculates the [Keccak256](https://en.wikipedia.org/wiki/SHA-3) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `keccak_256` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.keccak256('0xdeadbeef')\n * // @log: '0xd4fd4e189132273036449fc9e11198c739161b4c0116a9a2dccdfa1c492006f1'\n * ```\n *\n * @example\n * ### Calculate Hash of a String\n *\n * ```ts twoslash\n * import { Hash, Hex } from 'ox'\n *\n * Hash.keccak256(Hex.fromString('hello world'))\n * // @log: '0x3ea2f1d0abf3fc66cf29eebb70cbd4e7fe762ef8a09bcc06c8edf641230afec0'\n * ```\n *\n * @example\n * ### Configure Return Type\n *\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.keccak256('0xdeadbeef', { as: 'Bytes' })\n * // @log: Uint8Array [...]\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Keccak256 hash.\n */\nexport function keccak256<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: keccak256.Options<as> = {},\n): keccak256.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_keccak256(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace keccak256 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Calculates the [Ripemd160](https://en.wikipedia.org/wiki/RIPEMD) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `ripemd160` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.ripemd160('0xdeadbeef')\n * // '0x226821c2f5423e11fe9af68bd285c249db2e4b5a'\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Ripemd160 hash.\n */\nexport function ripemd160<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: ripemd160.Options<as> = {},\n): ripemd160.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_ripemd160(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace ripemd160 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Calculates the [Sha256](https://en.wikipedia.org/wiki/SHA-256) hash of a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n *\n * This function is a re-export of `sha256` from [`@noble/hashes`](https://github.com/paulmillr/noble-hashes), an audited & minimal JS hashing library.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.sha256('0xdeadbeef')\n * // '0x5f78c33274e43fa9de5659265c1d917e25c03722dcb0b8d27db8d5feaa813953'\n * ```\n *\n * @param value - {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value.\n * @param options - Options.\n * @returns Sha256 hash.\n */\nexport function sha256<\n  value extends Hex.Hex | Bytes.Bytes,\n  as extends 'Hex' | 'Bytes' =\n    | (value extends Hex.Hex ? 'Hex' : never)\n    | (value extends Bytes.Bytes ? 'Bytes' : never),\n>(\n  value: value | Hex.Hex | Bytes.Bytes,\n  options: sha256.Options<as> = {},\n): sha256.ReturnType<as> {\n  const { as = typeof value === 'string' ? 'Hex' : 'Bytes' } = options\n  const bytes = noble_sha256(Bytes.from(value))\n  if (as === 'Bytes') return bytes as never\n  return Hex.fromBytes(bytes) as never\n}\n\nexport declare namespace sha256 {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex'> = {\n    /** The return type. @default 'Hex' */\n    as?: as | 'Hex' | 'Bytes' | undefined\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Bytes.from.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if a string is a valid hash value.\n *\n * @example\n * ```ts twoslash\n * import { Hash } from 'ox'\n *\n * Hash.validate('0x')\n * // @log: false\n *\n * Hash.validate('0x3ea2f1d0abf3fc66cf29eebb70cbd4e7fe762ef8a09bcc06c8edf641230afec0')\n * // @log: true\n * ```\n *\n * @param value - Value to check.\n * @returns Whether the value is a valid hash.\n */\nexport function validate(value: string): value is Hex.Hex {\n  return Hex.validate(value) && Hex.size(value) === 32\n}\n\nexport declare namespace validate {\n  type ErrorType =\n    | Hex.validate.ErrorType\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n", "import * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as <PERSON><PERSON> from './Json.js'\nimport type { Compute, ExactPartial } from './internal/types.js'\n\n/** Root type for an ECDSA Public Key. */\nexport type PublicKey<\n  compressed extends boolean = false,\n  bigintType = bigint,\n  numberType = number,\n> = Compute<\n  compressed extends true\n    ? {\n        prefix: numberType\n        x: bigintType\n        y?: undefined\n      }\n    : {\n        prefix: numberType\n        x: bigintType\n        y: bigintType\n      }\n>\n\n/**\n * Asserts that a {@link ox#PublicKey.PublicKey} is valid.\n *\n * @example\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * PublicKey.assert({\n *   prefix: 4,\n *   y: 49782753348462494199823712700004552394425719014458918871452329774910450607807n,\n * })\n * // @error: PublicKey.InvalidError: Value \\`{\"y\":\"1\"}\\` is not a valid public key.\n * // @error: Public key must contain:\n * // @error: - an `x` and `prefix` value (compressed)\n * // @error: - an `x`, `y`, and `prefix` value (uncompressed)\n * ```\n *\n * @param publicKey - The public key object to assert.\n */\nexport function assert(\n  publicKey: ExactPartial<PublicKey>,\n  options: assert.Options = {},\n): asserts publicKey is PublicKey {\n  const { compressed } = options\n  const { prefix, x, y } = publicKey\n\n  // Uncompressed\n  if (\n    compressed === false ||\n    (typeof x === 'bigint' && typeof y === 'bigint')\n  ) {\n    if (prefix !== 4)\n      throw new InvalidPrefixError({\n        prefix,\n        cause: new InvalidUncompressedPrefixError(),\n      })\n    return\n  }\n\n  // Compressed\n  if (\n    compressed === true ||\n    (typeof x === 'bigint' && typeof y === 'undefined')\n  ) {\n    if (prefix !== 3 && prefix !== 2)\n      throw new InvalidPrefixError({\n        prefix,\n        cause: new InvalidCompressedPrefixError(),\n      })\n    return\n  }\n\n  // Unknown/invalid\n  throw new InvalidError({ publicKey })\n}\n\nexport declare namespace assert {\n  type Options = {\n    /** Whether or not the public key should be compressed. */\n    compressed?: boolean\n  }\n\n  type ErrorType = InvalidError | InvalidPrefixError | Errors.GlobalErrorType\n}\n\n/**\n * Compresses a {@link ox#PublicKey.PublicKey}.\n *\n * @example\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from({\n *   prefix: 4,\n *   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n *   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,\n * })\n *\n * const compressed = PublicKey.compress(publicKey) // [!code focus]\n * // @log: {\n * // @log:   prefix: 3,\n * // @log:   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n * // @log: }\n * ```\n *\n * @param publicKey - The public key to compress.\n * @returns The compressed public key.\n */\nexport function compress(publicKey: PublicKey<false>): PublicKey<true> {\n  const { x, y } = publicKey\n  return {\n    prefix: y % 2n === 0n ? 2 : 3,\n    x,\n  }\n}\n\nexport declare namespace compress {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Instantiates a typed {@link ox#PublicKey.PublicKey} object from a {@link ox#PublicKey.PublicKey}, {@link ox#Bytes.Bytes}, or {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from({\n *   prefix: 4,\n *   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n *   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,\n * })\n * // @log: {\n * // @log:   prefix: 4,\n * // @log:   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n * // @log:   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,\n * // @log: }\n * ```\n *\n * @example\n * ### From Serialized\n *\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from('0x048318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5')\n * // @log: {\n * // @log:   prefix: 4,\n * // @log:   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n * // @log:   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,\n * // @log: }\n * ```\n *\n * @param value - The public key value to instantiate.\n * @returns The instantiated {@link ox#PublicKey.PublicKey}.\n */\nexport function from<\n  const publicKey extends\n    | CompressedPublicKey\n    | UncompressedPublicKey\n    | Hex.Hex\n    | Bytes.Bytes,\n>(value: from.Value<publicKey>): from.ReturnType<publicKey> {\n  const publicKey = (() => {\n    if (Hex.validate(value)) return fromHex(value)\n    if (Bytes.validate(value)) return fromBytes(value)\n\n    const { prefix, x, y } = value\n    if (typeof x === 'bigint' && typeof y === 'bigint')\n      return { prefix: prefix ?? 0x04, x, y }\n    return { prefix, x }\n  })()\n\n  assert(publicKey)\n\n  return publicKey as never\n}\n\n/** @internal */\ntype CompressedPublicKey = PublicKey<true>\n\n/** @internal */\ntype UncompressedPublicKey = Omit<PublicKey<false>, 'prefix'> & {\n  prefix?: PublicKey['prefix'] | undefined\n}\n\nexport declare namespace from {\n  type Value<\n    publicKey extends\n      | CompressedPublicKey\n      | UncompressedPublicKey\n      | Hex.Hex\n      | Bytes.Bytes = PublicKey,\n  > = publicKey | CompressedPublicKey | UncompressedPublicKey\n\n  type ReturnType<\n    publicKey extends\n      | CompressedPublicKey\n      | UncompressedPublicKey\n      | Hex.Hex\n      | Bytes.Bytes = PublicKey,\n  > = publicKey extends CompressedPublicKey | UncompressedPublicKey\n    ? publicKey extends UncompressedPublicKey\n      ? Compute<publicKey & { readonly prefix: 0x04 }>\n      : publicKey\n    : PublicKey\n\n  type ErrorType = assert.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Deserializes a {@link ox#PublicKey.PublicKey} from a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.fromBytes(new Uint8Array([128, 3, 131, ...]))\n * // @log: {\n * // @log:   prefix: 4,\n * // @log:   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n * // @log:   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,\n * // @log: }\n * ```\n *\n * @param publicKey - The serialized public key.\n * @returns The deserialized public key.\n */\nexport function fromBytes(publicKey: Bytes.Bytes): PublicKey {\n  return fromHex(Hex.fromBytes(publicKey))\n}\n\nexport declare namespace fromBytes {\n  type ErrorType =\n    | fromHex.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Deserializes a {@link ox#PublicKey.PublicKey} from a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.fromHex('0x8318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5')\n * // @log: {\n * // @log:   prefix: 4,\n * // @log:   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n * // @log:   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,\n * // @log: }\n * ```\n *\n * @example\n * ### Deserializing a Compressed Public Key\n *\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.fromHex('0x038318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed75')\n * // @log: {\n * // @log:   prefix: 3,\n * // @log:   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n * // @log: }\n * ```\n *\n * @param publicKey - The serialized public key.\n * @returns The deserialized public key.\n */\nexport function fromHex(publicKey: Hex.Hex): PublicKey {\n  if (\n    publicKey.length !== 132 &&\n    publicKey.length !== 130 &&\n    publicKey.length !== 68\n  )\n    throw new InvalidSerializedSizeError({ publicKey })\n\n  if (publicKey.length === 130) {\n    const x = BigInt(Hex.slice(publicKey, 0, 32))\n    const y = BigInt(Hex.slice(publicKey, 32, 64))\n    return {\n      prefix: 4,\n      x,\n      y,\n    } as never\n  }\n\n  if (publicKey.length === 132) {\n    const prefix = Number(Hex.slice(publicKey, 0, 1))\n    const x = BigInt(Hex.slice(publicKey, 1, 33))\n    const y = BigInt(Hex.slice(publicKey, 33, 65))\n    return {\n      prefix,\n      x,\n      y,\n    } as never\n  }\n\n  const prefix = Number(Hex.slice(publicKey, 0, 1))\n  const x = BigInt(Hex.slice(publicKey, 1, 33))\n  return {\n    prefix,\n    x,\n  } as never\n}\n\nexport declare namespace fromHex {\n  type ErrorType = Hex.slice.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Serializes a {@link ox#PublicKey.PublicKey} to {@link ox#Bytes.Bytes}.\n *\n * @example\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from({\n *   prefix: 4,\n *   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n *   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,\n * })\n *\n * const bytes = PublicKey.toBytes(publicKey) // [!code focus]\n * // @log: Uint8Array [128, 3, 131, ...]\n * ```\n *\n * @param publicKey - The public key to serialize.\n * @returns The serialized public key.\n */\nexport function toBytes(\n  publicKey: PublicKey<boolean>,\n  options: toBytes.Options = {},\n): Bytes.Bytes {\n  return Bytes.fromHex(toHex(publicKey, options))\n}\n\nexport declare namespace toBytes {\n  type Options = {\n    /**\n     * Whether to include the prefix in the serialized public key.\n     * @default true\n     */\n    includePrefix?: boolean | undefined\n  }\n\n  type ErrorType =\n    | Hex.fromNumber.ErrorType\n    | Bytes.fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Serializes a {@link ox#PublicKey.PublicKey} to {@link ox#Hex.Hex}.\n *\n * @example\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from({\n *   prefix: 4,\n *   x: 59295962801117472859457908919941473389380284132224861839820747729565200149877n,\n *   y: 24099691209996290925259367678540227198235484593389470330605641003500238088869n,\n * })\n *\n * const hex = PublicKey.toHex(publicKey) // [!code focus]\n * // @log: '0x048318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5'\n * ```\n *\n * @param publicKey - The public key to serialize.\n * @returns The serialized public key.\n */\nexport function toHex(\n  publicKey: PublicKey<boolean>,\n  options: toHex.Options = {},\n): Hex.Hex {\n  assert(publicKey)\n\n  const { prefix, x, y } = publicKey\n  const { includePrefix = true } = options\n\n  const publicKey_ = Hex.concat(\n    includePrefix ? Hex.fromNumber(prefix, { size: 1 }) : '0x',\n    Hex.fromNumber(x, { size: 32 }),\n    // If the public key is not compressed, add the y coordinate.\n    typeof y === 'bigint' ? Hex.fromNumber(y, { size: 32 }) : '0x',\n  )\n\n  return publicKey_\n}\n\nexport declare namespace toHex {\n  type Options = {\n    /**\n     * Whether to include the prefix in the serialized public key.\n     * @default true\n     */\n    includePrefix?: boolean | undefined\n  }\n\n  type ErrorType = Hex.fromNumber.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Validates a {@link ox#PublicKey.PublicKey}. Returns `true` if valid, `false` otherwise.\n *\n * @example\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * const valid = PublicKey.validate({\n *   prefix: 4,\n *   y: 49782753348462494199823712700004552394425719014458918871452329774910450607807n,\n * })\n * // @log: false\n * ```\n *\n * @param publicKey - The public key object to assert.\n */\nexport function validate(\n  publicKey: ExactPartial<PublicKey>,\n  options: validate.Options = {},\n): boolean {\n  try {\n    assert(publicKey, options)\n    return true\n  } catch (error) {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type Options = {\n    /** Whether or not the public key should be compressed. */\n    compressed?: boolean\n  }\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Thrown when a public key is invalid.\n *\n * @example\n * ```ts twoslash\n * import { PublicKey } from 'ox'\n *\n * PublicKey.assert({ y: 1n })\n * // @error: PublicKey.InvalidError: Value `{\"y\":1n}` is not a valid public key.\n * // @error: Public key must contain:\n * // @error: - an `x` and `prefix` value (compressed)\n * // @error: - an `x`, `y`, and `prefix` value (uncompressed)\n * ```\n */\nexport class InvalidError extends Errors.BaseError {\n  override readonly name = 'PublicKey.InvalidError'\n\n  constructor({ publicKey }: { publicKey: unknown }) {\n    super(`Value \\`${Json.stringify(publicKey)}\\` is not a valid public key.`, {\n      metaMessages: [\n        'Public key must contain:',\n        '- an `x` and `prefix` value (compressed)',\n        '- an `x`, `y`, and `prefix` value (uncompressed)',\n      ],\n    })\n  }\n}\n\n/** Thrown when a public key has an invalid prefix. */\nexport class InvalidPrefixError<\n  cause extends InvalidCompressedPrefixError | InvalidUncompressedPrefixError =\n    | InvalidCompressedPrefixError\n    | InvalidUncompressedPrefixError,\n> extends Errors.BaseError<cause> {\n  override readonly name = 'PublicKey.InvalidPrefixError'\n\n  constructor({ prefix, cause }: { prefix: number | undefined; cause: cause }) {\n    super(`Prefix \"${prefix}\" is invalid.`, {\n      cause,\n    })\n  }\n}\n\n/** Thrown when the public key has an invalid prefix for a compressed public key. */\nexport class InvalidCompressedPrefixError extends Errors.BaseError {\n  override readonly name = 'PublicKey.InvalidCompressedPrefixError'\n\n  constructor() {\n    super('Prefix must be 2 or 3 for compressed public keys.')\n  }\n}\n\n/** Thrown when the public key has an invalid prefix for an uncompressed public key. */\nexport class InvalidUncompressedPrefixError extends Errors.BaseError {\n  override readonly name = 'PublicKey.InvalidUncompressedPrefixError'\n\n  constructor() {\n    super('Prefix must be 4 for uncompressed public keys.')\n  }\n}\n\n/** Thrown when the public key has an invalid serialized size. */\nexport class InvalidSerializedSizeError extends Errors.BaseError {\n  override readonly name = 'PublicKey.InvalidSerializedSizeError'\n\n  constructor({ publicKey }: { publicKey: Hex.Hex | Bytes.Bytes }) {\n    super(`Value \\`${publicKey}\\` is an invalid public key size.`, {\n      metaMessages: [\n        'Expected: 33 bytes (compressed + prefix), 64 bytes (uncompressed) or 65 bytes (uncompressed + prefix).',\n        `Received ${Hex.size(Hex.from(publicKey))} bytes.`,\n      ],\n    })\n  }\n}\n", "import type { Address as abitype_Address } from 'abitype'\nimport * as Bytes from './Bytes.js'\nimport * as Caches from './Caches.js'\nimport * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as <PERSON>Key from './PublicKey.js'\n\nconst addressRegex = /*#__PURE__*/ /^0x[a-fA-F0-9]{40}$/\n\n/** Root type for Address. */\nexport type Address = abitype_Address\n\n/**\n * Asserts that the given value is a valid {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.assert('******************************************')\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.assert('0xdeadbeef')\n * // @error: InvalidAddressError: Address \"0xdeadbeef\" is invalid.\n * ```\n *\n * @param value - Value to assert if it is a valid address.\n * @param options - Assertion options.\n */\nexport function assert(\n  value: string,\n  options: assert.Options = {},\n): asserts value is Address {\n  const { strict = true } = options\n\n  if (!addressRegex.test(value))\n    throw new InvalidAddressError({\n      address: value,\n      cause: new InvalidInputError(),\n    })\n\n  if (strict) {\n    if (value.toLowerCase() === value) return\n    if (checksum(value as Address) !== value)\n      throw new InvalidAddressError({\n        address: value,\n        cause: new InvalidChecksumError(),\n      })\n  }\n}\n\nexport declare namespace assert {\n  type Options = {\n    /**\n     * Enables strict mode. Whether or not to compare the address against its checksum.\n     *\n     * @default true\n     */\n    strict?: boolean | undefined\n  }\n\n  type ErrorType = InvalidAddressError | Errors.GlobalErrorType\n}\n\n/**\n * Computes the checksum address for the given {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.checksum('******************************************')\n * // @log: '******************************************'\n * ```\n *\n * @param address - The address to compute the checksum for.\n * @returns The checksummed address.\n */\nexport function checksum(address: string): Address {\n  if (Caches.checksum.has(address)) return Caches.checksum.get(address)!\n\n  assert(address, { strict: false })\n\n  const hexAddress = address.substring(2).toLowerCase()\n  const hash = Hash.keccak256(Bytes.fromString(hexAddress), { as: 'Bytes' })\n\n  const characters = hexAddress.split('')\n  for (let i = 0; i < 40; i += 2) {\n    if (hash[i >> 1]! >> 4 >= 8 && characters[i]) {\n      characters[i] = characters[i]!.toUpperCase()\n    }\n    if ((hash[i >> 1]! & 0x0f) >= 8 && characters[i + 1]) {\n      characters[i + 1] = characters[i + 1]!.toUpperCase()\n    }\n  }\n\n  const result = `0x${characters.join('')}` as const\n  Caches.checksum.set(address, result)\n  return result\n}\n\nexport declare namespace checksum {\n  type ErrorType =\n    | assert.ErrorType\n    | Hash.keccak256.ErrorType\n    | Bytes.fromString.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts a stringified address to a typed (checksummed) {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('******************************************')\n * // @log: '******************************************'\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('******************************************', {\n *   checksum: false\n * })\n * // @log: '******************************************'\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('hello')\n * // @error: InvalidAddressError: Address \"0xa\" is invalid.\n * ```\n *\n * @param address - An address string to convert to a typed Address.\n * @param options - Conversion options.\n * @returns The typed Address.\n */\nexport function from(address: string, options: from.Options = {}): Address {\n  const { checksum: checksumVal = false } = options\n  assert(address)\n  if (checksumVal) return checksum(address)\n  return address as Address\n}\n\nexport declare namespace from {\n  type Options = {\n    /**\n     * Whether to checksum the address.\n     *\n     * @default false\n     */\n    checksum?: boolean | undefined\n  }\n\n  type ErrorType =\n    | assert.ErrorType\n    | checksum.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an ECDSA public key to an {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address, PublicKey } from 'ox'\n *\n * const publicKey = PublicKey.from(\n *   '0x048318535b54105d4a7aae60c08fc45f9687181b4fdfc625bd1a753fa7397fed753547f11ca8696646f2f3acb08e31016afac23e630c5d11f59f61fef57b0d2aa5',\n * )\n * const address = Address.fromPublicKey(publicKey)\n * // @log: '******************************************'\n * ```\n *\n * @param publicKey - The ECDSA public key to convert to an {@link ox#Address.Address}.\n * @param options - Conversion options.\n * @returns The {@link ox#Address.Address} corresponding to the public key.\n */\nexport function fromPublicKey(\n  publicKey: PublicKey.PublicKey,\n  options: fromPublicKey.Options = {},\n): Address {\n  const address = Hash.keccak256(\n    `0x${PublicKey.toHex(publicKey).slice(4)}`,\n  ).substring(26)\n  return from(`0x${address}`, options)\n}\n\nexport declare namespace fromPublicKey {\n  type Options = {\n    /**\n     * Whether to checksum the address.\n     *\n     * @default false\n     */\n    checksum?: boolean | undefined\n  }\n\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | PublicKey.toHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Checks if two {@link ox#Address.Address} are equal.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.isEqual(\n *   '******************************************',\n *   '******************************************'\n * )\n * // @log: true\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.isEqual(\n *   '******************************************',\n *   '******************************************'\n * )\n * // @log: false\n * ```\n *\n * @param addressA - The first address to compare.\n * @param addressB - The second address to compare.\n * @returns Whether the addresses are equal.\n */\nexport function isEqual(addressA: Address, addressB: Address): boolean {\n  assert(addressA, { strict: false })\n  assert(addressB, { strict: false })\n  return addressA.toLowerCase() === addressB.toLowerCase()\n}\n\nexport declare namespace isEqual {\n  type ErrorType = assert.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if the given address is a valid {@link ox#Address.Address}.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.validate('******************************************')\n * // @log: true\n * ```\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.validate('0xdeadbeef')\n * // @log: false\n * ```\n *\n * @param address - Value to check if it is a valid address.\n * @param options - Check options.\n * @returns Whether the address is a valid address.\n */\nexport function validate(\n  address: string,\n  options: validate.Options = {},\n): address is Address {\n  const { strict = true } = options ?? {}\n  try {\n    assert(address, { strict })\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type Options = {\n    /**\n     * Enables strict mode. Whether or not to compare the address against its checksum.\n     *\n     * @default true\n     */\n    strict?: boolean | undefined\n  }\n}\n\n/**\n * Thrown when an address is invalid.\n *\n * @example\n * ```ts twoslash\n * import { Address } from 'ox'\n *\n * Address.from('0x123')\n * // @error: Address.InvalidAddressError: Address `0x123` is invalid.\n * ```\n */\nexport class InvalidAddressError<\n  cause extends InvalidInputError | InvalidChecksumError =\n    | InvalidInputError\n    | InvalidChecksumError,\n> extends Errors.BaseError<cause> {\n  override readonly name = 'Address.InvalidAddressError'\n\n  constructor({ address, cause }: { address: string; cause: cause }) {\n    super(`Address \"${address}\" is invalid.`, {\n      cause,\n    })\n  }\n}\n\n/** Thrown when an address is not a 20 byte (40 hexadecimal character) value. */\nexport class InvalidInputError extends Errors.BaseError {\n  override readonly name = 'Address.InvalidInputError'\n\n  constructor() {\n    super('Address is not a 20 byte (40 hexadecimal character) value.')\n  }\n}\n\n/** Thrown when an address does not match its checksum counterpart. */\nexport class InvalidChecksumError extends Errors.BaseError {\n  override readonly name = 'Address.InvalidChecksumError'\n\n  constructor() {\n    super('Address does not match its checksum counterpart.')\n  }\n}\n", "import type * as Address from './Address.js'\nimport { LruMap } from './internal/lru.js'\n\nconst caches = {\n  checksum: /*#__PURE__*/ new LruMap<Address.Address>(8192),\n}\n\nexport const checksum = caches.checksum\n\n/**\n * Clears all global caches.\n *\n * @example\n * ```ts\n * import { Caches } from 'ox'\n * Caches.clear()\n * ```\n */\nexport function clear() {\n  for (const cache of Object.values(caches)) cache.clear()\n}\n", "/**\n * @internal\n *\n * Map with a LRU (Least recently used) policy.\n * @see https://en.wikipedia.org/wiki/Cache_replacement_policies#LRU\n */\nexport class LruMap<value = unknown> extends Map<string, value> {\n  maxSize: number\n\n  constructor(size: number) {\n    super()\n    this.maxSize = size\n  }\n\n  override get(key: string) {\n    const value = super.get(key)\n\n    if (super.has(key) && value !== undefined) {\n      this.delete(key)\n      super.set(key, value)\n    }\n\n    return value\n  }\n\n  override set(key: string, value: value) {\n    super.set(key, value)\n    if (this.maxSize && this.size > this.maxSize) {\n      const firstKey = this.keys().next().value\n      if (firstKey) this.delete(firstKey)\n    }\n    return this\n  }\n}\n", "import type { Bytes } from '../Bytes.js'\nimport * as Errors from '../Errors.js'\n\n/** @internal */\nexport type Cursor = {\n  bytes: Bytes\n  dataView: DataView\n  position: number\n  positionReadCount: Map<number, number>\n  recursiveReadCount: number\n  recursiveReadLimit: number\n  remaining: number\n  assertReadLimit(position?: number): void\n  assertPosition(position: number): void\n  decrementPosition(offset: number): void\n  getReadCount(position?: number): number\n  incrementPosition(offset: number): void\n  inspectByte(position?: number): Bytes[number]\n  inspectBytes(length: number, position?: number): Bytes\n  inspectUint8(position?: number): number\n  inspectUint16(position?: number): number\n  inspectUint24(position?: number): number\n  inspectUint32(position?: number): number\n  pushByte(byte: Bytes[number]): void\n  pushBytes(bytes: Bytes): void\n  pushUint8(value: number): void\n  pushUint16(value: number): void\n  pushUint24(value: number): void\n  pushUint32(value: number): void\n  readByte(): Bytes[number]\n  readBytes(length: number, size?: number): Bytes\n  readUint8(): number\n  readUint16(): number\n  readUint24(): number\n  readUint32(): number\n  setPosition(position: number): () => void\n  _touch(): void\n}\n\nconst staticCursor: Cursor = /*#__PURE__*/ {\n  bytes: new Uint8Array(),\n  dataView: new DataView(new ArrayBuffer(0)),\n  position: 0,\n  positionReadCount: new Map(),\n  recursiveReadCount: 0,\n  recursiveReadLimit: Number.POSITIVE_INFINITY,\n  assertReadLimit() {\n    if (this.recursiveReadCount >= this.recursiveReadLimit)\n      throw new RecursiveReadLimitExceededError({\n        count: this.recursiveReadCount + 1,\n        limit: this.recursiveReadLimit,\n      })\n  },\n  assertPosition(position) {\n    if (position < 0 || position > this.bytes.length - 1)\n      throw new PositionOutOfBoundsError({\n        length: this.bytes.length,\n        position,\n      })\n  },\n  decrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position - offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  getReadCount(position) {\n    return this.positionReadCount.get(position || this.position) || 0\n  },\n  incrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position + offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  inspectByte(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]!\n  },\n  inspectBytes(length, position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + length - 1)\n    return this.bytes.subarray(position, position + length)\n  },\n  inspectUint8(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]!\n  },\n  inspectUint16(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 1)\n    return this.dataView.getUint16(position)\n  },\n  inspectUint24(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 2)\n    return (\n      (this.dataView.getUint16(position) << 8) +\n      this.dataView.getUint8(position + 2)\n    )\n  },\n  inspectUint32(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 3)\n    return this.dataView.getUint32(position)\n  },\n  pushByte(byte: Bytes[number]) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = byte\n    this.position++\n  },\n  pushBytes(bytes: Bytes) {\n    this.assertPosition(this.position + bytes.length - 1)\n    this.bytes.set(bytes, this.position)\n    this.position += bytes.length\n  },\n  pushUint8(value: number) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = value\n    this.position++\n  },\n  pushUint16(value: number) {\n    this.assertPosition(this.position + 1)\n    this.dataView.setUint16(this.position, value)\n    this.position += 2\n  },\n  pushUint24(value: number) {\n    this.assertPosition(this.position + 2)\n    this.dataView.setUint16(this.position, value >> 8)\n    this.dataView.setUint8(this.position + 2, value & ~4294967040)\n    this.position += 3\n  },\n  pushUint32(value: number) {\n    this.assertPosition(this.position + 3)\n    this.dataView.setUint32(this.position, value)\n    this.position += 4\n  },\n  readByte() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectByte()\n    this.position++\n    return value\n  },\n  readBytes(length, size) {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectBytes(length)\n    this.position += size ?? length\n    return value\n  },\n  readUint8() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint8()\n    this.position += 1\n    return value\n  },\n  readUint16() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint16()\n    this.position += 2\n    return value\n  },\n  readUint24() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint24()\n    this.position += 3\n    return value\n  },\n  readUint32() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint32()\n    this.position += 4\n    return value\n  },\n  get remaining() {\n    return this.bytes.length - this.position\n  },\n  setPosition(position) {\n    const oldPosition = this.position\n    this.assertPosition(position)\n    this.position = position\n    return () => (this.position = oldPosition)\n  },\n  _touch() {\n    if (this.recursiveReadLimit === Number.POSITIVE_INFINITY) return\n    const count = this.getReadCount()\n    this.positionReadCount.set(this.position, count + 1)\n    if (count > 0) this.recursiveReadCount++\n  },\n}\n\n/** @internal */\nexport function create(\n  bytes: Bytes,\n  { recursiveReadLimit = 8_192 }: create.Config = {},\n): Cursor {\n  const cursor: Cursor = Object.create(staticCursor)\n  cursor.bytes = bytes\n  cursor.dataView = new DataView(\n    bytes.buffer,\n    bytes.byteOffset,\n    bytes.byteLength,\n  )\n  cursor.positionReadCount = new Map()\n  cursor.recursiveReadLimit = recursiveReadLimit\n  return cursor\n}\n\n/** @internal */\nexport declare namespace create {\n  type Config = { recursiveReadLimit?: number | undefined }\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/** @internal */\nexport class NegativeOffsetError extends Errors.BaseError {\n  override readonly name = 'Cursor.NegativeOffsetError'\n\n  constructor({ offset }: { offset: number }) {\n    super(`Offset \\`${offset}\\` cannot be negative.`)\n  }\n}\n\n/** @internal */\nexport class PositionOutOfBoundsError extends Errors.BaseError {\n  override readonly name = 'Cursor.PositionOutOfBoundsError'\n\n  constructor({ length, position }: { length: number; position: number }) {\n    super(\n      `Position \\`${position}\\` is out of bounds (\\`0 < position < ${length}\\`).`,\n    )\n  }\n}\n\n/** @internal */\nexport class RecursiveReadLimitExceededError extends Errors.BaseError {\n  override readonly name = 'Cursor.RecursiveReadLimitExceededError'\n\n  constructor({ count, limit }: { count: number; limit: number }) {\n    super(\n      `Recursive read limit of \\`${limit}\\` exceeded (recursive read count: \\`${count}\\`).`,\n    )\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;mBAAAA;EAAA,cAAAC;EAAA,gBAAAC;;AA4CM,SAAU,UAMd,OACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,KAAK,OAAO,UAAU,WAAW,QAAQ,QAAO,IAAK;AAC7D,QAAM,QAAQ,WAAsBC,MAAK,KAAK,CAAC;AAC/C,MAAI,OAAO;AAAS,WAAO;AAC3B,SAAW,UAAU,KAAK;AAC5B;AAmCM,SAAUC,WAMd,OACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,KAAK,OAAO,UAAU,WAAW,QAAQ,QAAO,IAAK;AAC7D,QAAM,QAAQ,UAAsBD,MAAK,KAAK,CAAC;AAC/C,MAAI,OAAO;AAAS,WAAO;AAC3B,SAAW,UAAU,KAAK;AAC5B;AAmCM,SAAUE,QAMd,OACA,UAA8B,CAAA,GAAE;AAEhC,QAAM,EAAE,KAAK,OAAO,UAAU,WAAW,QAAQ,QAAO,IAAK;AAC7D,QAAM,QAAQ,OAAmBF,MAAK,KAAK,CAAC;AAC5C,MAAI,OAAO;AAAS,WAAO;AAC3B,SAAW,UAAU,KAAK;AAC5B;AAmCM,SAAUG,UAAS,OAAa;AACpC,SAAW,SAAS,KAAK,KAAS,KAAK,KAAK,MAAM;AACpD;;;AC9LA;;;;;;;;;cAAAC;EAAA,iBAAAC;EAAA,eAAAC;EAAA;;kBAAAC;;AA4CM,SAAU,OACd,WACA,UAA0B,CAAA,GAAE;AAE5B,QAAM,EAAE,WAAU,IAAK;AACvB,QAAM,EAAE,QAAQ,GAAG,EAAC,IAAK;AAGzB,MACE,eAAe,SACd,OAAO,MAAM,YAAY,OAAO,MAAM,UACvC;AACA,QAAI,WAAW;AACb,YAAM,IAAI,mBAAmB;QAC3B;QACA,OAAO,IAAI,+BAA8B;OAC1C;AACH;EACF;AAGA,MACE,eAAe,QACd,OAAO,MAAM,YAAY,OAAO,MAAM,aACvC;AACA,QAAI,WAAW,KAAK,WAAW;AAC7B,YAAM,IAAI,mBAAmB;QAC3B;QACA,OAAO,IAAI,6BAA4B;OACxC;AACH;EACF;AAGA,QAAM,IAAI,aAAa,EAAE,UAAS,CAAE;AACtC;AAkCM,SAAU,SAAS,WAA2B;AAClD,QAAM,EAAE,GAAG,EAAC,IAAK;AACjB,SAAO;IACL,QAAQ,IAAI,OAAO,KAAK,IAAI;IAC5B;;AAEJ;AA0CM,SAAUC,MAMd,OAA4B;AAC5B,QAAM,aAAa,MAAK;AACtB,QAAQ,SAAS,KAAK;AAAG,aAAOC,SAAQ,KAAK;AAC7C,QAAUC,UAAS,KAAK;AAAG,aAAOC,WAAU,KAAK;AAEjD,UAAM,EAAE,QAAQ,GAAG,EAAC,IAAK;AACzB,QAAI,OAAO,MAAM,YAAY,OAAO,MAAM;AACxC,aAAO,EAAE,QAAQ,UAAU,GAAM,GAAG,EAAC;AACvC,WAAO,EAAE,QAAQ,EAAC;EACpB,GAAE;AAEF,SAAO,SAAS;AAEhB,SAAO;AACT;AAqDM,SAAUA,WAAU,WAAsB;AAC9C,SAAOF,SAAY,UAAU,SAAS,CAAC;AACzC;AAwCM,SAAUA,SAAQ,WAAkB;AACxC,MACE,UAAU,WAAW,OACrB,UAAU,WAAW,OACrB,UAAU,WAAW;AAErB,UAAM,IAAI,2BAA2B,EAAE,UAAS,CAAE;AAEpD,MAAI,UAAU,WAAW,KAAK;AAC5B,UAAMG,KAAI,OAAW,MAAM,WAAW,GAAG,EAAE,CAAC;AAC5C,UAAM,IAAI,OAAW,MAAM,WAAW,IAAI,EAAE,CAAC;AAC7C,WAAO;MACL,QAAQ;MACR,GAAAA;MACA;;EAEJ;AAEA,MAAI,UAAU,WAAW,KAAK;AAC5B,UAAMC,UAAS,OAAW,MAAM,WAAW,GAAG,CAAC,CAAC;AAChD,UAAMD,KAAI,OAAW,MAAM,WAAW,GAAG,EAAE,CAAC;AAC5C,UAAM,IAAI,OAAW,MAAM,WAAW,IAAI,EAAE,CAAC;AAC7C,WAAO;MACL,QAAAC;MACA,GAAAD;MACA;;EAEJ;AAEA,QAAM,SAAS,OAAW,MAAM,WAAW,GAAG,CAAC,CAAC;AAChD,QAAM,IAAI,OAAW,MAAM,WAAW,GAAG,EAAE,CAAC;AAC5C,SAAO;IACL;IACA;;AAEJ;AA0BM,SAAU,QACd,WACA,UAA2B,CAAA,GAAE;AAE7B,SAAa,QAAQ,MAAM,WAAW,OAAO,CAAC;AAChD;AAqCM,SAAU,MACd,WACA,UAAyB,CAAA,GAAE;AAE3B,SAAO,SAAS;AAEhB,QAAM,EAAE,QAAQ,GAAG,EAAC,IAAK;AACzB,QAAM,EAAE,gBAAgB,KAAI,IAAK;AAEjC,QAAM,aAAiB;IACrB,gBAAoB,WAAW,QAAQ,EAAE,MAAM,EAAC,CAAE,IAAI;IAClD,WAAW,GAAG,EAAE,MAAM,GAAE,CAAE;;IAE9B,OAAO,MAAM,WAAe,WAAW,GAAG,EAAE,MAAM,GAAE,CAAE,IAAI;EAAI;AAGhE,SAAO;AACT;AA8BM,SAAUF,UACd,WACA,UAA4B,CAAA,GAAE;AAE9B,MAAI;AACF,WAAO,WAAW,OAAO;AACzB,WAAO;EACT,SAAS,OAAO;AACd,WAAO;EACT;AACF;AAyBM,IAAO,eAAP,cAAmC,UAAS;EAGhD,YAAY,EAAE,UAAS,GAA0B;AAC/C,UAAM,WAAgB,UAAU,SAAS,CAAC,iCAAiC;MACzE,cAAc;QACZ;QACA;QACA;;KAEH;AATe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAUzB;;AAII,IAAO,qBAAP,cAIW,UAAgB;EAG/B,YAAY,EAAE,QAAQ,MAAK,GAAgD;AACzE,UAAM,WAAW,MAAM,iBAAiB;MACtC;KACD;AALe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,+BAAP,cAAmD,UAAS;EAGhE,cAAA;AACE,UAAM,mDAAmD;AAHzC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAO,iCAAP,cAAqD,UAAS;EAGlE,cAAA;AACE,UAAM,gDAAgD;AAHtC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAO,6BAAP,cAAiD,UAAS;EAG9D,YAAY,EAAE,UAAS,GAAwC;AAC7D,UAAM,WAAW,SAAS,qCAAqC;MAC7D,cAAc;QACZ;QACA,YAAgB,KAAS,KAAK,SAAS,CAAC,CAAC;;KAE5C;AARe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EASzB;;;;ACtgBF;;;;;gBAAAI;EAAA,gBAAAC;EAAA,YAAAC;EAAA;;kBAAAC;;;;ACAA;;;;;;;ACKM,IAAO,SAAP,cAAuC,IAAkB;EAG7D,YAAYC,OAAY;AACtB,UAAK;AAHP,WAAA,eAAA,MAAA,WAAA;;;;;;AAIE,SAAK,UAAUA;EACjB;EAES,IAAI,KAAW;AACtB,UAAM,QAAQ,MAAM,IAAI,GAAG;AAE3B,QAAI,MAAM,IAAI,GAAG,KAAK,UAAU,QAAW;AACzC,WAAK,OAAO,GAAG;AACf,YAAM,IAAI,KAAK,KAAK;IACtB;AAEA,WAAO;EACT;EAES,IAAI,KAAa,OAAY;AACpC,UAAM,IAAI,KAAK,KAAK;AACpB,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS;AAC5C,YAAM,WAAW,KAAK,KAAI,EAAG,KAAI,EAAG;AACpC,UAAI;AAAU,aAAK,OAAO,QAAQ;IACpC;AACA,WAAO;EACT;;;;AD7BF,IAAM,SAAS;EACb,UAAwB,IAAI,OAAwB,IAAI;;AAGnD,IAAM,WAAW,OAAO;AAWzB,SAAU,QAAK;AACnB,aAAW,SAAS,OAAO,OAAO,MAAM;AAAG,UAAM,MAAK;AACxD;;;ADbA,IAAM,eAA6B;AA0B7B,SAAUC,QACd,OACA,UAA0B,CAAA,GAAE;AAE5B,QAAM,EAAE,SAAS,KAAI,IAAK;AAE1B,MAAI,CAAC,aAAa,KAAK,KAAK;AAC1B,UAAM,IAAI,oBAAoB;MAC5B,SAAS;MACT,OAAO,IAAI,kBAAiB;KAC7B;AAEH,MAAI,QAAQ;AACV,QAAI,MAAM,YAAW,MAAO;AAAO;AACnC,QAAIC,UAAS,KAAgB,MAAM;AACjC,YAAM,IAAI,oBAAoB;QAC5B,SAAS;QACT,OAAO,IAAI,qBAAoB;OAChC;EACL;AACF;AA6BM,SAAUA,UAAS,SAAe;AACtC,MAAW,SAAS,IAAI,OAAO;AAAG,WAAc,SAAS,IAAI,OAAO;AAEpE,EAAAD,QAAO,SAAS,EAAE,QAAQ,MAAK,CAAE;AAEjC,QAAM,aAAa,QAAQ,UAAU,CAAC,EAAE,YAAW;AACnD,QAAM,OAAY,UAAgB,WAAW,UAAU,GAAG,EAAE,IAAI,QAAO,CAAE;AAEzE,QAAM,aAAa,WAAW,MAAM,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,QAAI,KAAK,KAAK,CAAC,KAAM,KAAK,KAAK,WAAW,CAAC,GAAG;AAC5C,iBAAW,CAAC,IAAI,WAAW,CAAC,EAAG,YAAW;IAC5C;AACA,SAAK,KAAK,KAAK,CAAC,IAAK,OAAS,KAAK,WAAW,IAAI,CAAC,GAAG;AACpD,iBAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAG,YAAW;IACpD;EACF;AAEA,QAAM,SAAS,KAAK,WAAW,KAAK,EAAE,CAAC;AACvC,EAAO,SAAS,IAAI,SAAS,MAAM;AACnC,SAAO;AACT;AA2CM,SAAUE,MAAK,SAAiB,UAAwB,CAAA,GAAE;AAC9D,QAAM,EAAE,UAAU,cAAc,MAAK,IAAK;AAC1C,EAAAF,QAAO,OAAO;AACd,MAAI;AAAa,WAAOC,UAAS,OAAO;AACxC,SAAO;AACT;AAoCM,SAAU,cACd,WACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,UAAe,UACnB,KAAe,MAAM,SAAS,EAAE,MAAM,CAAC,CAAC,EAAE,EAC1C,UAAU,EAAE;AACd,SAAOC,MAAK,KAAK,OAAO,IAAI,OAAO;AACrC;AA+CM,SAAU,QAAQ,UAAmB,UAAiB;AAC1D,EAAAF,QAAO,UAAU,EAAE,QAAQ,MAAK,CAAE;AAClC,EAAAA,QAAO,UAAU,EAAE,QAAQ,MAAK,CAAE;AAClC,SAAO,SAAS,YAAW,MAAO,SAAS,YAAW;AACxD;AA6BM,SAAUG,UACd,SACA,UAA4B,CAAA,GAAE;AAE9B,QAAM,EAAE,SAAS,KAAI,IAAK,WAAW,CAAA;AACrC,MAAI;AACF,IAAAH,QAAO,SAAS,EAAE,OAAM,CAAE;AAC1B,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;AAwBM,IAAO,sBAAP,cAIW,UAAgB;EAG/B,YAAY,EAAE,SAAS,MAAK,GAAqC;AAC/D,UAAM,YAAY,OAAO,iBAAiB;MACxC;KACD;AALe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,oBAAP,cAAwC,UAAS;EAGrD,cAAA;AACE,UAAM,4DAA4D;AAHlD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAO,uBAAP,cAA2C,UAAS;EAGxD,cAAA;AACE,UAAM,kDAAkD;AAHxC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;;;AG5SF,IAAM,eAAqC;EACzC,OAAO,IAAI,WAAU;EACrB,UAAU,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;EACzC,UAAU;EACV,mBAAmB,oBAAI,IAAG;EAC1B,oBAAoB;EACpB,oBAAoB,OAAO;EAC3B,kBAAe;AACb,QAAI,KAAK,sBAAsB,KAAK;AAClC,YAAM,IAAI,gCAAgC;QACxC,OAAO,KAAK,qBAAqB;QACjC,OAAO,KAAK;OACb;EACL;EACA,eAAe,UAAQ;AACrB,QAAI,WAAW,KAAK,WAAW,KAAK,MAAM,SAAS;AACjD,YAAM,IAAI,yBAAyB;QACjC,QAAQ,KAAK,MAAM;QACnB;OACD;EACL;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,aAAa,UAAQ;AACnB,WAAO,KAAK,kBAAkB,IAAI,YAAY,KAAK,QAAQ,KAAK;EAClE;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,YAAY,WAAS;AACnB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,aAAa,QAAQ,WAAS;AAC5B,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,SAAS,CAAC;AACzC,WAAO,KAAK,MAAM,SAAS,UAAU,WAAW,MAAM;EACxD;EACA,aAAa,WAAS;AACpB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,YACG,KAAK,SAAS,UAAU,QAAQ,KAAK,KACtC,KAAK,SAAS,SAAS,WAAW,CAAC;EAEvC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,SAAS,MAAmB;AAC1B,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,UAAU,OAAY;AACpB,SAAK,eAAe,KAAK,WAAW,MAAM,SAAS,CAAC;AACpD,SAAK,MAAM,IAAI,OAAO,KAAK,QAAQ;AACnC,SAAK,YAAY,MAAM;EACzB;EACA,UAAU,OAAa;AACrB,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,SAAS,CAAC;AACjD,SAAK,SAAS,SAAS,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;AAC7D,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAQ;AACN,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,YAAW;AAC9B,SAAK;AACL,WAAO;EACT;EACA,UAAU,QAAQI,OAAI;AACpB,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAa,MAAM;AACtC,SAAK,YAAYA,SAAQ;AACzB,WAAO;EACT;EACA,YAAS;AACP,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,IAAI,YAAS;AACX,WAAO,KAAK,MAAM,SAAS,KAAK;EAClC;EACA,YAAY,UAAQ;AAClB,UAAM,cAAc,KAAK;AACzB,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;AAChB,WAAO,MAAO,KAAK,WAAW;EAChC;EACA,SAAM;AACJ,QAAI,KAAK,uBAAuB,OAAO;AAAmB;AAC1D,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,kBAAkB,IAAI,KAAK,UAAU,QAAQ,CAAC;AACnD,QAAI,QAAQ;AAAG,WAAK;EACtB;;AAII,SAAU,OACd,OACA,EAAE,qBAAqB,KAAK,IAAoB,CAAA,GAAE;AAElD,QAAM,SAAiB,OAAO,OAAO,YAAY;AACjD,SAAO,QAAQ;AACf,SAAO,WAAW,IAAI,SACpB,MAAM,QACN,MAAM,YACN,MAAM,UAAU;AAElB,SAAO,oBAAoB,oBAAI,IAAG;AAClC,SAAO,qBAAqB;AAC5B,SAAO;AACT;AAUM,IAAO,sBAAP,cAA0C,UAAS;EAGvD,YAAY,EAAE,OAAM,GAAsB;AACxC,UAAM,YAAY,MAAM,wBAAwB;AAHhC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;AAII,IAAO,2BAAP,cAA+C,UAAS;EAG5D,YAAY,EAAE,QAAQ,SAAQ,GAAwC;AACpE,UACE,cAAc,QAAQ,yCAAyC,MAAM,MAAM;AAJ7D,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,kCAAP,cAAsD,UAAS;EAGnE,YAAY,EAAE,OAAO,MAAK,GAAoC;AAC5D,UACE,6BAA6B,KAAK,wCAAwC,KAAK,MAAM;AAJvE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;", "names": ["ripemd160", "sha256", "validate", "from", "ripemd160", "sha256", "validate", "from", "fromBytes", "fromHex", "validate", "from", "fromHex", "validate", "fromBytes", "x", "prefix", "assert", "checksum", "from", "validate", "size", "assert", "checksum", "from", "validate", "size"]}